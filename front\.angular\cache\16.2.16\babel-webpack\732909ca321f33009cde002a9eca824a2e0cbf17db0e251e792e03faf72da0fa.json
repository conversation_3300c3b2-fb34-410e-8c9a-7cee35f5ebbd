{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Desktop/angular/front/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { TestBed } from '@angular/core/testing';\nimport { Router } from '@angular/router';\nimport { AuthService } from '../../services/auth.service';\nimport { DashboardComponent } from './dashboard.component';\ndescribe('DashboardComponent', () => {\n  let component;\n  let fixture;\n  let authService;\n  let router;\n  const mockUserInfo = {\n    code: 'USER001',\n    name: 'Test User',\n    agency: {\n      code: 'AG001',\n      name: 'Test Agency',\n      registerCode: 'REG001'\n    },\n    office: {\n      code: 'OFF001',\n      name: 'Test Office'\n    },\n    operator: {\n      code: 'OP001',\n      name: 'Test Operator',\n      thumbnail: 'thumb.jpg'\n    },\n    market: {\n      code: 'MK001',\n      name: 'Test Market',\n      favicon: 'favicon.ico'\n    }\n  };\n  beforeEach( /*#__PURE__*/_asyncToGenerator(function* () {\n    const authServiceSpy = jasmine.createSpyObj('AuthService', ['getUserInfo', 'logout']);\n    const routerSpy = jasmine.createSpyObj('Router', ['navigate']);\n    yield TestBed.configureTestingModule({\n      declarations: [DashboardComponent],\n      providers: [{\n        provide: AuthService,\n        useValue: authServiceSpy\n      }, {\n        provide: Router,\n        useValue: routerSpy\n      }]\n    }).compileComponents();\n    fixture = TestBed.createComponent(DashboardComponent);\n    component = fixture.componentInstance;\n    authService = TestBed.inject(AuthService);\n    router = TestBed.inject(Router);\n  }));\n  beforeEach(() => {\n    authService.getUserInfo.and.returnValue(mockUserInfo);\n    fixture.detectChanges();\n  });\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n  it('should load user info on init', () => {\n    component.ngOnInit();\n    expect(authService.getUserInfo).toHaveBeenCalled();\n    expect(component.userInfo).toEqual(mockUserInfo);\n  });\n  it('should logout and redirect to login', () => {\n    component.logout();\n    expect(authService.logout).toHaveBeenCalled();\n    expect(router.navigate).toHaveBeenCalledWith(['/login']);\n  });\n});", "map": {"version": 3, "names": ["TestBed", "Router", "AuthService", "DashboardComponent", "describe", "component", "fixture", "authService", "router", "mockUserInfo", "code", "name", "agency", "registerCode", "office", "operator", "thumbnail", "market", "favicon", "beforeEach", "_asyncToGenerator", "authServiceSpy", "jasmine", "createSpyObj", "routerSpy", "configureTestingModule", "declarations", "providers", "provide", "useValue", "compileComponents", "createComponent", "componentInstance", "inject", "getUserInfo", "and", "returnValue", "detectChanges", "it", "expect", "toBeTruthy", "ngOnInit", "toHaveBeenCalled", "userInfo", "toEqual", "logout", "navigate", "toHaveBeenCalledWith"], "sources": ["C:\\Users\\<USER>\\Desktop\\angular\\front\\src\\app\\components\\dashboard\\dashboard.component.spec.ts"], "sourcesContent": ["import { ComponentFixture, TestBed } from '@angular/core/testing';\nimport { Router } from '@angular/router';\nimport { AuthService } from '../../services/auth.service';\nimport { DashboardComponent } from './dashboard.component';\n\ndescribe('DashboardComponent', () => {\n  let component: DashboardComponent;\n  let fixture: ComponentFixture<DashboardComponent>;\n  let authService: jasmine.SpyObj<AuthService>;\n  let router: jasmine.SpyObj<Router>;\n\n  const mockUserInfo = {\n    code: 'USER001',\n    name: 'Test User',\n    agency: { code: 'AG001', name: 'Test Agency', registerCode: 'REG001' },\n    office: { code: 'OFF001', name: 'Test Office' },\n    operator: { code: 'OP001', name: 'Test Operator', thumbnail: 'thumb.jpg' },\n    market: { code: 'MK001', name: 'Test Market', favicon: 'favicon.ico' }\n  };\n\n  beforeEach(async () => {\n    const authServiceSpy = jasmine.createSpyObj('AuthService', ['getUserInfo', 'logout']);\n    const routerSpy = jasmine.createSpyObj('Router', ['navigate']);\n\n    await TestBed.configureTestingModule({\n      declarations: [DashboardComponent],\n      providers: [\n        { provide: AuthService, useValue: authServiceSpy },\n        { provide: Router, useValue: routerSpy }\n      ]\n    }).compileComponents();\n\n    fixture = TestBed.createComponent(DashboardComponent);\n    component = fixture.componentInstance;\n    authService = TestBed.inject(AuthService) as jasmine.SpyObj<AuthService>;\n    router = TestBed.inject(Router) as jasmine.SpyObj<Router>;\n  });\n\n  beforeEach(() => {\n    authService.getUserInfo.and.returnValue(mockUserInfo);\n    fixture.detectChanges();\n  });\n\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n\n  it('should load user info on init', () => {\n    component.ngOnInit();\n    expect(authService.getUserInfo).toHaveBeenCalled();\n    expect(component.userInfo).toEqual(mockUserInfo);\n  });\n\n  it('should logout and redirect to login', () => {\n    component.logout();\n    expect(authService.logout).toHaveBeenCalled();\n    expect(router.navigate).toHaveBeenCalledWith(['/login']);\n  });\n});\n"], "mappings": ";AAAA,SAA2BA,OAAO,QAAQ,uBAAuB;AACjE,SAASC,MAAM,QAAQ,iBAAiB;AACxC,SAASC,WAAW,QAAQ,6BAA6B;AACzD,SAASC,kBAAkB,QAAQ,uBAAuB;AAE1DC,QAAQ,CAAC,oBAAoB,EAAE,MAAK;EAClC,IAAIC,SAA6B;EACjC,IAAIC,OAA6C;EACjD,IAAIC,WAAwC;EAC5C,IAAIC,MAA8B;EAElC,MAAMC,YAAY,GAAG;IACnBC,IAAI,EAAE,SAAS;IACfC,IAAI,EAAE,WAAW;IACjBC,MAAM,EAAE;MAAEF,IAAI,EAAE,OAAO;MAAEC,IAAI,EAAE,aAAa;MAAEE,YAAY,EAAE;IAAQ,CAAE;IACtEC,MAAM,EAAE;MAAEJ,IAAI,EAAE,QAAQ;MAAEC,IAAI,EAAE;IAAa,CAAE;IAC/CI,QAAQ,EAAE;MAAEL,IAAI,EAAE,OAAO;MAAEC,IAAI,EAAE,eAAe;MAAEK,SAAS,EAAE;IAAW,CAAE;IAC1EC,MAAM,EAAE;MAAEP,IAAI,EAAE,OAAO;MAAEC,IAAI,EAAE,aAAa;MAAEO,OAAO,EAAE;IAAa;GACrE;EAEDC,UAAU,eAAAC,iBAAA,CAAC,aAAW;IACpB,MAAMC,cAAc,GAAGC,OAAO,CAACC,YAAY,CAAC,aAAa,EAAE,CAAC,aAAa,EAAE,QAAQ,CAAC,CAAC;IACrF,MAAMC,SAAS,GAAGF,OAAO,CAACC,YAAY,CAAC,QAAQ,EAAE,CAAC,UAAU,CAAC,CAAC;IAE9D,MAAMvB,OAAO,CAACyB,sBAAsB,CAAC;MACnCC,YAAY,EAAE,CAACvB,kBAAkB,CAAC;MAClCwB,SAAS,EAAE,CACT;QAAEC,OAAO,EAAE1B,WAAW;QAAE2B,QAAQ,EAAER;MAAc,CAAE,EAClD;QAAEO,OAAO,EAAE3B,MAAM;QAAE4B,QAAQ,EAAEL;MAAS,CAAE;KAE3C,CAAC,CAACM,iBAAiB,EAAE;IAEtBxB,OAAO,GAAGN,OAAO,CAAC+B,eAAe,CAAC5B,kBAAkB,CAAC;IACrDE,SAAS,GAAGC,OAAO,CAAC0B,iBAAiB;IACrCzB,WAAW,GAAGP,OAAO,CAACiC,MAAM,CAAC/B,WAAW,CAAgC;IACxEM,MAAM,GAAGR,OAAO,CAACiC,MAAM,CAAChC,MAAM,CAA2B;EAC3D,CAAC,EAAC;EAEFkB,UAAU,CAAC,MAAK;IACdZ,WAAW,CAAC2B,WAAW,CAACC,GAAG,CAACC,WAAW,CAAC3B,YAAY,CAAC;IACrDH,OAAO,CAAC+B,aAAa,EAAE;EACzB,CAAC,CAAC;EAEFC,EAAE,CAAC,eAAe,EAAE,MAAK;IACvBC,MAAM,CAAClC,SAAS,CAAC,CAACmC,UAAU,EAAE;EAChC,CAAC,CAAC;EAEFF,EAAE,CAAC,+BAA+B,EAAE,MAAK;IACvCjC,SAAS,CAACoC,QAAQ,EAAE;IACpBF,MAAM,CAAChC,WAAW,CAAC2B,WAAW,CAAC,CAACQ,gBAAgB,EAAE;IAClDH,MAAM,CAAClC,SAAS,CAACsC,QAAQ,CAAC,CAACC,OAAO,CAACnC,YAAY,CAAC;EAClD,CAAC,CAAC;EAEF6B,EAAE,CAAC,qCAAqC,EAAE,MAAK;IAC7CjC,SAAS,CAACwC,MAAM,EAAE;IAClBN,MAAM,CAAChC,WAAW,CAACsC,MAAM,CAAC,CAACH,gBAAgB,EAAE;IAC7CH,MAAM,CAAC/B,MAAM,CAACsC,QAAQ,CAAC,CAACC,oBAAoB,CAAC,CAAC,QAAQ,CAAC,CAAC;EAC1D,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}