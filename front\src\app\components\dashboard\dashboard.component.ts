import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { AuthService } from '../../services/auth.service';

@Component({
  selector: 'app-dashboard',
  templateUrl: './dashboard.component.html',
  styles: [`
    /* Variables CSS pour Block to Book */
    :host {
      --primary-blue: #4a90e2;
      --secondary-blue: #7bb3f0;
      --dark-blue: #2c5aa0;
      --light-gray: #f5f7fa;
      --medium-gray: #8fa4b3;
      --dark-gray: #4a5568;
      --white: #ffffff;
      --border-color: #e2e8f0;
      --shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      --shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.15);
      --transition: all 0.3s ease;
    }

    /* Container principal */
    .dashboard-container {
      min-height: 100vh;
      background: var(--light-gray);
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
      display: flex;
      flex-direction: column;
    }

    /* Header */
    .dashboard-header {
      background: var(--white);
      border-bottom: 1px solid var(--border-color);
      box-shadow: var(--shadow);
    }

    .header-content {
      max-width: 1400px;
      margin: 0 auto;
      padding: 1rem 2rem;
      display: flex;
      justify-content: space-between;
      align-items: center;
      flex-wrap: wrap;
      gap: 1rem;
    }

    /* Logo Section */
    .logo-section {
      display: flex;
      align-items: center;
      gap: 2rem;
    }

    .logo {
      display: flex;
      align-items: center;
      gap: 0.75rem;
    }

    .logo-icon {
      width: 2rem;
      height: 2rem;
      color: var(--primary-blue);
    }

    .logo-text {
      font-size: 1.25rem;
      font-weight: 700;
      color: var(--dark-gray);
      letter-spacing: -0.025em;
    }

    .demo-info {
      display: flex;
      flex-direction: column;
      gap: 0.25rem;
    }

    .demo-id {
      font-size: 0.875rem;
      font-weight: 600;
      color: var(--dark-gray);
    }

    .demo-details {
      font-size: 0.75rem;
      color: var(--medium-gray);
    }

    /* Header Contact */
    .header-contact {
      display: flex;
      gap: 2rem;
      flex-wrap: wrap;
    }

    .contact-item {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      font-size: 0.875rem;
      color: var(--dark-gray);
    }

    .contact-icon {
      width: 1rem;
      height: 1rem;
      color: var(--medium-gray);
    }

    /* Header Navigation */
    .header-nav {
      display: flex;
      align-items: center;
      gap: 1.5rem;
    }

    .nav-item {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      padding: 0.5rem 1rem;
      border-radius: 0.5rem;
      text-decoration: none;
      color: var(--dark-gray);
      font-size: 0.875rem;
      font-weight: 500;
      transition: var(--transition);
    }

    .nav-item:hover {
      background: var(--light-gray);
      color: var(--primary-blue);
    }

    .nav-item.active {
      background: var(--primary-blue);
      color: var(--white);
    }

    .nav-icon {
      width: 1.25rem;
      height: 1.25rem;
    }

    .logout-btn {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      padding: 0.5rem 1rem;
      background: transparent;
      border: 1px solid var(--border-color);
      border-radius: 0.5rem;
      color: var(--dark-gray);
      font-size: 0.875rem;
      font-weight: 500;
      cursor: pointer;
      transition: var(--transition);
    }

    .logout-btn:hover {
      background: #fee2e2;
      border-color: #fca5a5;
      color: #dc2626;
    }

    /* Main Dashboard */
    .dashboard-main {
      flex: 1;
      padding: 2rem;
    }

    .dashboard-grid {
      max-width: 1400px;
      margin: 0 auto;
      display: grid;
      grid-template-columns: 280px 1fr;
      gap: 2rem;
      height: calc(100vh - 120px);
    }

    /* Sidebar */
    .sidebar {
      background: var(--white);
      border-radius: 0.75rem;
      padding: 1.5rem;
      box-shadow: var(--shadow);
      height: fit-content;
    }

    .sidebar-section {
      display: flex;
      flex-direction: column;
      gap: 0.5rem;
      margin-bottom: 2rem;
    }

    .sidebar-section:last-child {
      margin-bottom: 0;
    }

    .sidebar-item {
      display: flex;
      align-items: center;
      gap: 0.75rem;
      padding: 0.875rem 1rem;
      border-radius: 0.5rem;
      color: var(--dark-gray);
      font-size: 0.875rem;
      font-weight: 500;
      cursor: pointer;
      transition: var(--transition);
    }

    .sidebar-item:hover {
      background: var(--light-gray);
      color: var(--primary-blue);
    }

    .sidebar-icon {
      width: 1.25rem;
      height: 1.25rem;
      color: var(--medium-gray);
      transition: var(--transition);
    }

    .sidebar-item:hover .sidebar-icon {
      color: var(--primary-blue);
    }

    /* Main Content */
    .main-content {
      background: var(--white);
      border-radius: 0.75rem;
      padding: 2rem;
      box-shadow: var(--shadow);
      overflow: hidden;
    }

    .content-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 2rem;
      height: 100%;
    }

    /* Service Cards */
    .service-card {
      background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
      border-radius: 1rem;
      padding: 2rem;
      color: var(--white);
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      text-align: center;
      cursor: pointer;
      transition: var(--transition);
      min-height: 200px;
      position: relative;
      overflow: hidden;
    }

    .service-card::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(45deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
      opacity: 0;
      transition: var(--transition);
    }

    .service-card:hover::before {
      opacity: 1;
    }

    .service-card:hover {
      transform: translateY(-4px);
      box-shadow: var(--shadow-lg);
    }

    .card-icon {
      width: 4rem;
      height: 4rem;
      margin-bottom: 1rem;
      background: rgba(255, 255, 255, 0.2);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .card-icon svg {
      width: 2rem;
      height: 2rem;
      color: var(--white);
    }

    .service-card h3 {
      font-size: 1.5rem;
      font-weight: 700;
      margin: 0;
      letter-spacing: -0.025em;
    }

    /* Responsive Design */
    @media (max-width: 1200px) {
      .dashboard-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
      }

      .sidebar {
        order: 2;
      }

      .main-content {
        order: 1;
      }
    }

    @media (max-width: 768px) {
      .header-content {
        flex-direction: column;
        align-items: flex-start;
        gap: 1.5rem;
      }

      .logo-section {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
      }

      .header-contact {
        flex-direction: column;
        gap: 0.75rem;
      }

      .header-nav {
        flex-wrap: wrap;
        gap: 1rem;
      }

      .dashboard-main {
        padding: 1rem;
      }

      .dashboard-grid {
        gap: 1rem;
      }

      .sidebar {
        padding: 1rem;
      }

      .main-content {
        padding: 1.5rem;
      }

      .content-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
      }
    }
  `]
})
export class DashboardComponent implements OnInit {
  userInfo: any;

  constructor(
    private authService: AuthService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.userInfo = this.authService.getUserInfo();
  }

  logout(): void {
    this.authService.logout();
    this.router.navigate(['/login']);
  }

  navigateToFlightSearch(): void {
    this.router.navigate(['/search-flights']);
  }
}
