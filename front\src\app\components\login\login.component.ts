import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';

import { AuthService } from '../../services/auth.service';
import { AuthRequest } from '../../models/auth-request.interface';

@Component({
  selector: 'app-login',
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.css']
})
export class LoginComponent implements OnInit {
  loginForm!: FormGroup;
  isLoading = false;
  errorMessage = '';
  showPassword = false;

  constructor(
    private formBuilder: FormBuilder,
    private authService: AuthService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.initializeForm();
    
    // Rediriger si déjà connecté
    if (this.authService.isAuthenticated()) {
      this.router.navigate(['/dashboard']); // Ajustez selon votre route de destination
    }
  }

  /**
   * Initialise le formulaire de connexion avec les validations
   */
  private initializeForm(): void {
    this.loginForm = this.formBuilder.group({
      agency: ['', [Validators.required, Validators.minLength(2)]],
      user: ['', [Validators.required, Validators.minLength(2)]],
      password: ['', [Validators.required, Validators.minLength(6)]]
    });
  }

  /**
   * Getter pour accéder facilement aux contrôles du formulaire
   */
  get formControls() {
    return this.loginForm.controls;
  }

  /**
   * Bascule la visibilité du mot de passe
   */
  togglePasswordVisibility(): void {
    this.showPassword = !this.showPassword;
  }

  /**
   * Soumet le formulaire de connexion
   */
  onSubmit(): void {
    if (this.loginForm.valid && !this.isLoading) {
      this.isLoading = true;
      this.errorMessage = '';

      // Créer l'objet de requête avec les noms de propriétés exacts du backend
      const authRequest: AuthRequest = {
        Agency: this.loginForm.value.agency.trim(),
        User: this.loginForm.value.user.trim(),
        Password: this.loginForm.value.password
      };

      this.authService.authenticate(authRequest).subscribe({
        next: (response) => {
          this.isLoading = false;
          
          if (response.header.success) {
            // Connexion réussie
            console.log('Connexion réussie:', response.body.userInfo);
            
            // Rediriger vers le dashboard ou la page d'accueil
            this.router.navigate(['/dashboard']); // Ajustez selon votre route
          } else {
            // Erreur retournée par l'API
            this.handleApiError(response);
          }
        },
        error: (error) => {
          this.isLoading = false;
          this.errorMessage = error.message || 'Une erreur est survenue lors de la connexion';
          console.error('Erreur de connexion:', error);
        }
      });
    } else {
      // Marquer tous les champs comme touchés pour afficher les erreurs
      this.markFormGroupTouched();
    }
  }

  /**
   * Gère les erreurs retournées par l'API
   */
  private handleApiError(response: any): void {
    if (response.header.messages && response.header.messages.length > 0) {
      // Utiliser le premier message d'erreur de l'API
      this.errorMessage = response.header.messages[0].message;
    } else {
      this.errorMessage = 'Échec de l\'authentification';
    }
  }

  /**
   * Marque tous les champs du formulaire comme touchés
   */
  private markFormGroupTouched(): void {
    Object.keys(this.loginForm.controls).forEach(key => {
      const control = this.loginForm.get(key);
      control?.markAsTouched();
    });
  }

  /**
   * Vérifie si un champ a une erreur et a été touché
   */
  hasError(fieldName: string, errorType: string): boolean {
    const field = this.loginForm.get(fieldName);
    return !!(field?.hasError(errorType) && field?.touched);
  }

  /**
   * Récupère le message d'erreur pour un champ
   */
  getErrorMessage(fieldName: string): string {
    const field = this.loginForm.get(fieldName);
    
    if (field?.hasError('required')) {
      return `${this.getFieldDisplayName(fieldName)} est requis`;
    }
    
    if (field?.hasError('minlength')) {
      const requiredLength = field.errors?.['minlength']?.requiredLength;
      return `${this.getFieldDisplayName(fieldName)} doit contenir au moins ${requiredLength} caractères`;
    }
    
    return '';
  }

  /**
   * Retourne le nom d'affichage pour un champ
   */
  private getFieldDisplayName(fieldName: string): string {
    const displayNames: { [key: string]: string } = {
      agency: 'Le code agence',
      user: 'Le code utilisateur',
      password: 'Le mot de passe'
    };
    
    return displayNames[fieldName] || fieldName;
  }

  /**
   * Nettoie le message d'erreur
   */
  clearError(): void {
    this.errorMessage = '';
  }
}
