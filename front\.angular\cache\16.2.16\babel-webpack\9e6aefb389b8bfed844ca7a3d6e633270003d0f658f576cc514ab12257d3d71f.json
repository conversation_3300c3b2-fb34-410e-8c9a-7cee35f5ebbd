{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/auth.service\";\nexport class HttpRequestInterceptor {\n  constructor(authService) {\n    this.authService = authService;\n  }\n  intercept(req, next) {\n    // Ajouter les headers CORS et d'authentification\n    let modifiedReq = req.clone({\n      setHeaders: {\n        'Content-Type': 'application/json',\n        'Accept': 'application/json',\n        'Access-Control-Allow-Origin': '*',\n        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',\n        'Access-Control-Allow-Headers': 'Content-Type, Authorization'\n      }\n    });\n    // Ajouter le token d'authentification si disponible\n    const token = this.authService.getToken();\n    if (token) {\n      modifiedReq = modifiedReq.clone({\n        setHeaders: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n    }\n    return next.handle(modifiedReq);\n  }\n  static {\n    this.ɵfac = function HttpRequestInterceptor_Factory(t) {\n      return new (t || HttpRequestInterceptor)(i0.ɵɵinject(i1.AuthService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: HttpRequestInterceptor,\n      factory: HttpRequestInterceptor.ɵfac\n    });\n  }\n}", "map": {"version": 3, "names": ["HttpRequestInterceptor", "constructor", "authService", "intercept", "req", "next", "modifiedReq", "clone", "setHeaders", "token", "getToken", "handle", "i0", "ɵɵinject", "i1", "AuthService", "factory", "ɵfac"], "sources": ["C:\\Users\\<USER>\\Desktop\\angular\\front\\src\\app\\interceptors\\http.interceptor.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpInterceptor, HttpRequest, HttpHandler, HttpEvent } from '@angular/common/http';\nimport { Observable } from 'rxjs';\nimport { AuthService } from '../services/auth.service';\n\n@Injectable()\nexport class HttpRequestInterceptor implements HttpInterceptor {\n\n  constructor(private authService: AuthService) {}\n\n  intercept(req: HttpRequest<any>, next: <PERSON>ttp<PERSON>and<PERSON>): Observable<HttpEvent<any>> {\n    // Ajouter les headers CORS et d'authentification\n    let modifiedReq = req.clone({\n      setHeaders: {\n        'Content-Type': 'application/json',\n        'Accept': 'application/json',\n        'Access-Control-Allow-Origin': '*',\n        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',\n        'Access-Control-Allow-Headers': 'Content-Type, Authorization'\n      }\n    });\n\n    // Ajouter le token d'authentification si disponible\n    const token = this.authService.getToken();\n    if (token) {\n      modifiedReq = modifiedReq.clone({\n        setHeaders: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n    }\n\n    return next.handle(modifiedReq);\n  }\n}\n"], "mappings": ";;AAMA,OAAM,MAAOA,sBAAsB;EAEjCC,YAAoBC,WAAwB;IAAxB,KAAAA,WAAW,GAAXA,WAAW;EAAgB;EAE/CC,SAASA,CAACC,GAAqB,EAAEC,IAAiB;IAChD;IACA,IAAIC,WAAW,GAAGF,GAAG,CAACG,KAAK,CAAC;MAC1BC,UAAU,EAAE;QACV,cAAc,EAAE,kBAAkB;QAClC,QAAQ,EAAE,kBAAkB;QAC5B,6BAA6B,EAAE,GAAG;QAClC,8BAA8B,EAAE,iCAAiC;QACjE,8BAA8B,EAAE;;KAEnC,CAAC;IAEF;IACA,MAAMC,KAAK,GAAG,IAAI,CAACP,WAAW,CAACQ,QAAQ,EAAE;IACzC,IAAID,KAAK,EAAE;MACTH,WAAW,GAAGA,WAAW,CAACC,KAAK,CAAC;QAC9BC,UAAU,EAAE;UACV,eAAe,EAAE,UAAUC,KAAK;;OAEnC,CAAC;;IAGJ,OAAOJ,IAAI,CAACM,MAAM,CAACL,WAAW,CAAC;EACjC;;;uBA3BWN,sBAAsB,EAAAY,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,WAAA;IAAA;EAAA;;;aAAtBf,sBAAsB;MAAAgB,OAAA,EAAtBhB,sBAAsB,CAAAiB;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}