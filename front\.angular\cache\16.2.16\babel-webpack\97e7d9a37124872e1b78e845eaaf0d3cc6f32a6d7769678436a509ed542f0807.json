{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Injectable } from '@angular/core';\nimport { HttpClient, HttpHeaders } from '@angular/common/http';\nimport { throwError } from 'rxjs';\nimport { catchError, map } from 'rxjs/operators';\nimport { environment } from '../../environments/environment';\nexport let AuthService = class AuthService {\n  constructor(http) {\n    this.http = http;\n    this.baseUrl = environment.apiUrl;\n    this.authEndpoint = environment.authEndpoint;\n  }\n  /**\n   * Authentifie un utilisateur avec les informations de connexion\n   * @param authRequest Les informations de connexion\n   * @returns Observable<AuthResponse>\n   */\n  authenticate(authRequest) {\n    const headers = new HttpHeaders({\n      'Content-Type': 'application/json',\n      'Accept': 'application/json'\n    });\n    return this.http.post(`${this.baseUrl}${this.authEndpoint}`, authRequest, {\n      headers\n    }).pipe(map(response => {\n      // Stocker le token si l'authentification réussit\n      if (response.header.success && response.body?.token) {\n        this.setToken(response.body.token);\n        this.setUserInfo(response.body.userInfo);\n        this.setTokenExpiration(response.body.expiresOn);\n      }\n      return response;\n    }), catchError(this.handleError));\n  }\n  /**\n   * Stocke le token d'authentification\n   * @param token Le token JWT\n   */\n  setToken(token) {\n    localStorage.setItem('auth_token', token);\n  }\n  /**\n   * Récupère le token d'authentification\n   * @returns Le token ou null\n   */\n  getToken() {\n    return localStorage.getItem('auth_token');\n  }\n  /**\n   * Stocke la date d'expiration du token\n   * @param expiresOn Date d'expiration\n   */\n  setTokenExpiration(expiresOn) {\n    localStorage.setItem('token_expires_on', expiresOn);\n  }\n  /**\n   * Récupère la date d'expiration du token\n   * @returns Date d'expiration ou null\n   */\n  getTokenExpiration() {\n    return localStorage.getItem('token_expires_on');\n  }\n  /**\n   * Stocke les informations utilisateur\n   * @param userInfo Les informations utilisateur\n   */\n  setUserInfo(userInfo) {\n    localStorage.setItem('user_info', JSON.stringify(userInfo));\n  }\n  /**\n   * Récupère les informations utilisateur\n   * @returns Les informations utilisateur ou null\n   */\n  getUserInfo() {\n    const userInfo = localStorage.getItem('user_info');\n    return userInfo ? JSON.parse(userInfo) : null;\n  }\n  /**\n   * Vérifie si l'utilisateur est connecté et si le token n'est pas expiré\n   * @returns true si connecté et token valide, false sinon\n   */\n  isAuthenticated() {\n    const token = this.getToken();\n    const expiresOn = this.getTokenExpiration();\n    if (!token || !expiresOn) {\n      return false;\n    }\n    // Vérifier si le token n'est pas expiré\n    const expirationDate = new Date(expiresOn);\n    const now = new Date();\n    if (now >= expirationDate) {\n      this.logout(); // Nettoyer les données expirées\n      return false;\n    }\n    return true;\n  }\n  /**\n   * Déconnecte l'utilisateur\n   */\n  logout() {\n    localStorage.removeItem('auth_token');\n    localStorage.removeItem('user_info');\n    localStorage.removeItem('token_expires_on');\n  }\n  /**\n   * Gère les erreurs HTTP\n   * @param error L'erreur HTTP\n   * @returns Observable avec l'erreur\n   */\n  handleError(error) {\n    let errorMessage = 'Une erreur est survenue lors de l\\'authentification';\n    if (error.error instanceof ErrorEvent) {\n      // Erreur côté client\n      errorMessage = `Erreur: ${error.error.message}`;\n    } else {\n      // Erreur côté serveur\n      switch (error.status) {\n        case 401:\n          errorMessage = 'Identifiants incorrects';\n          break;\n        case 403:\n          errorMessage = 'Accès refusé';\n          break;\n        case 404:\n          errorMessage = 'Service d\\'authentification non trouvé';\n          break;\n        case 500:\n          errorMessage = 'Erreur interne du serveur';\n          break;\n        case 0:\n          errorMessage = 'Impossible de contacter le serveur. Vérifiez votre connexion.';\n          break;\n        default:\n          errorMessage = `Erreur ${error.status}: ${error.message}`;\n      }\n      // Si l'erreur contient un message du backend, l'utiliser\n      if (error.error && typeof error.error === 'object' && error.error.message) {\n        errorMessage = error.error.message;\n      }\n    }\n    console.error('Erreur d\\'authentification:', error);\n    return throwError(() => new Error(errorMessage));\n  }\n  /**\n   * Obtient les headers d'autorisation pour les requêtes authentifiées\n   * @returns HttpHeaders avec le token d'autorisation\n   */\n  getAuthHeaders() {\n    const token = this.getToken();\n    if (token) {\n      return new HttpHeaders({\n        'Authorization': `Bearer ${token}`,\n        'Content-Type': 'application/json'\n      });\n    }\n    return new HttpHeaders({\n      'Content-Type': 'application/json'\n    });\n  }\n  static {\n    this.ctorParameters = () => [{\n      type: HttpClient\n    }];\n  }\n};\nAuthService = __decorate([Injectable({\n  providedIn: 'root'\n})], AuthService);", "map": {"version": 3, "names": ["Injectable", "HttpClient", "HttpHeaders", "throwError", "catchError", "map", "environment", "AuthService", "constructor", "http", "baseUrl", "apiUrl", "authEndpoint", "authenticate", "authRequest", "headers", "post", "pipe", "response", "header", "success", "body", "token", "setToken", "setUserInfo", "userInfo", "setTokenExpiration", "expiresOn", "handleError", "localStorage", "setItem", "getToken", "getItem", "getTokenExpiration", "JSON", "stringify", "getUserInfo", "parse", "isAuthenticated", "expirationDate", "Date", "now", "logout", "removeItem", "error", "errorMessage", "ErrorEvent", "message", "status", "console", "Error", "getAuthHeaders", "__decorate", "providedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\angular\\front\\src\\app\\services\\auth.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient, HttpErrorResponse, HttpHeaders } from '@angular/common/http';\nimport { Observable, throwError } from 'rxjs';\nimport { catchError, map } from 'rxjs/operators';\n\nimport { AuthRequest } from '../models/auth-request.interface';\nimport { AuthResponse } from '../models/auth-response.interface';\nimport { environment } from '../../environments/environment';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class AuthService {\n  private readonly baseUrl = environment.apiUrl;\n  private readonly authEndpoint = environment.authEndpoint;\n\n  constructor(private http: HttpClient) {}\n\n  /**\n   * Authentifie un utilisateur avec les informations de connexion\n   * @param authRequest Les informations de connexion\n   * @returns Observable<AuthResponse>\n   */\n  authenticate(authRequest: AuthRequest): Observable<AuthResponse> {\n    const headers = new HttpHeaders({\n      'Content-Type': 'application/json',\n      'Accept': 'application/json'\n    });\n\n    return this.http.post<AuthResponse>(`${this.baseUrl}${this.authEndpoint}`, authRequest, { headers })\n      .pipe(\n        map(response => {\n          // Stocker le token si l'authentification réussit\n          if (response.header.success && response.body?.token) {\n            this.setToken(response.body.token);\n            this.setUserInfo(response.body.userInfo);\n            this.setTokenExpiration(response.body.expiresOn);\n          }\n          return response;\n        }),\n        catchError(this.handleError)\n      );\n  }\n\n  /**\n   * Stocke le token d'authentification\n   * @param token Le token JWT\n   */\n  private setToken(token: string): void {\n    localStorage.setItem('auth_token', token);\n  }\n\n  /**\n   * Récupère le token d'authentification\n   * @returns Le token ou null\n   */\n  getToken(): string | null {\n    return localStorage.getItem('auth_token');\n  }\n\n  /**\n   * Stocke la date d'expiration du token\n   * @param expiresOn Date d'expiration\n   */\n  private setTokenExpiration(expiresOn: string): void {\n    localStorage.setItem('token_expires_on', expiresOn);\n  }\n\n  /**\n   * Récupère la date d'expiration du token\n   * @returns Date d'expiration ou null\n   */\n  getTokenExpiration(): string | null {\n    return localStorage.getItem('token_expires_on');\n  }\n\n  /**\n   * Stocke les informations utilisateur\n   * @param userInfo Les informations utilisateur\n   */\n  private setUserInfo(userInfo: any): void {\n    localStorage.setItem('user_info', JSON.stringify(userInfo));\n  }\n\n  /**\n   * Récupère les informations utilisateur\n   * @returns Les informations utilisateur ou null\n   */\n  getUserInfo(): any {\n    const userInfo = localStorage.getItem('user_info');\n    return userInfo ? JSON.parse(userInfo) : null;\n  }\n\n  /**\n   * Vérifie si l'utilisateur est connecté et si le token n'est pas expiré\n   * @returns true si connecté et token valide, false sinon\n   */\n  isAuthenticated(): boolean {\n    const token = this.getToken();\n    const expiresOn = this.getTokenExpiration();\n    \n    if (!token || !expiresOn) {\n      return false;\n    }\n\n    // Vérifier si le token n'est pas expiré\n    const expirationDate = new Date(expiresOn);\n    const now = new Date();\n    \n    if (now >= expirationDate) {\n      this.logout(); // Nettoyer les données expirées\n      return false;\n    }\n\n    return true;\n  }\n\n  /**\n   * Déconnecte l'utilisateur\n   */\n  logout(): void {\n    localStorage.removeItem('auth_token');\n    localStorage.removeItem('user_info');\n    localStorage.removeItem('token_expires_on');\n  }\n\n  /**\n   * Gère les erreurs HTTP\n   * @param error L'erreur HTTP\n   * @returns Observable avec l'erreur\n   */\n  private handleError(error: HttpErrorResponse): Observable<never> {\n    let errorMessage = 'Une erreur est survenue lors de l\\'authentification';\n    \n    if (error.error instanceof ErrorEvent) {\n      // Erreur côté client\n      errorMessage = `Erreur: ${error.error.message}`;\n    } else {\n      // Erreur côté serveur\n      switch (error.status) {\n        case 401:\n          errorMessage = 'Identifiants incorrects';\n          break;\n        case 403:\n          errorMessage = 'Accès refusé';\n          break;\n        case 404:\n          errorMessage = 'Service d\\'authentification non trouvé';\n          break;\n        case 500:\n          errorMessage = 'Erreur interne du serveur';\n          break;\n        case 0:\n          errorMessage = 'Impossible de contacter le serveur. Vérifiez votre connexion.';\n          break;\n        default:\n          errorMessage = `Erreur ${error.status}: ${error.message}`;\n      }\n      \n      // Si l'erreur contient un message du backend, l'utiliser\n      if (error.error && typeof error.error === 'object' && error.error.message) {\n        errorMessage = error.error.message;\n      }\n    }\n    \n    console.error('Erreur d\\'authentification:', error);\n    return throwError(() => new Error(errorMessage));\n  }\n\n  /**\n   * Obtient les headers d'autorisation pour les requêtes authentifiées\n   * @returns HttpHeaders avec le token d'autorisation\n   */\n  getAuthHeaders(): HttpHeaders {\n    const token = this.getToken();\n    if (token) {\n      return new HttpHeaders({\n        'Authorization': `Bearer ${token}`,\n        'Content-Type': 'application/json'\n      });\n    }\n    return new HttpHeaders({\n      'Content-Type': 'application/json'\n    });\n  }\n}\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,eAAe;AAC1C,SAASC,UAAU,EAAqBC,WAAW,QAAQ,sBAAsB;AACjF,SAAqBC,UAAU,QAAQ,MAAM;AAC7C,SAASC,UAAU,EAAEC,GAAG,QAAQ,gBAAgB;AAIhD,SAASC,WAAW,QAAQ,gCAAgC;AAKrD,WAAMC,WAAW,GAAjB,MAAMA,WAAW;EAItBC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAHP,KAAAC,OAAO,GAAGJ,WAAW,CAACK,MAAM;IAC5B,KAAAC,YAAY,GAAGN,WAAW,CAACM,YAAY;EAEjB;EAEvC;;;;;EAKAC,YAAYA,CAACC,WAAwB;IACnC,MAAMC,OAAO,GAAG,IAAIb,WAAW,CAAC;MAC9B,cAAc,EAAE,kBAAkB;MAClC,QAAQ,EAAE;KACX,CAAC;IAEF,OAAO,IAAI,CAACO,IAAI,CAACO,IAAI,CAAe,GAAG,IAAI,CAACN,OAAO,GAAG,IAAI,CAACE,YAAY,EAAE,EAAEE,WAAW,EAAE;MAAEC;IAAO,CAAE,CAAC,CACjGE,IAAI,CACHZ,GAAG,CAACa,QAAQ,IAAG;MACb;MACA,IAAIA,QAAQ,CAACC,MAAM,CAACC,OAAO,IAAIF,QAAQ,CAACG,IAAI,EAAEC,KAAK,EAAE;QACnD,IAAI,CAACC,QAAQ,CAACL,QAAQ,CAACG,IAAI,CAACC,KAAK,CAAC;QAClC,IAAI,CAACE,WAAW,CAACN,QAAQ,CAACG,IAAI,CAACI,QAAQ,CAAC;QACxC,IAAI,CAACC,kBAAkB,CAACR,QAAQ,CAACG,IAAI,CAACM,SAAS,CAAC;;MAElD,OAAOT,QAAQ;IACjB,CAAC,CAAC,EACFd,UAAU,CAAC,IAAI,CAACwB,WAAW,CAAC,CAC7B;EACL;EAEA;;;;EAIQL,QAAQA,CAACD,KAAa;IAC5BO,YAAY,CAACC,OAAO,CAAC,YAAY,EAAER,KAAK,CAAC;EAC3C;EAEA;;;;EAIAS,QAAQA,CAAA;IACN,OAAOF,YAAY,CAACG,OAAO,CAAC,YAAY,CAAC;EAC3C;EAEA;;;;EAIQN,kBAAkBA,CAACC,SAAiB;IAC1CE,YAAY,CAACC,OAAO,CAAC,kBAAkB,EAAEH,SAAS,CAAC;EACrD;EAEA;;;;EAIAM,kBAAkBA,CAAA;IAChB,OAAOJ,YAAY,CAACG,OAAO,CAAC,kBAAkB,CAAC;EACjD;EAEA;;;;EAIQR,WAAWA,CAACC,QAAa;IAC/BI,YAAY,CAACC,OAAO,CAAC,WAAW,EAAEI,IAAI,CAACC,SAAS,CAACV,QAAQ,CAAC,CAAC;EAC7D;EAEA;;;;EAIAW,WAAWA,CAAA;IACT,MAAMX,QAAQ,GAAGI,YAAY,CAACG,OAAO,CAAC,WAAW,CAAC;IAClD,OAAOP,QAAQ,GAAGS,IAAI,CAACG,KAAK,CAACZ,QAAQ,CAAC,GAAG,IAAI;EAC/C;EAEA;;;;EAIAa,eAAeA,CAAA;IACb,MAAMhB,KAAK,GAAG,IAAI,CAACS,QAAQ,EAAE;IAC7B,MAAMJ,SAAS,GAAG,IAAI,CAACM,kBAAkB,EAAE;IAE3C,IAAI,CAACX,KAAK,IAAI,CAACK,SAAS,EAAE;MACxB,OAAO,KAAK;;IAGd;IACA,MAAMY,cAAc,GAAG,IAAIC,IAAI,CAACb,SAAS,CAAC;IAC1C,MAAMc,GAAG,GAAG,IAAID,IAAI,EAAE;IAEtB,IAAIC,GAAG,IAAIF,cAAc,EAAE;MACzB,IAAI,CAACG,MAAM,EAAE,CAAC,CAAC;MACf,OAAO,KAAK;;IAGd,OAAO,IAAI;EACb;EAEA;;;EAGAA,MAAMA,CAAA;IACJb,YAAY,CAACc,UAAU,CAAC,YAAY,CAAC;IACrCd,YAAY,CAACc,UAAU,CAAC,WAAW,CAAC;IACpCd,YAAY,CAACc,UAAU,CAAC,kBAAkB,CAAC;EAC7C;EAEA;;;;;EAKQf,WAAWA,CAACgB,KAAwB;IAC1C,IAAIC,YAAY,GAAG,qDAAqD;IAExE,IAAID,KAAK,CAACA,KAAK,YAAYE,UAAU,EAAE;MACrC;MACAD,YAAY,GAAG,WAAWD,KAAK,CAACA,KAAK,CAACG,OAAO,EAAE;KAChD,MAAM;MACL;MACA,QAAQH,KAAK,CAACI,MAAM;QAClB,KAAK,GAAG;UACNH,YAAY,GAAG,yBAAyB;UACxC;QACF,KAAK,GAAG;UACNA,YAAY,GAAG,cAAc;UAC7B;QACF,KAAK,GAAG;UACNA,YAAY,GAAG,wCAAwC;UACvD;QACF,KAAK,GAAG;UACNA,YAAY,GAAG,2BAA2B;UAC1C;QACF,KAAK,CAAC;UACJA,YAAY,GAAG,+DAA+D;UAC9E;QACF;UACEA,YAAY,GAAG,UAAUD,KAAK,CAACI,MAAM,KAAKJ,KAAK,CAACG,OAAO,EAAE;;MAG7D;MACA,IAAIH,KAAK,CAACA,KAAK,IAAI,OAAOA,KAAK,CAACA,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAACA,KAAK,CAACG,OAAO,EAAE;QACzEF,YAAY,GAAGD,KAAK,CAACA,KAAK,CAACG,OAAO;;;IAItCE,OAAO,CAACL,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;IACnD,OAAOzC,UAAU,CAAC,MAAM,IAAI+C,KAAK,CAACL,YAAY,CAAC,CAAC;EAClD;EAEA;;;;EAIAM,cAAcA,CAAA;IACZ,MAAM7B,KAAK,GAAG,IAAI,CAACS,QAAQ,EAAE;IAC7B,IAAIT,KAAK,EAAE;MACT,OAAO,IAAIpB,WAAW,CAAC;QACrB,eAAe,EAAE,UAAUoB,KAAK,EAAE;QAClC,cAAc,EAAE;OACjB,CAAC;;IAEJ,OAAO,IAAIpB,WAAW,CAAC;MACrB,cAAc,EAAE;KACjB,CAAC;EACJ;;;;;;;AA5KWK,WAAW,GAAA6C,UAAA,EAHvBpD,UAAU,CAAC;EACVqD,UAAU,EAAE;CACb,CAAC,C,EACW9C,WAAW,CA6KvB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}