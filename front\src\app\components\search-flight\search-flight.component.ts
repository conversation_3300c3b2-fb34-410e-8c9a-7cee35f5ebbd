import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';

import { FlightService } from '../../services/flight.service';
import { FlightSearchForm, FlightClass } from '../../models/flight-search.interface';

@Component({
  selector: 'app-search-flight',
  templateUrl: './search-flight.component.html',
  styleUrls: ['./search-flight.component.css']
})
export class SearchFlightComponent implements OnInit {
  searchForm!: FormGroup;
  isLoading = false;
  errorMessage = '';
  
  // Options pour les sélecteurs
  flightClasses = [
    { value: FlightClass.ECONOMY, label: 'Economy' },
    { value: FlightClass.PREMIUM_ECONOMY, label: 'Premium Economy' },
    { value: FlightClass.BUSINESS, label: 'Business' },
    { value: FlightClass.FIRST, label: 'First Class' }
  ];

  // Données pour les passagers
  passengerCounts = {
    adults: Array.from({length: 9}, (_, i) => i + 1),
    children: Array.from({length: 8}, (_, i) => i),
    infants: Array.from({length: 4}, (_, i) => i)
  };

  // Compagnies aériennes préférées (exemple)
  preferredAirlines = [
    'Preferred Airline',
    'Turkish Airlines',
    'Emirates',
    'Qatar Airways',
    'Lufthansa',
    'Air France',
    'British Airways'
  ];

  constructor(
    private formBuilder: FormBuilder,
    private flightService: FlightService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.initializeForm();
  }

  /**
   * Initialise le formulaire de recherche
   */
  private initializeForm(): void {
    this.searchForm = this.formBuilder.group({
      tripType: ['oneWay', Validators.required],
      departureLocation: ['', [Validators.required, Validators.minLength(3)]],
      arrivalLocation: ['', [Validators.required, Validators.minLength(3)]],
      departureDate: ['', Validators.required],
      returnDate: [''],
      adults: [1, [Validators.required, Validators.min(1)]],
      children: [0, [Validators.required, Validators.min(0)]],
      infants: [0, [Validators.required, Validators.min(0)]],
      flightClass: [FlightClass.ECONOMY, Validators.required],
      directFlightsOnly: [false],
      preferredAirline: ['']
    });

    // Validation conditionnelle pour la date de retour
    this.searchForm.get('tripType')?.valueChanges.subscribe(tripType => {
      const returnDateControl = this.searchForm.get('returnDate');
      if (tripType === 'roundTrip') {
        returnDateControl?.setValidators([Validators.required]);
      } else {
        returnDateControl?.clearValidators();
      }
      returnDateControl?.updateValueAndValidity();
    });
  }

  /**
   * Getter pour accéder facilement aux contrôles du formulaire
   */
  get formControls() {
    return this.searchForm.controls;
  }

  /**
   * Échange les aéroports de départ et d'arrivée
   */
  swapAirports(): void {
    const departure = this.searchForm.get('departureLocation')?.value;
    const arrival = this.searchForm.get('arrivalLocation')?.value;
    
    this.searchForm.patchValue({
      departureLocation: arrival,
      arrivalLocation: departure
    });
  }

  /**
   * Obtient le nombre total de passagers
   */
  getTotalPassengers(): number {
    const adults = this.searchForm.get('adults')?.value || 0;
    const children = this.searchForm.get('children')?.value || 0;
    const infants = this.searchForm.get('infants')?.value || 0;
    return adults + children + infants;
  }

  /**
   * Obtient le texte d'affichage pour les passagers
   */
  getPassengerText(): string {
    const adults = this.searchForm.get('adults')?.value || 0;
    const children = this.searchForm.get('children')?.value || 0;
    const infants = this.searchForm.get('infants')?.value || 0;
    
    let text = `${adults} Adult${adults > 1 ? 's' : ''}`;
    if (children > 0) text += `, ${children} Child${children > 1 ? 'ren' : ''}`;
    if (infants > 0) text += `, ${infants} Infant${infants > 1 ? 's' : ''}`;
    
    return text;
  }

  /**
   * Obtient le nombre de jours entre les dates
   */
  getDaysBetweenDates(): string {
    const departureDate = this.searchForm.get('departureDate')?.value;
    const returnDate = this.searchForm.get('returnDate')?.value;
    
    if (!departureDate || !returnDate) return '';
    
    const start = new Date(departureDate);
    const end = new Date(returnDate);
    const diffTime = Math.abs(end.getTime() - start.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    return `+/- ${diffDays} Days`;
  }

  /**
   * Soumet le formulaire de recherche
   */
  onSubmit(): void {
    if (this.searchForm.valid && !this.isLoading) {
      this.isLoading = true;
      this.errorMessage = '';

      const searchData: FlightSearchForm = {
        tripType: this.searchForm.value.tripType,
        departureLocation: this.searchForm.value.departureLocation.trim(),
        arrivalLocation: this.searchForm.value.arrivalLocation.trim(),
        departureDate: this.searchForm.value.departureDate,
        returnDate: this.searchForm.value.returnDate,
        passengers: {
          adults: this.searchForm.value.adults,
          children: this.searchForm.value.children,
          infants: this.searchForm.value.infants
        },
        flightClass: this.searchForm.value.flightClass,
        directFlightsOnly: this.searchForm.value.directFlightsOnly,
        preferredAirline: this.searchForm.value.preferredAirline
      };

      // Appel du service selon le type de voyage
      let searchObservable;
      switch (searchData.tripType) {
        case 'oneWay':
          searchObservable = this.flightService.searchOneWayFlights(searchData);
          break;
        case 'roundTrip':
          searchObservable = this.flightService.searchRoundTripFlights(searchData);
          break;
        case 'multiCity':
          searchObservable = this.flightService.searchMulticityFlights(searchData);
          break;
        default:
          this.isLoading = false;
          this.errorMessage = 'Type de voyage non valide';
          return;
      }

      searchObservable.subscribe({
        next: (response) => {
          this.isLoading = false;
          if (response.header.success) {
            console.log('Recherche réussie:', response);
            // Rediriger vers la page de résultats ou traiter les résultats
            // this.router.navigate(['/flight-results'], { state: { results: response } });
          } else {
            this.handleApiError(response);
          }
        },
        error: (error) => {
          this.isLoading = false;
          this.errorMessage = error.message || 'Une erreur est survenue lors de la recherche';
          console.error('Erreur de recherche:', error);
        }
      });
    } else {
      this.markFormGroupTouched();
    }
  }

  /**
   * Gère les erreurs retournées par l'API
   */
  private handleApiError(response: any): void {
    if (response.header.messages && response.header.messages.length > 0) {
      this.errorMessage = response.header.messages[0].message;
    } else {
      this.errorMessage = 'Aucun vol trouvé pour ces critères';
    }
  }

  /**
   * Marque tous les champs du formulaire comme touchés
   */
  private markFormGroupTouched(): void {
    Object.keys(this.searchForm.controls).forEach(key => {
      const control = this.searchForm.get(key);
      control?.markAsTouched();
    });
  }

  /**
   * Vérifie si un champ a une erreur et a été touché
   */
  hasError(fieldName: string, errorType: string): boolean {
    const field = this.searchForm.get(fieldName);
    return !!(field?.hasError(errorType) && field?.touched);
  }

  /**
   * Récupère le message d'erreur pour un champ
   */
  getErrorMessage(fieldName: string): string {
    const field = this.searchForm.get(fieldName);
    
    if (field?.hasError('required')) {
      return `Ce champ est requis`;
    }
    
    if (field?.hasError('minlength')) {
      const requiredLength = field.errors?.['minlength']?.requiredLength;
      return `Minimum ${requiredLength} caractères requis`;
    }
    
    if (field?.hasError('min')) {
      const min = field.errors?.['min']?.min;
      return `La valeur minimum est ${min}`;
    }
    
    return '';
  }

  /**
   * Nettoie le message d'erreur
   */
  clearError(): void {
    this.errorMessage = '';
  }
}
