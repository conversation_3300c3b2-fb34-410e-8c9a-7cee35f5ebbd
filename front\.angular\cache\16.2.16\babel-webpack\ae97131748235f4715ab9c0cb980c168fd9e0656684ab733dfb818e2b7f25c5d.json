{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./dashboard.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"C:/Users/<USER>/Desktop/angular/front/src/app/components/dashboard/dashboard.component.ts.css?ngResource!=!C:\\\\Users\\\\<USER>\\\\Desktop\\\\angular\\\\front\\\\node_modules\\\\@ngtools\\\\webpack\\\\src\\\\loaders\\\\inline-resource.js?data=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%3D%3D!C:/Users/<USER>/Desktop/angular/front/src/app/components/dashboard/dashboard.component.ts\";\nimport { Component } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { AuthService } from '../../services/auth.service';\nexport let DashboardComponent = class DashboardComponent {\n  constructor(authService, router) {\n    this.authService = authService;\n    this.router = router;\n  }\n  ngOnInit() {\n    this.userInfo = this.authService.getUserInfo();\n  }\n  logout() {\n    this.authService.logout();\n    this.router.navigate(['/login']);\n  }\n  static {\n    this.ctorParameters = () => [{\n      type: AuthService\n    }, {\n      type: Router\n    }];\n  }\n};\nDashboardComponent = __decorate([Component({\n  selector: 'app-dashboard',\n  template: __NG_CLI_RESOURCE__0,\n  styles: [__NG_CLI_RESOURCE__1]\n})], DashboardComponent);", "map": {"version": 3, "names": ["Component", "Router", "AuthService", "DashboardComponent", "constructor", "authService", "router", "ngOnInit", "userInfo", "getUserInfo", "logout", "navigate", "__decorate", "selector", "template", "__NG_CLI_RESOURCE__0"], "sources": ["C:\\Users\\<USER>\\Desktop\\angular\\front\\src\\app\\components\\dashboard\\dashboard.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { AuthService } from '../../services/auth.service';\n\n@Component({\n  selector: 'app-dashboard',\n  templateUrl: './dashboard.component.html',\n  styles: [`\n    /* Variables CSS pour Block to Book */\n    :host {\n      --primary-blue: #4a90e2;\n      --secondary-blue: #7bb3f0;\n      --dark-blue: #2c5aa0;\n      --light-gray: #f5f7fa;\n      --medium-gray: #8fa4b3;\n      --dark-gray: #4a5568;\n      --white: #ffffff;\n      --border-color: #e2e8f0;\n      --shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n      --shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.15);\n      --transition: all 0.3s ease;\n    }\n\n    /* Container principal */\n    .dashboard-container {\n      min-height: 100vh;\n      background: var(--light-gray);\n      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;\n      display: flex;\n      flex-direction: column;\n    }\n\n    /* Header */\n    .dashboard-header {\n      background: var(--white);\n      border-bottom: 1px solid var(--border-color);\n      box-shadow: var(--shadow);\n    }\n\n    .header-content {\n      max-width: 1400px;\n      margin: 0 auto;\n      padding: 1rem 2rem;\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      flex-wrap: wrap;\n      gap: 1rem;\n    }\n\n    /* Logo Section */\n    .logo-section {\n      display: flex;\n      align-items: center;\n      gap: 2rem;\n    }\n\n    .logo {\n      display: flex;\n      align-items: center;\n      gap: 0.75rem;\n    }\n\n    .logo-icon {\n      width: 2rem;\n      height: 2rem;\n      color: var(--primary-blue);\n    }\n\n    .logo-text {\n      font-size: 1.25rem;\n      font-weight: 700;\n      color: var(--dark-gray);\n      letter-spacing: -0.025em;\n    }\n\n    .demo-info {\n      display: flex;\n      flex-direction: column;\n      gap: 0.25rem;\n    }\n\n    .demo-id {\n      font-size: 0.875rem;\n      font-weight: 600;\n      color: var(--dark-gray);\n    }\n\n    .demo-details {\n      font-size: 0.75rem;\n      color: var(--medium-gray);\n    }\n\n    /* Header Contact */\n    .header-contact {\n      display: flex;\n      gap: 2rem;\n      flex-wrap: wrap;\n    }\n\n    .contact-item {\n      display: flex;\n      align-items: center;\n      gap: 0.5rem;\n      font-size: 0.875rem;\n      color: var(--dark-gray);\n    }\n\n    .contact-icon {\n      width: 1rem;\n      height: 1rem;\n      color: var(--medium-gray);\n    }\n\n    /* Header Navigation */\n    .header-nav {\n      display: flex;\n      align-items: center;\n      gap: 1.5rem;\n    }\n\n    .nav-item {\n      display: flex;\n      align-items: center;\n      gap: 0.5rem;\n      padding: 0.5rem 1rem;\n      border-radius: 0.5rem;\n      text-decoration: none;\n      color: var(--dark-gray);\n      font-size: 0.875rem;\n      font-weight: 500;\n      transition: var(--transition);\n    }\n\n    .nav-item:hover {\n      background: var(--light-gray);\n      color: var(--primary-blue);\n    }\n\n    .nav-item.active {\n      background: var(--primary-blue);\n      color: var(--white);\n    }\n\n    .nav-icon {\n      width: 1.25rem;\n      height: 1.25rem;\n    }\n\n    .logout-btn {\n      display: flex;\n      align-items: center;\n      gap: 0.5rem;\n      padding: 0.5rem 1rem;\n      background: transparent;\n      border: 1px solid var(--border-color);\n      border-radius: 0.5rem;\n      color: var(--dark-gray);\n      font-size: 0.875rem;\n      font-weight: 500;\n      cursor: pointer;\n      transition: var(--transition);\n    }\n\n    .logout-btn:hover {\n      background: #fee2e2;\n      border-color: #fca5a5;\n      color: #dc2626;\n    }\n\n    /* Main Dashboard */\n    .dashboard-main {\n      flex: 1;\n      padding: 2rem;\n    }\n\n    .dashboard-grid {\n      max-width: 1400px;\n      margin: 0 auto;\n      display: grid;\n      grid-template-columns: 280px 1fr;\n      gap: 2rem;\n      height: calc(100vh - 120px);\n    }\n\n    /* Sidebar */\n    .sidebar {\n      background: var(--white);\n      border-radius: 0.75rem;\n      padding: 1.5rem;\n      box-shadow: var(--shadow);\n      height: fit-content;\n    }\n\n    .sidebar-section {\n      display: flex;\n      flex-direction: column;\n      gap: 0.5rem;\n      margin-bottom: 2rem;\n    }\n\n    .sidebar-section:last-child {\n      margin-bottom: 0;\n    }\n\n    .sidebar-item {\n      display: flex;\n      align-items: center;\n      gap: 0.75rem;\n      padding: 0.875rem 1rem;\n      border-radius: 0.5rem;\n      color: var(--dark-gray);\n      font-size: 0.875rem;\n      font-weight: 500;\n      cursor: pointer;\n      transition: var(--transition);\n    }\n\n    .sidebar-item:hover {\n      background: var(--light-gray);\n      color: var(--primary-blue);\n    }\n\n    .sidebar-icon {\n      width: 1.25rem;\n      height: 1.25rem;\n      color: var(--medium-gray);\n      transition: var(--transition);\n    }\n\n    .sidebar-item:hover .sidebar-icon {\n      color: var(--primary-blue);\n    }\n\n    /* Main Content */\n    .main-content {\n      background: var(--white);\n      border-radius: 0.75rem;\n      padding: 2rem;\n      box-shadow: var(--shadow);\n      overflow: hidden;\n    }\n\n    .content-grid {\n      display: grid;\n      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n      gap: 2rem;\n      height: 100%;\n    }\n\n    /* Service Cards */\n    .service-card {\n      background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);\n      border-radius: 1rem;\n      padding: 2rem;\n      color: var(--white);\n      display: flex;\n      flex-direction: column;\n      align-items: center;\n      justify-content: center;\n      text-align: center;\n      cursor: pointer;\n      transition: var(--transition);\n      min-height: 200px;\n      position: relative;\n      overflow: hidden;\n    }\n\n    .service-card::before {\n      content: '';\n      position: absolute;\n      top: 0;\n      left: 0;\n      right: 0;\n      bottom: 0;\n      background: linear-gradient(45deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%);\n      opacity: 0;\n      transition: var(--transition);\n    }\n\n    .service-card:hover::before {\n      opacity: 1;\n    }\n\n    .service-card:hover {\n      transform: translateY(-4px);\n      box-shadow: var(--shadow-lg);\n    }\n\n    .card-icon {\n      width: 4rem;\n      height: 4rem;\n      margin-bottom: 1rem;\n      background: rgba(255, 255, 255, 0.2);\n      border-radius: 50%;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n    }\n\n    .card-icon svg {\n      width: 2rem;\n      height: 2rem;\n      color: var(--white);\n    }\n\n    .service-card h3 {\n      font-size: 1.5rem;\n      font-weight: 700;\n      margin: 0;\n      letter-spacing: -0.025em;\n    }\n\n    /* Responsive Design */\n    @media (max-width: 1200px) {\n      .dashboard-grid {\n        grid-template-columns: 1fr;\n        gap: 1.5rem;\n      }\n\n      .sidebar {\n        order: 2;\n      }\n\n      .main-content {\n        order: 1;\n      }\n    }\n\n    @media (max-width: 768px) {\n      .header-content {\n        flex-direction: column;\n        align-items: flex-start;\n        gap: 1.5rem;\n      }\n\n      .logo-section {\n        flex-direction: column;\n        align-items: flex-start;\n        gap: 1rem;\n      }\n\n      .header-contact {\n        flex-direction: column;\n        gap: 0.75rem;\n      }\n\n      .header-nav {\n        flex-wrap: wrap;\n        gap: 1rem;\n      }\n\n      .dashboard-main {\n        padding: 1rem;\n      }\n\n      .dashboard-grid {\n        gap: 1rem;\n      }\n\n      .sidebar {\n        padding: 1rem;\n      }\n\n      .main-content {\n        padding: 1.5rem;\n      }\n\n      .content-grid {\n        grid-template-columns: 1fr;\n        gap: 1rem;\n      }\n    }\n  `]\n})\nexport class DashboardComponent implements OnInit {\n  userInfo: any;\n\n  constructor(\n    private authService: AuthService,\n    private router: Router\n  ) {}\n\n  ngOnInit(): void {\n    this.userInfo = this.authService.getUserInfo();\n  }\n\n  logout(): void {\n    this.authService.logout();\n    this.router.navigate(['/login']);\n  }\n}\n"], "mappings": ";;;AAAA,SAASA,SAAS,QAAgB,eAAe;AACjD,SAASC,MAAM,QAAQ,iBAAiB;AACxC,SAASC,WAAW,QAAQ,6BAA6B;AAqXlD,WAAMC,kBAAkB,GAAxB,MAAMA,kBAAkB;EAG7BC,YACUC,WAAwB,EACxBC,MAAc;IADd,KAAAD,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;EACb;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,QAAQ,GAAG,IAAI,CAACH,WAAW,CAACI,WAAW,EAAE;EAChD;EAEAC,MAAMA,CAAA;IACJ,IAAI,CAACL,WAAW,CAACK,MAAM,EAAE;IACzB,IAAI,CAACJ,MAAM,CAACK,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;EAClC;;;;;;;;;AAfWR,kBAAkB,GAAAS,UAAA,EAnX9BZ,SAAS,CAAC;EACTa,QAAQ,EAAE,eAAe;EACzBC,QAAA,EAAAC,oBAAyC;;CAgX1C,CAAC,C,EACWZ,kBAAkB,CAgB9B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}