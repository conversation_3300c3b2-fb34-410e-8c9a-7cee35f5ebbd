{"ast": null, "code": "import { asyncScheduler } from '../scheduler/async';\nimport { sample } from './sample';\nimport { interval } from '../observable/interval';\nexport function sampleTime(period, scheduler = asyncScheduler) {\n  return sample(interval(period, scheduler));\n}", "map": {"version": 3, "names": ["asyncScheduler", "sample", "interval", "sampleTime", "period", "scheduler"], "sources": ["C:/Users/<USER>/Desktop/angular/front/node_modules/rxjs/dist/esm/internal/operators/sampleTime.js"], "sourcesContent": ["import { asyncScheduler } from '../scheduler/async';\nimport { sample } from './sample';\nimport { interval } from '../observable/interval';\nexport function sampleTime(period, scheduler = asyncScheduler) {\n    return sample(interval(period, scheduler));\n}\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,oBAAoB;AACnD,SAASC,MAAM,QAAQ,UAAU;AACjC,SAASC,QAAQ,QAAQ,wBAAwB;AACjD,OAAO,SAASC,UAAUA,CAACC,MAAM,EAAEC,SAAS,GAAGL,cAAc,EAAE;EAC3D,OAAOC,MAAM,CAACC,QAAQ,CAACE,MAAM,EAAEC,SAAS,CAAC,CAAC;AAC9C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}