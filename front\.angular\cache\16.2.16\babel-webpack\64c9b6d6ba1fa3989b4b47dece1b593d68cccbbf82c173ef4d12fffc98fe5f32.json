{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/auth.service\";\nimport * as i2 from \"@angular/router\";\nexport class AuthGuard {\n  constructor(authService, router) {\n    this.authService = authService;\n    this.router = router;\n  }\n  canActivate(route, state) {\n    if (this.authService.isAuthenticated()) {\n      return true;\n    } else {\n      // Rediriger vers la page de connexion si non authentifié\n      this.router.navigate(['/login'], {\n        queryParams: {\n          returnUrl: state.url\n        }\n      });\n      return false;\n    }\n  }\n  static {\n    this.ɵfac = function AuthGuard_Factory(t) {\n      return new (t || AuthGuard)(i0.ɵɵinject(i1.AuthService), i0.ɵɵinject(i2.Router));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: AuthGuard,\n      factory: AuthGuard.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON>", "constructor", "authService", "router", "canActivate", "route", "state", "isAuthenticated", "navigate", "queryParams", "returnUrl", "url", "i0", "ɵɵinject", "i1", "AuthService", "i2", "Router", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\angular\\front\\src\\app\\guards\\auth.guard.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { CanActivate, Router, ActivatedRouteSnapshot, RouterStateSnapshot } from '@angular/router';\nimport { AuthService } from '../services/auth.service';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class AuthGuard implements CanActivate {\n\n  constructor(\n    private authService: AuthService,\n    private router: Router\n  ) {}\n\n  canActivate(\n    route: ActivatedRouteSnapshot,\n    state: RouterStateSnapshot\n  ): boolean {\n    if (this.authService.isAuthenticated()) {\n      return true;\n    } else {\n      // Rediriger vers la page de connexion si non authentifié\n      this.router.navigate(['/login'], { \n        queryParams: { returnUrl: state.url } \n      });\n      return false;\n    }\n  }\n}\n"], "mappings": ";;;AAOA,OAAM,MAAOA,SAAS;EAEpBC,YACUC,WAAwB,EACxBC,MAAc;IADd,KAAAD,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;EACb;EAEHC,WAAWA,CACTC,KAA6B,EAC7BC,KAA0B;IAE1B,IAAI,IAAI,CAACJ,WAAW,CAACK,eAAe,EAAE,EAAE;MACtC,OAAO,IAAI;KACZ,MAAM;MACL;MACA,IAAI,CAACJ,MAAM,CAACK,QAAQ,CAAC,CAAC,QAAQ,CAAC,EAAE;QAC/BC,WAAW,EAAE;UAAEC,SAAS,EAAEJ,KAAK,CAACK;QAAG;OACpC,CAAC;MACF,OAAO,KAAK;;EAEhB;;;uBApBWX,SAAS,EAAAY,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;EAAA;;;aAATjB,SAAS;MAAAkB,OAAA,EAATlB,SAAS,CAAAmB,IAAA;MAAAC,UAAA,EAFR;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}