{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../services/auth.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/common\";\nfunction LoginComponent_div_33_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 64);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 65);\n    i0.ɵɵelement(2, \"path\", 66);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 67);\n    i0.ɵɵlistener(\"click\", function LoginComponent_div_33_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r7 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r7.clearError());\n    });\n    i0.ɵɵtext(6, \"\\u00D7\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r0.errorMessage);\n  }\n}\nfunction LoginComponent_div_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 68);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getErrorMessage(\"agency\"), \" \");\n  }\n}\nfunction LoginComponent__svg_svg_51_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(0, \"svg\", 69);\n    i0.ɵɵelement(1, \"path\", 70)(2, \"path\", 71);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent__svg_svg_52_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(0, \"svg\", 69);\n    i0.ɵɵelement(1, \"path\", 72);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_div_53_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 68);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r4.getErrorMessage(\"password\"), \" \");\n  }\n}\nfunction LoginComponent_span_58_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"LOGIN\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_span_59_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 73);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 74);\n    i0.ɵɵelement(2, \"circle\", 75)(3, \"path\", 76);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" Loading... \");\n    i0.ɵɵelementEnd();\n  }\n}\nexport class LoginComponent {\n  constructor(formBuilder, authService, router) {\n    this.formBuilder = formBuilder;\n    this.authService = authService;\n    this.router = router;\n    this.isLoading = false;\n    this.errorMessage = '';\n    this.showPassword = false;\n  }\n  ngOnInit() {\n    this.initializeForm();\n    // Rediriger si déjà connecté\n    if (this.authService.isAuthenticated()) {\n      this.router.navigate(['/dashboard']); // Ajustez selon votre route de destination\n    }\n  }\n  /**\n   * Initialise le formulaire de connexion avec les validations\n   */\n  initializeForm() {\n    this.loginForm = this.formBuilder.group({\n      agency: ['', [Validators.required, Validators.minLength(2)]],\n      user: ['', [Validators.required, Validators.minLength(2)]],\n      password: ['', [Validators.required, Validators.minLength(6)]]\n    });\n  }\n  /**\n   * Getter pour accéder facilement aux contrôles du formulaire\n   */\n  get formControls() {\n    return this.loginForm.controls;\n  }\n  /**\n   * Bascule la visibilité du mot de passe\n   */\n  togglePasswordVisibility() {\n    this.showPassword = !this.showPassword;\n  }\n  /**\n   * Soumet le formulaire de connexion\n   */\n  onSubmit() {\n    if (this.loginForm.valid && !this.isLoading) {\n      this.isLoading = true;\n      this.errorMessage = '';\n      // Créer l'objet de requête avec les noms de propriétés exacts du backend\n      const authRequest = {\n        Agency: this.loginForm.value.agency.trim(),\n        User: this.loginForm.value.user.trim(),\n        Password: this.loginForm.value.password\n      };\n      this.authService.authenticate(authRequest).subscribe({\n        next: response => {\n          this.isLoading = false;\n          if (response.header.success) {\n            // Connexion réussie\n            console.log('Connexion réussie:', response.body.userInfo);\n            // Rediriger vers le dashboard ou la page d'accueil\n            this.router.navigate(['/dashboard']); // Ajustez selon votre route\n          } else {\n            // Erreur retournée par l'API\n            this.handleApiError(response);\n          }\n        },\n        error: error => {\n          this.isLoading = false;\n          this.errorMessage = error.message || 'Une erreur est survenue lors de la connexion';\n          console.error('Erreur de connexion:', error);\n        }\n      });\n    } else {\n      // Marquer tous les champs comme touchés pour afficher les erreurs\n      this.markFormGroupTouched();\n    }\n  }\n  /**\n   * Gère les erreurs retournées par l'API\n   */\n  handleApiError(response) {\n    if (response.header.messages && response.header.messages.length > 0) {\n      // Utiliser le premier message d'erreur de l'API\n      this.errorMessage = response.header.messages[0].message;\n    } else {\n      this.errorMessage = 'Échec de l\\'authentification';\n    }\n  }\n  /**\n   * Marque tous les champs du formulaire comme touchés\n   */\n  markFormGroupTouched() {\n    Object.keys(this.loginForm.controls).forEach(key => {\n      const control = this.loginForm.get(key);\n      control?.markAsTouched();\n    });\n  }\n  /**\n   * Vérifie si un champ a une erreur et a été touché\n   */\n  hasError(fieldName, errorType) {\n    const field = this.loginForm.get(fieldName);\n    return !!(field?.hasError(errorType) && field?.touched);\n  }\n  /**\n   * Récupère le message d'erreur pour un champ\n   */\n  getErrorMessage(fieldName) {\n    const field = this.loginForm.get(fieldName);\n    if (field?.hasError('required')) {\n      return `${this.getFieldDisplayName(fieldName)} est requis`;\n    }\n    if (field?.hasError('minlength')) {\n      const requiredLength = field.errors?.['minlength']?.requiredLength;\n      return `${this.getFieldDisplayName(fieldName)} doit contenir au moins ${requiredLength} caractères`;\n    }\n    return '';\n  }\n  /**\n   * Retourne le nom d'affichage pour un champ\n   */\n  getFieldDisplayName(fieldName) {\n    const displayNames = {\n      agency: 'Le code agence',\n      user: 'Le code utilisateur',\n      password: 'Le mot de passe'\n    };\n    return displayNames[fieldName] || fieldName;\n  }\n  /**\n   * Nettoie le message d'erreur\n   */\n  clearError() {\n    this.errorMessage = '';\n  }\n  static {\n    this.ɵfac = function LoginComponent_Factory(t) {\n      return new (t || LoginComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: LoginComponent,\n      selectors: [[\"app-login\"]],\n      decls: 81,\n      vars: 16,\n      consts: [[1, \"login-container\"], [1, \"login-left\"], [1, \"brand-section\"], [1, \"brand-title\"], [1, \"brand-subtitle\"], [1, \"travel-illustration\"], [\"viewBox\", \"0 0 200 200\", \"fill\", \"none\", 1, \"travel-icon\"], [\"d\", \"M50 100 L150 80 L160 85 L150 90 L50 110 Z\", \"fill\", \"white\", \"opacity\", \"0.9\"], [\"d\", \"M45 105 L55 100 L55 110 Z\", \"fill\", \"white\", \"opacity\", \"0.7\"], [\"cx\", \"40\", \"cy\", \"60\", \"r\", \"15\", \"fill\", \"white\", \"opacity\", \"0.6\"], [\"cx\", \"50\", \"cy\", \"60\", \"r\", \"18\", \"fill\", \"white\", \"opacity\", \"0.6\"], [\"cx\", \"60\", \"cy\", \"60\", \"r\", \"15\", \"fill\", \"white\", \"opacity\", \"0.6\"], [\"d\", \"M0 150 L30 120 L60 140 L90 110 L120 130 L150 100 L180 120 L200 110 L200 200 L0 200 Z\", \"fill\", \"white\", \"opacity\", \"0.3\"], [1, \"monuments\"], [\"viewBox\", \"0 0 300 100\", \"fill\", \"none\", 1, \"monument-icon\"], [\"d\", \"M50 90 L60 20 L70 90 M45 90 L75 90 M55 60 L65 60\", \"stroke\", \"#0ea5e9\", \"stroke-width\", \"2\", \"fill\", \"none\"], [\"d\", \"M120 90 L120 60 Q120 50 130 50 Q140 50 140 60 L140 90 M110 90 L150 90\", \"stroke\", \"#0ea5e9\", \"stroke-width\", \"2\", \"fill\", \"none\"], [\"cx\", \"130\", \"cy\", \"55\", \"r\", \"3\", \"fill\", \"#0ea5e9\"], [\"x\", \"180\", \"y\", \"70\", \"width\", \"15\", \"height\", \"20\", \"fill\", \"#0ea5e9\", \"opacity\", \"0.7\"], [\"x\", \"200\", \"y\", \"60\", \"width\", \"15\", \"height\", \"30\", \"fill\", \"#0ea5e9\", \"opacity\", \"0.8\"], [\"x\", \"220\", \"y\", \"50\", \"width\", \"15\", \"height\", \"40\", \"fill\", \"#0ea5e9\"], [1, \"login-right\"], [1, \"login-form-container\"], [1, \"login-header\"], [1, \"plane-icon\"], [\"viewBox\", \"0 0 24 24\", \"fill\", \"none\", \"stroke\", \"currentColor\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M12 19l9 2-9-18-9 18 9-2zm0 0v-8\"], [1, \"welcome-title\"], [1, \"welcome-subtitle\"], [\"class\", \"error-message\", \"role\", \"alert\", 4, \"ngIf\"], [\"novalidate\", \"\", 1, \"login-form\", 3, \"formGroup\", \"ngSubmit\"], [1, \"form-group\"], [\"for\", \"agency\", 1, \"form-label\"], [1, \"input-container\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"input-icon\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207\"], [\"type\", \"text\", \"id\", \"agency\", \"formControlName\", \"agency\", \"placeholder\", \"Enter your email\", \"autocomplete\", \"email\", 1, \"form-input\"], [\"class\", \"field-error\", 4, \"ngIf\"], [\"for\", \"password\", 1, \"form-label\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z\"], [\"id\", \"password\", \"formControlName\", \"password\", \"placeholder\", \"\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\", \"autocomplete\", \"current-password\", 1, \"form-input\", 3, \"type\"], [\"type\", \"button\", 1, \"password-toggle\", 3, \"click\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 4, \"ngIf\"], [1, \"forgot-password\"], [\"href\", \"#\", 1, \"forgot-link\"], [\"type\", \"submit\", 1, \"login-button\", 3, \"disabled\"], [4, \"ngIf\"], [\"class\", \"loading-content\", 4, \"ngIf\"], [1, \"divider\"], [1, \"social-login\"], [\"type\", \"button\", 1, \"social-btn\", \"google-btn\"], [\"viewBox\", \"0 0 24 24\"], [\"fill\", \"#4285F4\", \"d\", \"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z\"], [\"fill\", \"#34A853\", \"d\", \"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z\"], [\"fill\", \"#FBBC05\", \"d\", \"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z\"], [\"fill\", \"#EA4335\", \"d\", \"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z\"], [\"type\", \"button\", 1, \"social-btn\", \"facebook-btn\"], [\"viewBox\", \"0 0 24 24\", \"fill\", \"#1877F2\"], [\"d\", \"M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z\"], [\"type\", \"button\", 1, \"social-btn\", \"apple-btn\"], [\"viewBox\", \"0 0 24 24\", \"fill\", \"#000\"], [\"d\", \"M12.017 0C8.396 0 8.025.044 8.025.044c0 .467.02.94.058 1.4.037.46.094.92.17 1.374.076.454.17.9.282 1.336.112.436.25.86.413 1.27.163.41.35.8.563 1.17.213.37.45.72.712 1.05.262.33.55.64.862.93.312.29.65.56 1.012.81.362.25.75.48 1.162.69.412.21.85.4 1.312.57.462.17.95.32 1.462.45.512.13 1.05.24 1.612.33.562.09 1.15.16 1.762.21.612.05 1.25.08 1.912.09h.08c.662-.01 1.3-.04 1.912-.09.612-.05 1.2-.12 1.762-.21.562-.09 1.1-.2 1.612-.33.512-.13 1-.28 1.462-.45.462-.17.9-.36 1.312-.57.412-.21.8-.44 1.162-.69.362-.25.7-.52 1.012-.81.312-.29.6-.6.862-.93.262-.33.499-.68.712-1.05.213-.37.4-.76.563-1.17.163-.41.301-.834.413-1.27.112-.436.206-.882.282-1.336.076-.454.133-.914.17-1.374.037-.46.058-.933.058-1.4 0 0-.371-.044-3.992-.044z\"], [1, \"register-link\"], [\"href\", \"#\", 1, \"register-btn\"], [\"role\", \"alert\", 1, \"error-message\"], [\"fill\", \"currentColor\", \"viewBox\", \"0 0 20 20\", 1, \"error-icon\"], [\"fill-rule\", \"evenodd\", \"d\", \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z\", \"clip-rule\", \"evenodd\"], [\"type\", \"button\", 1, \"error-close\", 3, \"click\"], [1, \"field-error\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21\"], [1, \"loading-content\"], [\"viewBox\", \"0 0 24 24\", 1, \"loading-spinner\"], [\"cx\", \"12\", \"cy\", \"12\", \"r\", \"10\", \"stroke\", \"currentColor\", \"stroke-width\", \"4\", \"fill\", \"none\", \"opacity\", \"0.25\"], [\"fill\", \"currentColor\", \"d\", \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\", \"opacity\", \"0.75\"]],\n      template: function LoginComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h1\", 3);\n          i0.ɵɵtext(4, \"Paximum Tours\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"p\", 4);\n          i0.ɵɵtext(6, \"Travel is the only purchase that enriches you in ways beyond material wealth\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(7, \"div\", 5);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(8, \"svg\", 6);\n          i0.ɵɵelement(9, \"path\", 7)(10, \"path\", 8)(11, \"circle\", 9)(12, \"circle\", 10)(13, \"circle\", 11)(14, \"path\", 12);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(15, \"div\", 13);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(16, \"svg\", 14);\n          i0.ɵɵelement(17, \"path\", 15)(18, \"path\", 16)(19, \"circle\", 17)(20, \"rect\", 18)(21, \"rect\", 19)(22, \"rect\", 20);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(23, \"div\", 21)(24, \"div\", 22)(25, \"div\", 23)(26, \"div\", 24);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(27, \"svg\", 25);\n          i0.ɵɵelement(28, \"path\", 26);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(29, \"h2\", 27);\n          i0.ɵɵtext(30, \"Welcome\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(31, \"p\", 28);\n          i0.ɵɵtext(32, \"Login with Email\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(33, LoginComponent_div_33_Template, 7, 1, \"div\", 29);\n          i0.ɵɵelementStart(34, \"form\", 30);\n          i0.ɵɵlistener(\"ngSubmit\", function LoginComponent_Template_form_ngSubmit_34_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵelementStart(35, \"div\", 31)(36, \"label\", 32);\n          i0.ɵɵtext(37, \"Email/Agency\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(38, \"div\", 33);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(39, \"svg\", 34);\n          i0.ɵɵelement(40, \"path\", 35);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelement(41, \"input\", 36);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(42, LoginComponent_div_42_Template, 2, 1, \"div\", 37);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(43, \"div\", 31)(44, \"label\", 38);\n          i0.ɵɵtext(45, \"Password\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(46, \"div\", 33);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(47, \"svg\", 34);\n          i0.ɵɵelement(48, \"path\", 39);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelement(49, \"input\", 40);\n          i0.ɵɵelementStart(50, \"button\", 41);\n          i0.ɵɵlistener(\"click\", function LoginComponent_Template_button_click_50_listener() {\n            return ctx.togglePasswordVisibility();\n          });\n          i0.ɵɵtemplate(51, LoginComponent__svg_svg_51_Template, 3, 0, \"svg\", 42);\n          i0.ɵɵtemplate(52, LoginComponent__svg_svg_52_Template, 2, 0, \"svg\", 42);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(53, LoginComponent_div_53_Template, 2, 1, \"div\", 37);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(54, \"div\", 43)(55, \"a\", 44);\n          i0.ɵɵtext(56, \"Forgot your password?\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(57, \"button\", 45);\n          i0.ɵɵtemplate(58, LoginComponent_span_58_Template, 2, 0, \"span\", 46);\n          i0.ɵɵtemplate(59, LoginComponent_span_59_Template, 5, 0, \"span\", 47);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(60, \"div\", 48)(61, \"span\");\n          i0.ɵɵtext(62, \"OR\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(63, \"div\", 49)(64, \"button\", 50);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(65, \"svg\", 51);\n          i0.ɵɵelement(66, \"path\", 52)(67, \"path\", 53)(68, \"path\", 54)(69, \"path\", 55);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(70, \"button\", 56);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(71, \"svg\", 57);\n          i0.ɵɵelement(72, \"path\", 58);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(73, \"button\", 59);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(74, \"svg\", 60);\n          i0.ɵɵelement(75, \"path\", 61);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(76, \"div\", 62)(77, \"span\");\n          i0.ɵɵtext(78, \"Don't have account? \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(79, \"a\", 63);\n          i0.ɵɵtext(80, \"Register Now\");\n          i0.ɵɵelementEnd()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(33);\n          i0.ɵɵproperty(\"ngIf\", ctx.errorMessage);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"formGroup\", ctx.loginForm);\n          i0.ɵɵadvance(7);\n          i0.ɵɵclassProp(\"error\", ctx.hasError(\"agency\", \"required\") || ctx.hasError(\"agency\", \"minlength\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.formControls[\"agency\"].touched && ctx.formControls[\"agency\"].errors);\n          i0.ɵɵadvance(7);\n          i0.ɵɵclassProp(\"error\", ctx.hasError(\"password\", \"required\") || ctx.hasError(\"password\", \"minlength\"));\n          i0.ɵɵproperty(\"type\", ctx.showPassword ? \"text\" : \"password\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", !ctx.showPassword);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.showPassword);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.formControls[\"password\"].touched && ctx.formControls[\"password\"].errors);\n          i0.ɵɵadvance(4);\n          i0.ɵɵclassProp(\"loading\", ctx.isLoading);\n          i0.ɵɵproperty(\"disabled\", ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n        }\n      },\n      dependencies: [i4.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName],\n      styles: [\"\\n\\n[_ngcontent-%COMP%]:root {\\n  --primary-blue: #0ea5e9;\\n  --primary-blue-hover: #0284c7;\\n  --secondary-blue: #38bdf8;\\n  --text-dark: #1e293b;\\n  --text-light: #64748b;\\n  --text-white: #ffffff;\\n  --error-color: #ef4444;\\n  --error-bg: #fef2f2;\\n  --success-color: #10b981;\\n  --border-color: #e2e8f0;\\n  --input-bg: #f8fafc;\\n  --shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);\\n  --transition: all 0.3s ease;\\n}\\n\\n\\n\\n.login-container[_ngcontent-%COMP%] {\\n  min-height: 100vh;\\n  display: flex;\\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;\\n}\\n\\n\\n\\n.login-left[_ngcontent-%COMP%] {\\n  flex: 1;\\n  background: linear-gradient(135deg, #0ea5e9 0%, #38bdf8 50%, #0284c7 100%);\\n  position: relative;\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: space-between;\\n  padding: 3rem 2rem 2rem;\\n  color: white;\\n  overflow: hidden;\\n}\\n\\n.login-left[_ngcontent-%COMP%]::before {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: url('data:image/svg+xml,<svg xmlns=\\\"http://www.w3.org/2000/svg\\\" viewBox=\\\"0 0 100 100\\\"><defs><pattern id=\\\"grain\\\" width=\\\"100\\\" height=\\\"100\\\" patternUnits=\\\"userSpaceOnUse\\\"><circle cx=\\\"25\\\" cy=\\\"25\\\" r=\\\"1\\\" fill=\\\"white\\\" opacity=\\\"0.1\\\"/><circle cx=\\\"75\\\" cy=\\\"75\\\" r=\\\"1\\\" fill=\\\"white\\\" opacity=\\\"0.1\\\"/><circle cx=\\\"50\\\" cy=\\\"10\\\" r=\\\"0.5\\\" fill=\\\"white\\\" opacity=\\\"0.1\\\"/></pattern></defs><rect width=\\\"100\\\" height=\\\"100\\\" fill=\\\"url(%23grain)\\\"/></svg>') repeat;\\n  opacity: 0.3;\\n}\\n\\n.brand-section[_ngcontent-%COMP%] {\\n  position: relative;\\n  z-index: 2;\\n}\\n\\n.brand-title[_ngcontent-%COMP%] {\\n  font-size: 2.5rem;\\n  font-weight: 700;\\n  margin: 0 0 1rem 0;\\n  line-height: 1.2;\\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n}\\n\\n.brand-subtitle[_ngcontent-%COMP%] {\\n  font-size: 1.1rem;\\n  line-height: 1.6;\\n  opacity: 0.9;\\n  margin: 0;\\n  max-width: 400px;\\n}\\n\\n.travel-illustration[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 50%;\\n  left: 50%;\\n  transform: translate(-50%, -50%);\\n  z-index: 1;\\n}\\n\\n.travel-icon[_ngcontent-%COMP%] {\\n  width: 300px;\\n  height: 300px;\\n  opacity: 0.6;\\n  animation: _ngcontent-%COMP%_float 6s ease-in-out infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_float {\\n  0%, 100% { transform: translateY(0px); }\\n  50% { transform: translateY(-20px); }\\n}\\n\\n.monuments[_ngcontent-%COMP%] {\\n  position: relative;\\n  z-index: 2;\\n  text-align: center;\\n}\\n\\n.monument-icon[_ngcontent-%COMP%] {\\n  width: 100%;\\n  max-width: 300px;\\n  height: 80px;\\n  opacity: 0.8;\\n}\\n\\n\\n\\n.login-right[_ngcontent-%COMP%] {\\n  flex: 1;\\n  background: white;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 2rem;\\n  min-height: 100vh;\\n}\\n\\n.login-form-container[_ngcontent-%COMP%] {\\n  width: 100%;\\n  max-width: 400px;\\n}\\n\\n\\n\\n.login-header[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-bottom: 2rem;\\n}\\n\\n.plane-icon[_ngcontent-%COMP%] {\\n  width: 60px;\\n  height: 60px;\\n  margin: 0 auto 1rem;\\n  background: linear-gradient(135deg, var(--primary-blue), var(--secondary-blue));\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  color: white;\\n  box-shadow: var(--shadow);\\n}\\n\\n.plane-icon[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  width: 24px;\\n  height: 24px;\\n}\\n\\n.welcome-title[_ngcontent-%COMP%] {\\n  font-size: 2rem;\\n  font-weight: 700;\\n  color: var(--text-dark);\\n  margin: 0 0 0.5rem 0;\\n}\\n\\n.welcome-subtitle[_ngcontent-%COMP%] {\\n  color: var(--text-light);\\n  font-size: 0.875rem;\\n  margin: 0;\\n}\\n\\n\\n\\n.login-form[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 1.5rem;\\n}\\n\\n.form-group[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 0.5rem;\\n}\\n\\n.form-label[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: var(--text-dark);\\n  font-size: 0.875rem;\\n  margin-bottom: 0.25rem;\\n}\\n\\n.input-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  display: flex;\\n  align-items: center;\\n}\\n\\n.input-icon[_ngcontent-%COMP%] {\\n  position: absolute;\\n  left: 1rem;\\n  width: 1.25rem;\\n  height: 1.25rem;\\n  color: var(--text-light);\\n  z-index: 1;\\n}\\n\\n.form-input[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 0.875rem 1rem 0.875rem 3rem;\\n  border: 2px solid var(--border-color);\\n  border-radius: 0.75rem;\\n  font-size: 1rem;\\n  background: var(--input-bg);\\n  transition: var(--transition);\\n  color: var(--text-dark);\\n}\\n\\n.form-input[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  border-color: var(--primary-blue);\\n  background: white;\\n  box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.1);\\n}\\n\\n.form-input[_ngcontent-%COMP%]::placeholder {\\n  color: var(--text-light);\\n}\\n\\n.form-input.error[_ngcontent-%COMP%] {\\n  border-color: var(--error-color);\\n  background: var(--error-bg);\\n}\\n\\n\\n\\n.password-toggle[_ngcontent-%COMP%] {\\n  position: absolute;\\n  right: 1rem;\\n  background: none;\\n  border: none;\\n  cursor: pointer;\\n  color: var(--text-light);\\n  padding: 0.5rem;\\n  border-radius: 0.375rem;\\n  transition: var(--transition);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  z-index: 2;\\n}\\n\\n.password-toggle[_ngcontent-%COMP%]:hover {\\n  color: var(--text-dark);\\n}\\n\\n.password-toggle[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  color: var(--primary-blue);\\n}\\n\\n.password-toggle[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  width: 1.25rem;\\n  height: 1.25rem;\\n}\\n\\n\\n\\n.forgot-password[_ngcontent-%COMP%] {\\n  text-align: right;\\n  margin-top: -0.5rem;\\n}\\n\\n.forgot-link[_ngcontent-%COMP%] {\\n  color: var(--primary-blue);\\n  text-decoration: none;\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  transition: var(--transition);\\n}\\n\\n.forgot-link[_ngcontent-%COMP%]:hover {\\n  color: var(--primary-blue-hover);\\n  text-decoration: underline;\\n}\\n\\n\\n\\n.error-message[_ngcontent-%COMP%] {\\n  background: var(--error-bg);\\n  border: 1px solid #fecaca;\\n  color: var(--error-color);\\n  padding: 0.875rem 1rem;\\n  border-radius: 0.75rem;\\n  font-size: 0.875rem;\\n  display: flex;\\n  align-items: center;\\n  gap: 0.75rem;\\n  margin-bottom: 1rem;\\n}\\n\\n.error-icon[_ngcontent-%COMP%] {\\n  width: 1.25rem;\\n  height: 1.25rem;\\n  flex-shrink: 0;\\n}\\n\\n.error-close[_ngcontent-%COMP%] {\\n  margin-left: auto;\\n  background: none;\\n  border: none;\\n  color: var(--error-color);\\n  cursor: pointer;\\n  font-size: 1.25rem;\\n  line-height: 1;\\n  padding: 0.25rem;\\n  border-radius: 0.25rem;\\n  transition: var(--transition);\\n}\\n\\n.error-close[_ngcontent-%COMP%]:hover {\\n  background: rgba(239, 68, 68, 0.1);\\n}\\n\\n.field-error[_ngcontent-%COMP%] {\\n  color: var(--error-color);\\n  font-size: 0.75rem;\\n  margin-top: 0.25rem;\\n  font-weight: 500;\\n}\\n\\n\\n\\n.login-button[_ngcontent-%COMP%] {\\n  width: 100%;\\n  background: var(--primary-blue);\\n  color: white;\\n  border: none;\\n  padding: 1rem;\\n  border-radius: 0.75rem;\\n  font-size: 1rem;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: var(--transition);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 0.5rem;\\n  min-height: 3.25rem;\\n  text-transform: uppercase;\\n  letter-spacing: 0.025em;\\n}\\n\\n.login-button[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background: var(--primary-blue-hover);\\n  transform: translateY(-1px);\\n  box-shadow: var(--shadow);\\n}\\n\\n.login-button[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.6;\\n  cursor: not-allowed;\\n  transform: none;\\n}\\n\\n.loading-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n}\\n\\n.loading-spinner[_ngcontent-%COMP%] {\\n  width: 1.25rem;\\n  height: 1.25rem;\\n  animation: _ngcontent-%COMP%_spin 1s linear infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_spin {\\n  from { transform: rotate(0deg); }\\n  to { transform: rotate(360deg); }\\n}\\n\\n\\n\\n.login-button[_ngcontent-%COMP%] {\\n  background: var(--background-gradient);\\n  color: white;\\n  border: none;\\n  padding: 1rem 1.5rem;\\n  border-radius: 0.5rem;\\n  font-size: 1rem;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: var(--transition);\\n  position: relative;\\n  overflow: hidden;\\n  min-height: 3.25rem;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n\\n.login-button[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  transform: translateY(-2px);\\n  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);\\n}\\n\\n.login-button[_ngcontent-%COMP%]:active:not(:disabled) {\\n  transform: translateY(0);\\n}\\n\\n.login-button[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.6;\\n  cursor: not-allowed;\\n  transform: none;\\n}\\n\\n.button-content[_ngcontent-%COMP%], .loading-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 0.5rem;\\n}\\n\\n.button-icon[_ngcontent-%COMP%] {\\n  width: 1.25rem;\\n  height: 1.25rem;\\n}\\n\\n.loading-spinner[_ngcontent-%COMP%] {\\n  width: 1.25rem;\\n  height: 1.25rem;\\n  animation: _ngcontent-%COMP%_spin 1s linear infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_spin {\\n  from {\\n    transform: rotate(0deg);\\n  }\\n  to {\\n    transform: rotate(360deg);\\n  }\\n}\\n\\n\\n\\n.login-footer[_ngcontent-%COMP%] {\\n  margin-top: 2.5rem;\\n  text-align: center;\\n  border-top: 1px solid #e5e7eb;\\n  padding-top: 1.5rem;\\n}\\n\\n.footer-text[_ngcontent-%COMP%] {\\n  color: var(--secondary-color);\\n  font-size: 0.875rem;\\n  margin: 0 0 0.75rem 0;\\n}\\n\\n.footer-link[_ngcontent-%COMP%] {\\n  color: var(--primary-color);\\n  text-decoration: none;\\n  font-weight: 600;\\n  transition: var(--transition);\\n}\\n\\n.footer-link[_ngcontent-%COMP%]:hover {\\n  color: var(--primary-hover);\\n  text-decoration: underline;\\n}\\n\\n.version-info[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  color: #9ca3af;\\n  font-weight: 500;\\n}\\n\\n\\n\\n@media (max-width: 480px) {\\n  .login-container[_ngcontent-%COMP%] {\\n    padding: 0.75rem;\\n  }\\n  \\n  .login-card[_ngcontent-%COMP%] {\\n    padding: 2rem 1.5rem;\\n    border-radius: 0.5rem;\\n  }\\n  \\n  .login-title[_ngcontent-%COMP%] {\\n    font-size: 1.875rem;\\n  }\\n  \\n  .form-input[_ngcontent-%COMP%] {\\n    padding: 0.75rem;\\n    font-size: 0.875rem;\\n  }\\n  \\n  .login-button[_ngcontent-%COMP%] {\\n    padding: 0.875rem 1.25rem;\\n    font-size: 0.875rem;\\n  }\\n}\\n\\n@media (max-width: 360px) {\\n  .login-card[_ngcontent-%COMP%] {\\n    padding: 1.5rem 1rem;\\n  }\\n  \\n  .login-title[_ngcontent-%COMP%] {\\n    font-size: 1.5rem;\\n  }\\n  \\n  .login-form[_ngcontent-%COMP%] {\\n    gap: 1.5rem;\\n  }\\n}\\n\\n\\n\\n@media (prefers-reduced-motion: reduce) {\\n  .login-card[_ngcontent-%COMP%] {\\n    animation: none;\\n  }\\n  \\n  .loading-spinner[_ngcontent-%COMP%] {\\n    animation: none;\\n  }\\n  \\n  .login-button[_ngcontent-%COMP%] {\\n    transition: none;\\n  }\\n  \\n  *[_ngcontent-%COMP%] {\\n    transition: none !important;\\n  }\\n}\\n\\n\\n\\n@media (prefers-color-scheme: dark) {\\n  .login-container[_ngcontent-%COMP%] {\\n    background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);\\n  }\\n  \\n  .login-card[_ngcontent-%COMP%] {\\n    background: #1e293b;\\n    color: #f1f5f9;\\n    border: 1px solid #334155;\\n  }\\n  \\n  .login-title[_ngcontent-%COMP%] {\\n    color: #60a5fa;\\n  }\\n  \\n  .login-subtitle[_ngcontent-%COMP%], .logo-subtitle[_ngcontent-%COMP%] {\\n    color: #cbd5e1;\\n  }\\n  \\n  .form-label[_ngcontent-%COMP%] {\\n    color: #e2e8f0;\\n  }\\n  \\n  .form-input[_ngcontent-%COMP%] {\\n    background-color: #334155;\\n    border-color: #475569;\\n    color: #f1f5f9;\\n  }\\n  \\n  .form-input[_ngcontent-%COMP%]:focus {\\n    background-color: #1e293b;\\n    border-color: #60a5fa;\\n  }\\n  \\n  .footer-text[_ngcontent-%COMP%] {\\n    color: #cbd5e1;\\n  }\\n  \\n  .version-info[_ngcontent-%COMP%] {\\n    color: #64748b;\\n  }\\n}\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "i0", "ɵɵelementStart", "ɵɵnamespaceSVG", "ɵɵelement", "ɵɵelementEnd", "ɵɵnamespaceHTML", "ɵɵtext", "ɵɵlistener", "LoginComponent_div_33_Template_button_click_5_listener", "ɵɵrestoreView", "_r8", "ctx_r7", "ɵɵnextContext", "ɵɵresetView", "clearError", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r0", "errorMessage", "ɵɵtextInterpolate1", "ctx_r1", "getErrorMessage", "ctx_r4", "LoginComponent", "constructor", "formBuilder", "authService", "router", "isLoading", "showPassword", "ngOnInit", "initializeForm", "isAuthenticated", "navigate", "loginForm", "group", "agency", "required", "<PERSON><PERSON><PERSON><PERSON>", "user", "password", "formControls", "controls", "togglePasswordVisibility", "onSubmit", "valid", "authRequest", "Agency", "value", "trim", "User", "Password", "authenticate", "subscribe", "next", "response", "header", "success", "console", "log", "body", "userInfo", "handleApiError", "error", "message", "markFormGroupTouched", "messages", "length", "Object", "keys", "for<PERSON>ach", "key", "control", "get", "<PERSON><PERSON><PERSON><PERSON>ched", "<PERSON><PERSON><PERSON><PERSON>", "fieldName", "errorType", "field", "touched", "getFieldDisplayName", "<PERSON><PERSON><PERSON><PERSON>", "errors", "displayNames", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "AuthService", "i3", "Router", "selectors", "decls", "vars", "consts", "template", "LoginComponent_Template", "rf", "ctx", "ɵɵtemplate", "LoginComponent_div_33_Template", "LoginComponent_Template_form_ngSubmit_34_listener", "LoginComponent_div_42_Template", "LoginComponent_Template_button_click_50_listener", "LoginComponent__svg_svg_51_Template", "LoginComponent__svg_svg_52_Template", "LoginComponent_div_53_Template", "LoginComponent_span_58_Template", "LoginComponent_span_59_Template", "ɵɵproperty", "ɵɵclassProp"], "sources": ["C:\\Users\\<USER>\\Desktop\\angular\\front\\src\\app\\components\\login\\login.component.ts", "C:\\Users\\<USER>\\Desktop\\angular\\front\\src\\app\\components\\login\\login.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { Router } from '@angular/router';\n\nimport { AuthService } from '../../services/auth.service';\nimport { AuthRequest } from '../../models/auth-request.interface';\n\n@Component({\n  selector: 'app-login',\n  templateUrl: './login.component.html',\n  styleUrls: ['./login.component.css']\n})\nexport class LoginComponent implements OnInit {\n  loginForm!: FormGroup;\n  isLoading = false;\n  errorMessage = '';\n  showPassword = false;\n\n  constructor(\n    private formBuilder: FormBuilder,\n    private authService: AuthService,\n    private router: Router\n  ) {}\n\n  ngOnInit(): void {\n    this.initializeForm();\n    \n    // Rediriger si déjà connecté\n    if (this.authService.isAuthenticated()) {\n      this.router.navigate(['/dashboard']); // Ajustez selon votre route de destination\n    }\n  }\n\n  /**\n   * Initialise le formulaire de connexion avec les validations\n   */\n  private initializeForm(): void {\n    this.loginForm = this.formBuilder.group({\n      agency: ['', [Validators.required, Validators.minLength(2)]],\n      user: ['', [Validators.required, Validators.minLength(2)]],\n      password: ['', [Validators.required, Validators.minLength(6)]]\n    });\n  }\n\n  /**\n   * Getter pour accéder facilement aux contrôles du formulaire\n   */\n  get formControls() {\n    return this.loginForm.controls;\n  }\n\n  /**\n   * Bascule la visibilité du mot de passe\n   */\n  togglePasswordVisibility(): void {\n    this.showPassword = !this.showPassword;\n  }\n\n  /**\n   * Soumet le formulaire de connexion\n   */\n  onSubmit(): void {\n    if (this.loginForm.valid && !this.isLoading) {\n      this.isLoading = true;\n      this.errorMessage = '';\n\n      // Créer l'objet de requête avec les noms de propriétés exacts du backend\n      const authRequest: AuthRequest = {\n        Agency: this.loginForm.value.agency.trim(),\n        User: this.loginForm.value.user.trim(),\n        Password: this.loginForm.value.password\n      };\n\n      this.authService.authenticate(authRequest).subscribe({\n        next: (response) => {\n          this.isLoading = false;\n          \n          if (response.header.success) {\n            // Connexion réussie\n            console.log('Connexion réussie:', response.body.userInfo);\n            \n            // Rediriger vers le dashboard ou la page d'accueil\n            this.router.navigate(['/dashboard']); // Ajustez selon votre route\n          } else {\n            // Erreur retournée par l'API\n            this.handleApiError(response);\n          }\n        },\n        error: (error) => {\n          this.isLoading = false;\n          this.errorMessage = error.message || 'Une erreur est survenue lors de la connexion';\n          console.error('Erreur de connexion:', error);\n        }\n      });\n    } else {\n      // Marquer tous les champs comme touchés pour afficher les erreurs\n      this.markFormGroupTouched();\n    }\n  }\n\n  /**\n   * Gère les erreurs retournées par l'API\n   */\n  private handleApiError(response: any): void {\n    if (response.header.messages && response.header.messages.length > 0) {\n      // Utiliser le premier message d'erreur de l'API\n      this.errorMessage = response.header.messages[0].message;\n    } else {\n      this.errorMessage = 'Échec de l\\'authentification';\n    }\n  }\n\n  /**\n   * Marque tous les champs du formulaire comme touchés\n   */\n  private markFormGroupTouched(): void {\n    Object.keys(this.loginForm.controls).forEach(key => {\n      const control = this.loginForm.get(key);\n      control?.markAsTouched();\n    });\n  }\n\n  /**\n   * Vérifie si un champ a une erreur et a été touché\n   */\n  hasError(fieldName: string, errorType: string): boolean {\n    const field = this.loginForm.get(fieldName);\n    return !!(field?.hasError(errorType) && field?.touched);\n  }\n\n  /**\n   * Récupère le message d'erreur pour un champ\n   */\n  getErrorMessage(fieldName: string): string {\n    const field = this.loginForm.get(fieldName);\n    \n    if (field?.hasError('required')) {\n      return `${this.getFieldDisplayName(fieldName)} est requis`;\n    }\n    \n    if (field?.hasError('minlength')) {\n      const requiredLength = field.errors?.['minlength']?.requiredLength;\n      return `${this.getFieldDisplayName(fieldName)} doit contenir au moins ${requiredLength} caractères`;\n    }\n    \n    return '';\n  }\n\n  /**\n   * Retourne le nom d'affichage pour un champ\n   */\n  private getFieldDisplayName(fieldName: string): string {\n    const displayNames: { [key: string]: string } = {\n      agency: 'Le code agence',\n      user: 'Le code utilisateur',\n      password: 'Le mot de passe'\n    };\n    \n    return displayNames[fieldName] || fieldName;\n  }\n\n  /**\n   * Nettoie le message d'erreur\n   */\n  clearError(): void {\n    this.errorMessage = '';\n  }\n}\n", "<div class=\"login-container\">\n  <!-- Section gauche avec image de fond -->\n  <div class=\"login-left\">\n    <div class=\"brand-section\">\n      <h1 class=\"brand-title\">Paximum Tours</h1>\n      <p class=\"brand-subtitle\">Travel is the only purchase that enriches you in ways beyond material wealth</p>\n    </div>\n\n    <!-- Illustration de voyage -->\n    <div class=\"travel-illustration\">\n      <svg class=\"travel-icon\" viewBox=\"0 0 200 200\" fill=\"none\">\n        <!-- Avion -->\n        <path d=\"M50 100 L150 80 L160 85 L150 90 L50 110 Z\" fill=\"white\" opacity=\"0.9\"/>\n        <path d=\"M45 105 L55 100 L55 110 Z\" fill=\"white\" opacity=\"0.7\"/>\n        <!-- Nuages -->\n        <circle cx=\"40\" cy=\"60\" r=\"15\" fill=\"white\" opacity=\"0.6\"/>\n        <circle cx=\"50\" cy=\"60\" r=\"18\" fill=\"white\" opacity=\"0.6\"/>\n        <circle cx=\"60\" cy=\"60\" r=\"15\" fill=\"white\" opacity=\"0.6\"/>\n        <!-- Montagnes -->\n        <path d=\"M0 150 L30 120 L60 140 L90 110 L120 130 L150 100 L180 120 L200 110 L200 200 L0 200 Z\" fill=\"white\" opacity=\"0.3\"/>\n      </svg>\n    </div>\n\n    <!-- Monuments en bas -->\n    <div class=\"monuments\">\n      <svg class=\"monument-icon\" viewBox=\"0 0 300 100\" fill=\"none\">\n        <!-- Tour Eiffel stylisée -->\n        <path d=\"M50 90 L60 20 L70 90 M45 90 L75 90 M55 60 L65 60\" stroke=\"#0ea5e9\" stroke-width=\"2\" fill=\"none\"/>\n        <!-- Taj Mahal stylisé -->\n        <path d=\"M120 90 L120 60 Q120 50 130 50 Q140 50 140 60 L140 90 M110 90 L150 90\" stroke=\"#0ea5e9\" stroke-width=\"2\" fill=\"none\"/>\n        <circle cx=\"130\" cy=\"55\" r=\"3\" fill=\"#0ea5e9\"/>\n        <!-- Building moderne -->\n        <rect x=\"180\" y=\"70\" width=\"15\" height=\"20\" fill=\"#0ea5e9\" opacity=\"0.7\"/>\n        <rect x=\"200\" y=\"60\" width=\"15\" height=\"30\" fill=\"#0ea5e9\" opacity=\"0.8\"/>\n        <rect x=\"220\" y=\"50\" width=\"15\" height=\"40\" fill=\"#0ea5e9\"/>\n      </svg>\n    </div>\n  </div>\n\n  <!-- Section droite avec formulaire -->\n  <div class=\"login-right\">\n    <div class=\"login-form-container\">\n      <!-- Header avec avion -->\n      <div class=\"login-header\">\n        <div class=\"plane-icon\">\n          <svg viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\">\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 19l9 2-9-18-9 18 9-2zm0 0v-8\"/>\n          </svg>\n        </div>\n        <h2 class=\"welcome-title\">Welcome</h2>\n        <p class=\"welcome-subtitle\">Login with Email</p>\n      </div>\n\n      <!-- Message d'erreur global -->\n      <div *ngIf=\"errorMessage\" class=\"error-message\" role=\"alert\">\n        <svg class=\"error-icon\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n          <path fill-rule=\"evenodd\" d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z\" clip-rule=\"evenodd\"></path>\n        </svg>\n        <span>{{ errorMessage }}</span>\n        <button type=\"button\" class=\"error-close\" (click)=\"clearError()\">×</button>\n      </div>\n\n      <!-- Formulaire -->\n      <form [formGroup]=\"loginForm\" (ngSubmit)=\"onSubmit()\" class=\"login-form\" novalidate>\n\n        <!-- Champ Email/Agency -->\n        <div class=\"form-group\">\n          <label for=\"agency\" class=\"form-label\">Email/Agency</label>\n          <div class=\"input-container\">\n            <svg class=\"input-icon\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207\"/>\n            </svg>\n            <input\n              type=\"text\"\n              id=\"agency\"\n              formControlName=\"agency\"\n              class=\"form-input\"\n              [class.error]=\"hasError('agency', 'required') || hasError('agency', 'minlength')\"\n              placeholder=\"Enter your email\"\n              autocomplete=\"email\"\n            >\n          </div>\n          <div *ngIf=\"formControls['agency'].touched && formControls['agency'].errors\" class=\"field-error\">\n            {{ getErrorMessage('agency') }}\n          </div>\n        </div>\n\n        <!-- Champ Password -->\n        <div class=\"form-group\">\n          <label for=\"password\" class=\"form-label\">Password</label>\n          <div class=\"input-container\">\n            <svg class=\"input-icon\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z\"/>\n            </svg>\n            <input\n              [type]=\"showPassword ? 'text' : 'password'\"\n              id=\"password\"\n              formControlName=\"password\"\n              class=\"form-input\"\n              [class.error]=\"hasError('password', 'required') || hasError('password', 'minlength')\"\n              placeholder=\"••••••••••••\"\n              autocomplete=\"current-password\"\n            >\n            <button\n              type=\"button\"\n              class=\"password-toggle\"\n              (click)=\"togglePasswordVisibility()\"\n            >\n              <svg *ngIf=\"!showPassword\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"/>\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z\"/>\n              </svg>\n              <svg *ngIf=\"showPassword\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21\"/>\n              </svg>\n            </button>\n          </div>\n          <div *ngIf=\"formControls['password'].touched && formControls['password'].errors\" class=\"field-error\">\n            {{ getErrorMessage('password') }}\n          </div>\n        </div>\n\n        <!-- Forgot password -->\n        <div class=\"forgot-password\">\n          <a href=\"#\" class=\"forgot-link\">Forgot your password?</a>\n        </div>\n\n        <!-- Bouton LOGIN -->\n        <button\n          type=\"submit\"\n          class=\"login-button\"\n          [disabled]=\"isLoading\"\n          [class.loading]=\"isLoading\"\n        >\n          <span *ngIf=\"!isLoading\">LOGIN</span>\n          <span *ngIf=\"isLoading\" class=\"loading-content\">\n            <svg class=\"loading-spinner\" viewBox=\"0 0 24 24\">\n              <circle cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" stroke-width=\"4\" fill=\"none\" opacity=\"0.25\"/>\n              <path fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\" opacity=\"0.75\"/>\n            </svg>\n            Loading...\n          </span>\n        </button>\n\n        <!-- Divider -->\n        <div class=\"divider\">\n          <span>OR</span>\n        </div>\n\n        <!-- Social login buttons -->\n        <div class=\"social-login\">\n          <button type=\"button\" class=\"social-btn google-btn\">\n            <svg viewBox=\"0 0 24 24\">\n              <path fill=\"#4285F4\" d=\"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z\"/>\n              <path fill=\"#34A853\" d=\"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z\"/>\n              <path fill=\"#FBBC05\" d=\"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z\"/>\n              <path fill=\"#EA4335\" d=\"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z\"/>\n            </svg>\n          </button>\n\n          <button type=\"button\" class=\"social-btn facebook-btn\">\n            <svg viewBox=\"0 0 24 24\" fill=\"#1877F2\">\n              <path d=\"M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z\"/>\n            </svg>\n          </button>\n\n          <button type=\"button\" class=\"social-btn apple-btn\">\n            <svg viewBox=\"0 0 24 24\" fill=\"#000\">\n              <path d=\"M12.017 0C8.396 0 8.025.044 8.025.044c0 .467.02.94.058 1.4.037.46.094.92.17 1.374.076.454.17.9.282 1.336.112.436.25.86.413 1.27.163.41.35.8.563 1.17.213.37.45.72.712 1.05.262.33.55.64.862.93.312.29.65.56 1.012.81.362.25.75.48 1.162.69.412.21.85.4 1.312.57.462.17.95.32 1.462.45.512.13 1.05.24 1.612.33.562.09 1.15.16 1.762.21.612.05 1.25.08 1.912.09h.08c.662-.01 1.3-.04 1.912-.09.612-.05 1.2-.12 1.762-.21.562-.09 1.1-.2 1.612-.33.512-.13 1-.28 1.462-.45.462-.17.9-.36 1.312-.57.412-.21.8-.44 1.162-.69.362-.25.7-.52 1.012-.81.312-.29.6-.6.862-.93.262-.33.499-.68.712-1.05.213-.37.4-.76.563-1.17.163-.41.301-.834.413-1.27.112-.436.206-.882.282-1.336.076-.454.133-.914.17-1.374.037-.46.058-.933.058-1.4 0 0-.371-.044-3.992-.044z\"/>\n            </svg>\n          </button>\n        </div>\n\n        <!-- Register link -->\n        <div class=\"register-link\">\n          <span>Don't have account? </span>\n          <a href=\"#\" class=\"register-btn\">Register Now</a>\n        </div>\n      </form>\n    </div>\n  </div>\n</div>\n"], "mappings": "AACA,SAAiCA,UAAU,QAAQ,gBAAgB;;;;;;;;;ICqD7DC,EAAA,CAAAC,cAAA,cAA6D;IAC3DD,EAAA,CAAAE,cAAA,EAAgE;IAAhEF,EAAA,CAAAC,cAAA,cAAgE;IAC9DD,EAAA,CAAAG,SAAA,eAA2K;IAC7KH,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAK,eAAA,EAAM;IAANL,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAM,MAAA,GAAkB;IAAAN,EAAA,CAAAI,YAAA,EAAO;IAC/BJ,EAAA,CAAAC,cAAA,iBAAiE;IAAvBD,EAAA,CAAAO,UAAA,mBAAAC,uDAAA;MAAAR,EAAA,CAAAS,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAX,EAAA,CAAAY,aAAA;MAAA,OAASZ,EAAA,CAAAa,WAAA,CAAAF,MAAA,CAAAG,UAAA,EAAY;IAAA,EAAC;IAACd,EAAA,CAAAM,MAAA,aAAC;IAAAN,EAAA,CAAAI,YAAA,EAAS;;;;IADrEJ,EAAA,CAAAe,SAAA,GAAkB;IAAlBf,EAAA,CAAAgB,iBAAA,CAAAC,MAAA,CAAAC,YAAA,CAAkB;;;;;IAwBtBlB,EAAA,CAAAC,cAAA,cAAiG;IAC/FD,EAAA,CAAAM,MAAA,GACF;IAAAN,EAAA,CAAAI,YAAA,EAAM;;;;IADJJ,EAAA,CAAAe,SAAA,GACF;IADEf,EAAA,CAAAmB,kBAAA,MAAAC,MAAA,CAAAC,eAAA,gBACF;;;;;IAwBIrB,EAAA,CAAAE,cAAA,EAAiF;IAAjFF,EAAA,CAAAC,cAAA,cAAiF;IAC/ED,EAAA,CAAAG,SAAA,eAA4G;IAE9GH,EAAA,CAAAI,YAAA,EAAM;;;;;IACNJ,EAAA,CAAAE,cAAA,EAAgF;IAAhFF,EAAA,CAAAC,cAAA,cAAgF;IAC9ED,EAAA,CAAAG,SAAA,eAA+P;IACjQH,EAAA,CAAAI,YAAA,EAAM;;;;;IAGVJ,EAAA,CAAAC,cAAA,cAAqG;IACnGD,EAAA,CAAAM,MAAA,GACF;IAAAN,EAAA,CAAAI,YAAA,EAAM;;;;IADJJ,EAAA,CAAAe,SAAA,GACF;IADEf,EAAA,CAAAmB,kBAAA,MAAAG,MAAA,CAAAD,eAAA,kBACF;;;;;IAeArB,EAAA,CAAAC,cAAA,WAAyB;IAAAD,EAAA,CAAAM,MAAA,YAAK;IAAAN,EAAA,CAAAI,YAAA,EAAO;;;;;IACrCJ,EAAA,CAAAC,cAAA,eAAgD;IAC9CD,EAAA,CAAAE,cAAA,EAAiD;IAAjDF,EAAA,CAAAC,cAAA,cAAiD;IAC/CD,EAAA,CAAAG,SAAA,iBAAkG;IAEpGH,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAM,MAAA,mBACF;IAAAN,EAAA,CAAAI,YAAA,EAAO;;;ADjIjB,OAAM,MAAOmB,cAAc;EAMzBC,YACUC,WAAwB,EACxBC,WAAwB,EACxBC,MAAc;IAFd,KAAAF,WAAW,GAAXA,WAAW;IACX,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IAPhB,KAAAC,SAAS,GAAG,KAAK;IACjB,KAAAV,YAAY,GAAG,EAAE;IACjB,KAAAW,YAAY,GAAG,KAAK;EAMjB;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,cAAc,EAAE;IAErB;IACA,IAAI,IAAI,CAACL,WAAW,CAACM,eAAe,EAAE,EAAE;MACtC,IAAI,CAACL,MAAM,CAACM,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;;EAE1C;EAEA;;;EAGQF,cAAcA,CAAA;IACpB,IAAI,CAACG,SAAS,GAAG,IAAI,CAACT,WAAW,CAACU,KAAK,CAAC;MACtCC,MAAM,EAAE,CAAC,EAAE,EAAE,CAACrC,UAAU,CAACsC,QAAQ,EAAEtC,UAAU,CAACuC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5DC,IAAI,EAAE,CAAC,EAAE,EAAE,CAACxC,UAAU,CAACsC,QAAQ,EAAEtC,UAAU,CAACuC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1DE,QAAQ,EAAE,CAAC,EAAE,EAAE,CAACzC,UAAU,CAACsC,QAAQ,EAAEtC,UAAU,CAACuC,SAAS,CAAC,CAAC,CAAC,CAAC;KAC9D,CAAC;EACJ;EAEA;;;EAGA,IAAIG,YAAYA,CAAA;IACd,OAAO,IAAI,CAACP,SAAS,CAACQ,QAAQ;EAChC;EAEA;;;EAGAC,wBAAwBA,CAAA;IACtB,IAAI,CAACd,YAAY,GAAG,CAAC,IAAI,CAACA,YAAY;EACxC;EAEA;;;EAGAe,QAAQA,CAAA;IACN,IAAI,IAAI,CAACV,SAAS,CAACW,KAAK,IAAI,CAAC,IAAI,CAACjB,SAAS,EAAE;MAC3C,IAAI,CAACA,SAAS,GAAG,IAAI;MACrB,IAAI,CAACV,YAAY,GAAG,EAAE;MAEtB;MACA,MAAM4B,WAAW,GAAgB;QAC/BC,MAAM,EAAE,IAAI,CAACb,SAAS,CAACc,KAAK,CAACZ,MAAM,CAACa,IAAI,EAAE;QAC1CC,IAAI,EAAE,IAAI,CAAChB,SAAS,CAACc,KAAK,CAACT,IAAI,CAACU,IAAI,EAAE;QACtCE,QAAQ,EAAE,IAAI,CAACjB,SAAS,CAACc,KAAK,CAACR;OAChC;MAED,IAAI,CAACd,WAAW,CAAC0B,YAAY,CAACN,WAAW,CAAC,CAACO,SAAS,CAAC;QACnDC,IAAI,EAAGC,QAAQ,IAAI;UACjB,IAAI,CAAC3B,SAAS,GAAG,KAAK;UAEtB,IAAI2B,QAAQ,CAACC,MAAM,CAACC,OAAO,EAAE;YAC3B;YACAC,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEJ,QAAQ,CAACK,IAAI,CAACC,QAAQ,CAAC;YAEzD;YACA,IAAI,CAAClC,MAAM,CAACM,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;WACvC,MAAM;YACL;YACA,IAAI,CAAC6B,cAAc,CAACP,QAAQ,CAAC;;QAEjC,CAAC;QACDQ,KAAK,EAAGA,KAAK,IAAI;UACf,IAAI,CAACnC,SAAS,GAAG,KAAK;UACtB,IAAI,CAACV,YAAY,GAAG6C,KAAK,CAACC,OAAO,IAAI,8CAA8C;UACnFN,OAAO,CAACK,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;QAC9C;OACD,CAAC;KACH,MAAM;MACL;MACA,IAAI,CAACE,oBAAoB,EAAE;;EAE/B;EAEA;;;EAGQH,cAAcA,CAACP,QAAa;IAClC,IAAIA,QAAQ,CAACC,MAAM,CAACU,QAAQ,IAAIX,QAAQ,CAACC,MAAM,CAACU,QAAQ,CAACC,MAAM,GAAG,CAAC,EAAE;MACnE;MACA,IAAI,CAACjD,YAAY,GAAGqC,QAAQ,CAACC,MAAM,CAACU,QAAQ,CAAC,CAAC,CAAC,CAACF,OAAO;KACxD,MAAM;MACL,IAAI,CAAC9C,YAAY,GAAG,8BAA8B;;EAEtD;EAEA;;;EAGQ+C,oBAAoBA,CAAA;IAC1BG,MAAM,CAACC,IAAI,CAAC,IAAI,CAACnC,SAAS,CAACQ,QAAQ,CAAC,CAAC4B,OAAO,CAACC,GAAG,IAAG;MACjD,MAAMC,OAAO,GAAG,IAAI,CAACtC,SAAS,CAACuC,GAAG,CAACF,GAAG,CAAC;MACvCC,OAAO,EAAEE,aAAa,EAAE;IAC1B,CAAC,CAAC;EACJ;EAEA;;;EAGAC,QAAQA,CAACC,SAAiB,EAAEC,SAAiB;IAC3C,MAAMC,KAAK,GAAG,IAAI,CAAC5C,SAAS,CAACuC,GAAG,CAACG,SAAS,CAAC;IAC3C,OAAO,CAAC,EAAEE,KAAK,EAAEH,QAAQ,CAACE,SAAS,CAAC,IAAIC,KAAK,EAAEC,OAAO,CAAC;EACzD;EAEA;;;EAGA1D,eAAeA,CAACuD,SAAiB;IAC/B,MAAME,KAAK,GAAG,IAAI,CAAC5C,SAAS,CAACuC,GAAG,CAACG,SAAS,CAAC;IAE3C,IAAIE,KAAK,EAAEH,QAAQ,CAAC,UAAU,CAAC,EAAE;MAC/B,OAAO,GAAG,IAAI,CAACK,mBAAmB,CAACJ,SAAS,CAAC,aAAa;;IAG5D,IAAIE,KAAK,EAAEH,QAAQ,CAAC,WAAW,CAAC,EAAE;MAChC,MAAMM,cAAc,GAAGH,KAAK,CAACI,MAAM,GAAG,WAAW,CAAC,EAAED,cAAc;MAClE,OAAO,GAAG,IAAI,CAACD,mBAAmB,CAACJ,SAAS,CAAC,2BAA2BK,cAAc,aAAa;;IAGrG,OAAO,EAAE;EACX;EAEA;;;EAGQD,mBAAmBA,CAACJ,SAAiB;IAC3C,MAAMO,YAAY,GAA8B;MAC9C/C,MAAM,EAAE,gBAAgB;MACxBG,IAAI,EAAE,qBAAqB;MAC3BC,QAAQ,EAAE;KACX;IAED,OAAO2C,YAAY,CAACP,SAAS,CAAC,IAAIA,SAAS;EAC7C;EAEA;;;EAGA9D,UAAUA,CAAA;IACR,IAAI,CAACI,YAAY,GAAG,EAAE;EACxB;;;uBA1JWK,cAAc,EAAAvB,EAAA,CAAAoF,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAtF,EAAA,CAAAoF,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAAxF,EAAA,CAAAoF,iBAAA,CAAAK,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAdnE,cAAc;MAAAoE,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCZ3BjG,EAAA,CAAAC,cAAA,aAA6B;UAICD,EAAA,CAAAM,MAAA,oBAAa;UAAAN,EAAA,CAAAI,YAAA,EAAK;UAC1CJ,EAAA,CAAAC,cAAA,WAA0B;UAAAD,EAAA,CAAAM,MAAA,mFAA4E;UAAAN,EAAA,CAAAI,YAAA,EAAI;UAI5GJ,EAAA,CAAAC,cAAA,aAAiC;UAC/BD,EAAA,CAAAE,cAAA,EAA2D;UAA3DF,EAAA,CAAAC,cAAA,aAA2D;UAEzDD,EAAA,CAAAG,SAAA,cAAgF;UAQlFH,EAAA,CAAAI,YAAA,EAAM;UAIRJ,EAAA,CAAAK,eAAA,EAAuB;UAAvBL,EAAA,CAAAC,cAAA,eAAuB;UACrBD,EAAA,CAAAE,cAAA,EAA6D;UAA7DF,EAAA,CAAAC,cAAA,eAA6D;UAE3DD,EAAA,CAAAG,SAAA,gBAA0G;UAQ5GH,EAAA,CAAAI,YAAA,EAAM;UAKVJ,EAAA,CAAAK,eAAA,EAAyB;UAAzBL,EAAA,CAAAC,cAAA,eAAyB;UAKjBD,EAAA,CAAAE,cAAA,EAA2D;UAA3DF,EAAA,CAAAC,cAAA,eAA2D;UACzDD,EAAA,CAAAG,SAAA,gBAA4G;UAC9GH,EAAA,CAAAI,YAAA,EAAM;UAERJ,EAAA,CAAAK,eAAA,EAA0B;UAA1BL,EAAA,CAAAC,cAAA,cAA0B;UAAAD,EAAA,CAAAM,MAAA,eAAO;UAAAN,EAAA,CAAAI,YAAA,EAAK;UACtCJ,EAAA,CAAAC,cAAA,aAA4B;UAAAD,EAAA,CAAAM,MAAA,wBAAgB;UAAAN,EAAA,CAAAI,YAAA,EAAI;UAIlDJ,EAAA,CAAAmG,UAAA,KAAAC,8BAAA,kBAMM;UAGNpG,EAAA,CAAAC,cAAA,gBAAoF;UAAtDD,EAAA,CAAAO,UAAA,sBAAA8F,kDAAA;YAAA,OAAYH,GAAA,CAAAtD,QAAA,EAAU;UAAA,EAAC;UAGnD5C,EAAA,CAAAC,cAAA,eAAwB;UACiBD,EAAA,CAAAM,MAAA,oBAAY;UAAAN,EAAA,CAAAI,YAAA,EAAQ;UAC3DJ,EAAA,CAAAC,cAAA,eAA6B;UAC3BD,EAAA,CAAAE,cAAA,EAA8E;UAA9EF,EAAA,CAAAC,cAAA,eAA8E;UAC5ED,EAAA,CAAAG,SAAA,gBAAyL;UAC3LH,EAAA,CAAAI,YAAA,EAAM;UACNJ,EAAA,CAAAK,eAAA,EAQC;UARDL,EAAA,CAAAG,SAAA,iBAQC;UACHH,EAAA,CAAAI,YAAA,EAAM;UACNJ,EAAA,CAAAmG,UAAA,KAAAG,8BAAA,kBAEM;UACRtG,EAAA,CAAAI,YAAA,EAAM;UAGNJ,EAAA,CAAAC,cAAA,eAAwB;UACmBD,EAAA,CAAAM,MAAA,gBAAQ;UAAAN,EAAA,CAAAI,YAAA,EAAQ;UACzDJ,EAAA,CAAAC,cAAA,eAA6B;UAC3BD,EAAA,CAAAE,cAAA,EAA8E;UAA9EF,EAAA,CAAAC,cAAA,eAA8E;UAC5ED,EAAA,CAAAG,SAAA,gBAAgL;UAClLH,EAAA,CAAAI,YAAA,EAAM;UACNJ,EAAA,CAAAK,eAAA,EAQC;UARDL,EAAA,CAAAG,SAAA,iBAQC;UACDH,EAAA,CAAAC,cAAA,kBAIC;UADCD,EAAA,CAAAO,UAAA,mBAAAgG,iDAAA;YAAA,OAASL,GAAA,CAAAvD,wBAAA,EAA0B;UAAA,EAAC;UAEpC3C,EAAA,CAAAmG,UAAA,KAAAK,mCAAA,kBAGM;UACNxG,EAAA,CAAAmG,UAAA,KAAAM,mCAAA,kBAEM;UACRzG,EAAA,CAAAI,YAAA,EAAS;UAEXJ,EAAA,CAAAmG,UAAA,KAAAO,8BAAA,kBAEM;UACR1G,EAAA,CAAAI,YAAA,EAAM;UAGNJ,EAAA,CAAAC,cAAA,eAA6B;UACKD,EAAA,CAAAM,MAAA,6BAAqB;UAAAN,EAAA,CAAAI,YAAA,EAAI;UAI3DJ,EAAA,CAAAC,cAAA,kBAKC;UACCD,EAAA,CAAAmG,UAAA,KAAAQ,+BAAA,mBAAqC;UACrC3G,EAAA,CAAAmG,UAAA,KAAAS,+BAAA,mBAMO;UACT5G,EAAA,CAAAI,YAAA,EAAS;UAGTJ,EAAA,CAAAC,cAAA,eAAqB;UACbD,EAAA,CAAAM,MAAA,UAAE;UAAAN,EAAA,CAAAI,YAAA,EAAO;UAIjBJ,EAAA,CAAAC,cAAA,eAA0B;UAEtBD,EAAA,CAAAE,cAAA,EAAyB;UAAzBF,EAAA,CAAAC,cAAA,eAAyB;UACvBD,EAAA,CAAAG,SAAA,gBAAkJ;UAIpJH,EAAA,CAAAI,YAAA,EAAM;UAGRJ,EAAA,CAAAK,eAAA,EAAsD;UAAtDL,EAAA,CAAAC,cAAA,kBAAsD;UACpDD,EAAA,CAAAE,cAAA,EAAwC;UAAxCF,EAAA,CAAAC,cAAA,eAAwC;UACtCD,EAAA,CAAAG,SAAA,gBAA0S;UAC5SH,EAAA,CAAAI,YAAA,EAAM;UAGRJ,EAAA,CAAAK,eAAA,EAAmD;UAAnDL,EAAA,CAAAC,cAAA,kBAAmD;UACjDD,EAAA,CAAAE,cAAA,EAAqC;UAArCF,EAAA,CAAAC,cAAA,eAAqC;UACnCD,EAAA,CAAAG,SAAA,gBAAouB;UACtuBH,EAAA,CAAAI,YAAA,EAAM;UAKVJ,EAAA,CAAAK,eAAA,EAA2B;UAA3BL,EAAA,CAAAC,cAAA,eAA2B;UACnBD,EAAA,CAAAM,MAAA,4BAAoB;UAAAN,EAAA,CAAAI,YAAA,EAAO;UACjCJ,EAAA,CAAAC,cAAA,aAAiC;UAAAD,EAAA,CAAAM,MAAA,oBAAY;UAAAN,EAAA,CAAAI,YAAA,EAAI;;;UA1H/CJ,EAAA,CAAAe,SAAA,IAAkB;UAAlBf,EAAA,CAAA6G,UAAA,SAAAX,GAAA,CAAAhF,YAAA,CAAkB;UASlBlB,EAAA,CAAAe,SAAA,GAAuB;UAAvBf,EAAA,CAAA6G,UAAA,cAAAX,GAAA,CAAAhE,SAAA,CAAuB;UAcrBlC,EAAA,CAAAe,SAAA,GAAiF;UAAjFf,EAAA,CAAA8G,WAAA,UAAAZ,GAAA,CAAAvB,QAAA,0BAAAuB,GAAA,CAAAvB,QAAA,wBAAiF;UAK/E3E,EAAA,CAAAe,SAAA,GAAqE;UAArEf,EAAA,CAAA6G,UAAA,SAAAX,GAAA,CAAAzD,YAAA,WAAAsC,OAAA,IAAAmB,GAAA,CAAAzD,YAAA,WAAAyC,MAAA,CAAqE;UAiBvElF,EAAA,CAAAe,SAAA,GAAqF;UAArFf,EAAA,CAAA8G,WAAA,UAAAZ,GAAA,CAAAvB,QAAA,4BAAAuB,GAAA,CAAAvB,QAAA,0BAAqF;UAJrF3E,EAAA,CAAA6G,UAAA,SAAAX,GAAA,CAAArE,YAAA,uBAA2C;UAarC7B,EAAA,CAAAe,SAAA,GAAmB;UAAnBf,EAAA,CAAA6G,UAAA,UAAAX,GAAA,CAAArE,YAAA,CAAmB;UAInB7B,EAAA,CAAAe,SAAA,GAAkB;UAAlBf,EAAA,CAAA6G,UAAA,SAAAX,GAAA,CAAArE,YAAA,CAAkB;UAKtB7B,EAAA,CAAAe,SAAA,GAAyE;UAAzEf,EAAA,CAAA6G,UAAA,SAAAX,GAAA,CAAAzD,YAAA,aAAAsC,OAAA,IAAAmB,GAAA,CAAAzD,YAAA,aAAAyC,MAAA,CAAyE;UAe/ElF,EAAA,CAAAe,SAAA,GAA2B;UAA3Bf,EAAA,CAAA8G,WAAA,YAAAZ,GAAA,CAAAtE,SAAA,CAA2B;UAD3B5B,EAAA,CAAA6G,UAAA,aAAAX,GAAA,CAAAtE,SAAA,CAAsB;UAGf5B,EAAA,CAAAe,SAAA,GAAgB;UAAhBf,EAAA,CAAA6G,UAAA,UAAAX,GAAA,CAAAtE,SAAA,CAAgB;UAChB5B,EAAA,CAAAe,SAAA,GAAe;UAAff,EAAA,CAAA6G,UAAA,SAAAX,GAAA,CAAAtE,SAAA,CAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}