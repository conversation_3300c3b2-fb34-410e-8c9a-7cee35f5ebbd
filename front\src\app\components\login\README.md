# Composant Login

## Description

Composant de connexion responsable pour l'application Paximum. Ce composant permet aux utilisateurs de s'authentifier en utilisant leur code agence, code utilisateur et mot de passe.

## Fonctionnalités

- **Design responsable** : S'adapte à tous les types d'écrans (mobile, tablette, desktop)
- **Validation de formulaire** : Validation en temps réel avec messages d'erreur
- **Gestion des erreurs** : Affichage des erreurs d'authentification
- **Indicateur de chargement** : Feedback visuel pendant l'authentification
- **Accessibilité** : Conforme aux standards d'accessibilité web
- **Mode sombre** : Support automatique du mode sombre
- **Sécurité** : Masquage/affichage du mot de passe

## Structure des fichiers

```
components/login/
├── login.component.ts      # Logique du composant
├── login.component.html    # Template HTML
├── login.component.css     # Styles CSS
├── login.component.spec.ts # Tests unitaires
└── README.md              # Documentation
```

## Utilisation

### Intégration dans le module

```typescript
import { LoginComponent } from './components/login/login.component';

@NgModule({
  declarations: [
    LoginComponent
  ],
  // ...
})
```

### Configuration des routes

```typescript
const routes: Routes = [
  { path: 'login', component: LoginComponent },
  // ...
];
```

### Utilisation dans un template

```html
<app-login></app-login>
```

## Configuration

### Variables d'environnement

Configurez l'URL de votre backend dans les fichiers d'environnement :

```typescript
// environment.ts
export const environment = {
  production: false,
  apiUrl: 'http://localhost:8080',
  authEndpoint: '/auth/login'
};
```

### Backend requis

Le composant s'attend à recevoir une réponse du backend au format :

```typescript
interface AuthResponse {
  header: {
    requestId: string;
    success: boolean;
    messages: Array<{
      id: number;
      code: string;
      messageType: number;
      message: string;
    }>;
  };
  body: {
    token: string;
    expiresOn: string;
    tokenId: number;
    userInfo: UserInfo;
  };
}
```

## Validation

- **Code Agence** : Requis, minimum 2 caractères
- **Code Utilisateur** : Requis, minimum 2 caractères  
- **Mot de passe** : Requis, minimum 6 caractères

## Gestion des erreurs

Le composant gère plusieurs types d'erreurs :

- Erreurs de validation de formulaire
- Erreurs de connexion réseau
- Erreurs d'authentification du backend
- Erreurs de timeout

## Responsive Design

Le composant s'adapte automatiquement aux différentes tailles d'écran :

- **Mobile** (< 480px) : Layout optimisé pour mobile
- **Tablette** (480px - 768px) : Layout intermédiaire
- **Desktop** (> 768px) : Layout complet

## Accessibilité

- Support des lecteurs d'écran
- Navigation au clavier
- Contrastes de couleurs conformes WCAG
- Labels et descriptions appropriés
- Focus management

## Tests

Exécuter les tests unitaires :

```bash
ng test
```

Les tests couvrent :
- Initialisation du composant
- Validation des formulaires
- Soumission des données
- Gestion des erreurs
- Navigation

## Personnalisation

### Styles

Les styles peuvent être personnalisés en modifiant les variables CSS dans `login.component.css` :

```css
:root {
  --primary-color: #2563eb;
  --primary-hover: #1d4ed8;
  --error-color: #dc2626;
  /* ... */
}
```

### Messages

Les messages d'erreur peuvent être personnalisés dans le composant TypeScript.

## Dépendances

- Angular 16+
- Angular Reactive Forms
- Angular HTTP Client
- RxJS

## Support navigateurs

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+
