{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./app.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./app.component.css?ngResource\";\nimport { Component } from '@angular/core';\nexport let AppComponent = class AppComponent {\n  constructor() {\n    this.title = 'front';\n  }\n};\nAppComponent = __decorate([Component({\n  selector: 'app-root',\n  template: __NG_CLI_RESOURCE__0,\n  styles: [__NG_CLI_RESOURCE__1]\n})], AppComponent);", "map": {"version": 3, "names": ["Component", "AppComponent", "constructor", "title", "__decorate", "selector", "template", "__NG_CLI_RESOURCE__0"], "sources": ["C:\\Users\\<USER>\\Desktop\\angular\\front\\src\\app\\app.component.ts"], "sourcesContent": ["import { Component } from '@angular/core';\n\n@Component({\n  selector: 'app-root',\n  templateUrl: './app.component.html',\n  styleUrls: ['./app.component.css']\n})\nexport class AppComponent {\n  title = 'front';\n}\n"], "mappings": ";;;AAAA,SAASA,SAAS,QAAQ,eAAe;AAOlC,WAAMC,YAAY,GAAlB,MAAMA,YAAY;EAAlBC,YAAA;IACL,KAAAC,KAAK,GAAG,OAAO;EACjB;CAAC;AAFYF,YAAY,GAAAG,UAAA,EALxBJ,SAAS,CAAC;EACTK,QAAQ,EAAE,UAAU;EACpBC,QAAA,EAAAC,oBAAmC;;CAEpC,CAAC,C,EACWN,YAAY,CAExB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}