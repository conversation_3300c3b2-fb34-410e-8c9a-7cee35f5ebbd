<div class="login-container">
  <div class="login-card">
    <!-- Header -->
    <div class="login-header">
      <div class="logo-container">
        <h1 class="login-title">Paximum</h1>
        <div class="logo-subtitle">Système d'authentification</div>
      </div>
      <p class="login-subtitle">Connectez-vous à votre compte</p>
    </div>

    <!-- Formulaire -->
    <form [formGroup]="loginForm" (ngSubmit)="onSubmit()" class="login-form" novalidate>
      
      <!-- Message d'erreur global -->
      <div *ngIf="errorMessage" class="error-message" role="alert">
        <svg class="error-icon" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
          <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
        </svg>
        <span>{{ errorMessage }}</span>
        <button type="button" class="error-close" (click)="clearError()" aria-label="Fermer le message d'erreur">
          <svg fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
          </svg>
        </button>
      </div>

      <!-- Champ Code Agence -->
      <div class="form-group">
        <label for="agency" class="form-label">
          <svg class="label-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
          </svg>
          Code Agence
        </label>
        <input
          type="text"
          id="agency"
          formControlName="agency"
          class="form-input"
          [class.error]="hasError('agency', 'required') || hasError('agency', 'minlength')"
          placeholder="Entrez le code de votre agence"
          autocomplete="organization"
          maxlength="50"
        >
        <div *ngIf="formControls['agency'].touched && formControls['agency'].errors" class="field-error">
          {{ getErrorMessage('agency') }}
        </div>
      </div>

      <!-- Champ Code Utilisateur -->
      <div class="form-group">
        <label for="user" class="form-label">
          <svg class="label-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
          </svg>
          Code Utilisateur
        </label>
        <input
          type="text"
          id="user"
          formControlName="user"
          class="form-input"
          [class.error]="hasError('user', 'required') || hasError('user', 'minlength')"
          placeholder="Entrez votre code utilisateur"
          autocomplete="username"
          maxlength="50"
        >
        <div *ngIf="formControls['user'].touched && formControls['user'].errors" class="field-error">
          {{ getErrorMessage('user') }}
        </div>
      </div>

      <!-- Champ Mot de passe -->
      <div class="form-group">
        <label for="password" class="form-label">
          <svg class="label-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
          </svg>
          Mot de passe
        </label>
        <div class="password-input-container">
          <input
            [type]="showPassword ? 'text' : 'password'"
            id="password"
            formControlName="password"
            class="form-input password-input"
            [class.error]="hasError('password', 'required') || hasError('password', 'minlength')"
            placeholder="Entrez votre mot de passe"
            autocomplete="current-password"
            maxlength="100"
          >
          <button
            type="button"
            class="password-toggle"
            (click)="togglePasswordVisibility()"
            [attr.aria-label]="showPassword ? 'Masquer le mot de passe' : 'Afficher le mot de passe'"
            tabindex="0"
          >
            <svg *ngIf="!showPassword" class="password-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
            </svg>
            <svg *ngIf="showPassword" class="password-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"></path>
            </svg>
          </button>
        </div>
        <div *ngIf="formControls['password'].touched && formControls['password'].errors" class="field-error">
          {{ getErrorMessage('password') }}
        </div>
      </div>

      <!-- Bouton de connexion -->
      <button
        type="submit"
        class="login-button"
        [disabled]="isLoading || loginForm.invalid"
        [class.loading]="isLoading"
      >
        <span *ngIf="!isLoading" class="button-content">
          <svg class="button-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1"></path>
          </svg>
          Se connecter
        </span>
        <span *ngIf="isLoading" class="loading-content">
          <svg class="loading-spinner" viewBox="0 0 24 24">
            <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4" fill="none" opacity="0.25"></circle>
            <path fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" opacity="0.75"></path>
          </svg>
          Connexion en cours...
        </span>
      </button>
    </form>

    <!-- Footer -->
    <div class="login-footer">
      <p class="footer-text">
        Besoin d'aide ? 
        <a href="mailto:<EMAIL>" class="footer-link">Contactez le support</a>
      </p>
      <div class="version-info">
        Version 1.0.0
      </div>
    </div>
  </div>
</div>
