{"ast": null, "code": "'use strict';\n\n/**\n * @license Angular v<unknown>\n * (c) 2010-2022 Google LLC. https://angular.io/\n * License: MIT\n */\n/**\n * @fileoverview\n * @suppress {globalThis}\n */\nconst NEWLINE = '\\n';\nconst IGNORE_FRAMES = {};\nconst creationTrace = '__creationTrace__';\nconst ERROR_TAG = 'STACKTRACE TRACKING';\nconst SEP_TAG = '__SEP_TAG__';\nlet sepTemplate = SEP_TAG + '@[native]';\nclass LongStackTrace {\n  constructor() {\n    this.error = getStacktrace();\n    this.timestamp = new Date();\n  }\n}\nfunction getStacktraceWithUncaughtError() {\n  return new Error(ERROR_TAG);\n}\nfunction getStacktraceWithCaughtError() {\n  try {\n    throw getStacktraceWithUncaughtError();\n  } catch (err) {\n    return err;\n  }\n}\n// Some implementations of exception handling don't create a stack trace if the exception\n// isn't thrown, however it's faster not to actually throw the exception.\nconst error = getStacktraceWithUncaughtError();\nconst caughtError = getStacktraceWithCaughtError();\nconst getStacktrace = error.stack ? getStacktraceWithUncaughtError : caughtError.stack ? getStacktraceWithCaughtError : getStacktraceWithUncaughtError;\nfunction getFrames(error) {\n  return error.stack ? error.stack.split(NEWLINE) : [];\n}\nfunction addErrorStack(lines, error) {\n  let trace = getFrames(error);\n  for (let i = 0; i < trace.length; i++) {\n    const frame = trace[i];\n    // Filter out the Frames which are part of stack capturing.\n    if (!IGNORE_FRAMES.hasOwnProperty(frame)) {\n      lines.push(trace[i]);\n    }\n  }\n}\nfunction renderLongStackTrace(frames, stack) {\n  const longTrace = [stack ? stack.trim() : ''];\n  if (frames) {\n    let timestamp = new Date().getTime();\n    for (let i = 0; i < frames.length; i++) {\n      const traceFrames = frames[i];\n      const lastTime = traceFrames.timestamp;\n      let separator = `____________________Elapsed ${timestamp - lastTime.getTime()} ms; At: ${lastTime}`;\n      separator = separator.replace(/[^\\w\\d]/g, '_');\n      longTrace.push(sepTemplate.replace(SEP_TAG, separator));\n      addErrorStack(longTrace, traceFrames.error);\n      timestamp = lastTime.getTime();\n    }\n  }\n  return longTrace.join(NEWLINE);\n}\n// if Error.stackTraceLimit is 0, means stack trace\n// is disabled, so we don't need to generate long stack trace\n// this will improve performance in some test(some test will\n// set stackTraceLimit to 0, https://github.com/angular/zone.js/issues/698\nfunction stackTracesEnabled() {\n  // Cast through any since this property only exists on Error in the nodejs\n  // typings.\n  return Error.stackTraceLimit > 0;\n}\nZone['longStackTraceZoneSpec'] = {\n  name: 'long-stack-trace',\n  longStackTraceLimit: 10,\n  // add a getLongStackTrace method in spec to\n  // handle handled reject promise error.\n  getLongStackTrace: function (error) {\n    if (!error) {\n      return undefined;\n    }\n    const trace = error[Zone.__symbol__('currentTaskTrace')];\n    if (!trace) {\n      return error.stack;\n    }\n    return renderLongStackTrace(trace, error.stack);\n  },\n  onScheduleTask: function (parentZoneDelegate, currentZone, targetZone, task) {\n    if (stackTracesEnabled()) {\n      const currentTask = Zone.currentTask;\n      let trace = currentTask && currentTask.data && currentTask.data[creationTrace] || [];\n      trace = [new LongStackTrace()].concat(trace);\n      if (trace.length > this.longStackTraceLimit) {\n        trace.length = this.longStackTraceLimit;\n      }\n      if (!task.data) task.data = {};\n      if (task.type === 'eventTask') {\n        // Fix issue https://github.com/angular/zone.js/issues/1195,\n        // For event task of browser, by default, all task will share a\n        // singleton instance of data object, we should create a new one here\n        // The cast to `any` is required to workaround a closure bug which wrongly applies\n        // URL sanitization rules to .data access.\n        task.data = {\n          ...task.data\n        };\n      }\n      task.data[creationTrace] = trace;\n    }\n    return parentZoneDelegate.scheduleTask(targetZone, task);\n  },\n  onHandleError: function (parentZoneDelegate, currentZone, targetZone, error) {\n    if (stackTracesEnabled()) {\n      const parentTask = Zone.currentTask || error.task;\n      if (error instanceof Error && parentTask) {\n        const longStack = renderLongStackTrace(parentTask.data && parentTask.data[creationTrace], error.stack);\n        try {\n          error.stack = error.longStack = longStack;\n        } catch (err) {}\n      }\n    }\n    return parentZoneDelegate.handleError(targetZone, error);\n  }\n};\nfunction captureStackTraces(stackTraces, count) {\n  if (count > 0) {\n    stackTraces.push(getFrames(new LongStackTrace().error));\n    captureStackTraces(stackTraces, count - 1);\n  }\n}\nfunction computeIgnoreFrames() {\n  if (!stackTracesEnabled()) {\n    return;\n  }\n  const frames = [];\n  captureStackTraces(frames, 2);\n  const frames1 = frames[0];\n  const frames2 = frames[1];\n  for (let i = 0; i < frames1.length; i++) {\n    const frame1 = frames1[i];\n    if (frame1.indexOf(ERROR_TAG) == -1) {\n      let match = frame1.match(/^\\s*at\\s+/);\n      if (match) {\n        sepTemplate = match[0] + SEP_TAG + ' (http://localhost)';\n        break;\n      }\n    }\n  }\n  for (let i = 0; i < frames1.length; i++) {\n    const frame1 = frames1[i];\n    const frame2 = frames2[i];\n    if (frame1 === frame2) {\n      IGNORE_FRAMES[frame1] = true;\n    } else {\n      break;\n    }\n  }\n}\ncomputeIgnoreFrames();\nclass ProxyZoneSpec {\n  static get() {\n    return Zone.current.get('ProxyZoneSpec');\n  }\n  static isLoaded() {\n    return ProxyZoneSpec.get() instanceof ProxyZoneSpec;\n  }\n  static assertPresent() {\n    if (!ProxyZoneSpec.isLoaded()) {\n      throw new Error(`Expected to be running in 'ProxyZone', but it was not found.`);\n    }\n    return ProxyZoneSpec.get();\n  }\n  constructor(defaultSpecDelegate = null) {\n    this.defaultSpecDelegate = defaultSpecDelegate;\n    this.name = 'ProxyZone';\n    this._delegateSpec = null;\n    this.properties = {\n      'ProxyZoneSpec': this\n    };\n    this.propertyKeys = null;\n    this.lastTaskState = null;\n    this.isNeedToTriggerHasTask = false;\n    this.tasks = [];\n    this.setDelegate(defaultSpecDelegate);\n  }\n  setDelegate(delegateSpec) {\n    const isNewDelegate = this._delegateSpec !== delegateSpec;\n    this._delegateSpec = delegateSpec;\n    this.propertyKeys && this.propertyKeys.forEach(key => delete this.properties[key]);\n    this.propertyKeys = null;\n    if (delegateSpec && delegateSpec.properties) {\n      this.propertyKeys = Object.keys(delegateSpec.properties);\n      this.propertyKeys.forEach(k => this.properties[k] = delegateSpec.properties[k]);\n    }\n    // if a new delegateSpec was set, check if we need to trigger hasTask\n    if (isNewDelegate && this.lastTaskState && (this.lastTaskState.macroTask || this.lastTaskState.microTask)) {\n      this.isNeedToTriggerHasTask = true;\n    }\n  }\n  getDelegate() {\n    return this._delegateSpec;\n  }\n  resetDelegate() {\n    this.getDelegate();\n    this.setDelegate(this.defaultSpecDelegate);\n  }\n  tryTriggerHasTask(parentZoneDelegate, currentZone, targetZone) {\n    if (this.isNeedToTriggerHasTask && this.lastTaskState) {\n      // last delegateSpec has microTask or macroTask\n      // should call onHasTask in current delegateSpec\n      this.isNeedToTriggerHasTask = false;\n      this.onHasTask(parentZoneDelegate, currentZone, targetZone, this.lastTaskState);\n    }\n  }\n  removeFromTasks(task) {\n    if (!this.tasks) {\n      return;\n    }\n    for (let i = 0; i < this.tasks.length; i++) {\n      if (this.tasks[i] === task) {\n        this.tasks.splice(i, 1);\n        return;\n      }\n    }\n  }\n  getAndClearPendingTasksInfo() {\n    if (this.tasks.length === 0) {\n      return '';\n    }\n    const taskInfo = this.tasks.map(task => {\n      const dataInfo = task.data && Object.keys(task.data).map(key => {\n        return key + ':' + task.data[key];\n      }).join(',');\n      return `type: ${task.type}, source: ${task.source}, args: {${dataInfo}}`;\n    });\n    const pendingTasksInfo = '--Pending async tasks are: [' + taskInfo + ']';\n    // clear tasks\n    this.tasks = [];\n    return pendingTasksInfo;\n  }\n  onFork(parentZoneDelegate, currentZone, targetZone, zoneSpec) {\n    if (this._delegateSpec && this._delegateSpec.onFork) {\n      return this._delegateSpec.onFork(parentZoneDelegate, currentZone, targetZone, zoneSpec);\n    } else {\n      return parentZoneDelegate.fork(targetZone, zoneSpec);\n    }\n  }\n  onIntercept(parentZoneDelegate, currentZone, targetZone, delegate, source) {\n    if (this._delegateSpec && this._delegateSpec.onIntercept) {\n      return this._delegateSpec.onIntercept(parentZoneDelegate, currentZone, targetZone, delegate, source);\n    } else {\n      return parentZoneDelegate.intercept(targetZone, delegate, source);\n    }\n  }\n  onInvoke(parentZoneDelegate, currentZone, targetZone, delegate, applyThis, applyArgs, source) {\n    this.tryTriggerHasTask(parentZoneDelegate, currentZone, targetZone);\n    if (this._delegateSpec && this._delegateSpec.onInvoke) {\n      return this._delegateSpec.onInvoke(parentZoneDelegate, currentZone, targetZone, delegate, applyThis, applyArgs, source);\n    } else {\n      return parentZoneDelegate.invoke(targetZone, delegate, applyThis, applyArgs, source);\n    }\n  }\n  onHandleError(parentZoneDelegate, currentZone, targetZone, error) {\n    if (this._delegateSpec && this._delegateSpec.onHandleError) {\n      return this._delegateSpec.onHandleError(parentZoneDelegate, currentZone, targetZone, error);\n    } else {\n      return parentZoneDelegate.handleError(targetZone, error);\n    }\n  }\n  onScheduleTask(parentZoneDelegate, currentZone, targetZone, task) {\n    if (task.type !== 'eventTask') {\n      this.tasks.push(task);\n    }\n    if (this._delegateSpec && this._delegateSpec.onScheduleTask) {\n      return this._delegateSpec.onScheduleTask(parentZoneDelegate, currentZone, targetZone, task);\n    } else {\n      return parentZoneDelegate.scheduleTask(targetZone, task);\n    }\n  }\n  onInvokeTask(parentZoneDelegate, currentZone, targetZone, task, applyThis, applyArgs) {\n    if (task.type !== 'eventTask') {\n      this.removeFromTasks(task);\n    }\n    this.tryTriggerHasTask(parentZoneDelegate, currentZone, targetZone);\n    if (this._delegateSpec && this._delegateSpec.onInvokeTask) {\n      return this._delegateSpec.onInvokeTask(parentZoneDelegate, currentZone, targetZone, task, applyThis, applyArgs);\n    } else {\n      return parentZoneDelegate.invokeTask(targetZone, task, applyThis, applyArgs);\n    }\n  }\n  onCancelTask(parentZoneDelegate, currentZone, targetZone, task) {\n    if (task.type !== 'eventTask') {\n      this.removeFromTasks(task);\n    }\n    this.tryTriggerHasTask(parentZoneDelegate, currentZone, targetZone);\n    if (this._delegateSpec && this._delegateSpec.onCancelTask) {\n      return this._delegateSpec.onCancelTask(parentZoneDelegate, currentZone, targetZone, task);\n    } else {\n      return parentZoneDelegate.cancelTask(targetZone, task);\n    }\n  }\n  onHasTask(delegate, current, target, hasTaskState) {\n    this.lastTaskState = hasTaskState;\n    if (this._delegateSpec && this._delegateSpec.onHasTask) {\n      this._delegateSpec.onHasTask(delegate, current, target, hasTaskState);\n    } else {\n      delegate.hasTask(target, hasTaskState);\n    }\n  }\n}\n// Export the class so that new instances can be created with proper\n// constructor params.\nZone['ProxyZoneSpec'] = ProxyZoneSpec;\nclass SyncTestZoneSpec {\n  constructor(namePrefix) {\n    this.runZone = Zone.current;\n    this.name = 'syncTestZone for ' + namePrefix;\n  }\n  onScheduleTask(delegate, current, target, task) {\n    switch (task.type) {\n      case 'microTask':\n      case 'macroTask':\n        throw new Error(`Cannot call ${task.source} from within a sync test (${this.name}).`);\n      case 'eventTask':\n        task = delegate.scheduleTask(target, task);\n        break;\n    }\n    return task;\n  }\n}\n// Export the class so that new instances can be created with proper\n// constructor params.\nZone['SyncTestZoneSpec'] = SyncTestZoneSpec;\n\n/// <reference types=\"jasmine\"/>\nZone.__load_patch('jasmine', (global, Zone, api) => {\n  const __extends = function (d, b) {\n    for (const p in b) if (b.hasOwnProperty(p)) d[p] = b[p];\n    function __() {\n      this.constructor = d;\n    }\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n  };\n  // Patch jasmine's describe/it/beforeEach/afterEach functions so test code always runs\n  // in a testZone (ProxyZone). (See: angular/zone.js#91 & angular/angular#10503)\n  if (!Zone) throw new Error('Missing: zone.js');\n  if (typeof jest !== 'undefined') {\n    // return if jasmine is a light implementation inside jest\n    // in this case, we are running inside jest not jasmine\n    return;\n  }\n  if (typeof jasmine == 'undefined' || jasmine['__zone_patch__']) {\n    return;\n  }\n  jasmine['__zone_patch__'] = true;\n  const SyncTestZoneSpec = Zone['SyncTestZoneSpec'];\n  const ProxyZoneSpec = Zone['ProxyZoneSpec'];\n  if (!SyncTestZoneSpec) throw new Error('Missing: SyncTestZoneSpec');\n  if (!ProxyZoneSpec) throw new Error('Missing: ProxyZoneSpec');\n  const ambientZone = Zone.current;\n  const symbol = Zone.__symbol__;\n  // whether patch jasmine clock when in fakeAsync\n  const disablePatchingJasmineClock = global[symbol('fakeAsyncDisablePatchingClock')] === true;\n  // the original variable name fakeAsyncPatchLock is not accurate, so the name will be\n  // fakeAsyncAutoFakeAsyncWhenClockPatched and if this enablePatchingJasmineClock is false, we also\n  // automatically disable the auto jump into fakeAsync feature\n  const enableAutoFakeAsyncWhenClockPatched = !disablePatchingJasmineClock && (global[symbol('fakeAsyncPatchLock')] === true || global[symbol('fakeAsyncAutoFakeAsyncWhenClockPatched')] === true);\n  const ignoreUnhandledRejection = global[symbol('ignoreUnhandledRejection')] === true;\n  if (!ignoreUnhandledRejection) {\n    const globalErrors = jasmine.GlobalErrors;\n    if (globalErrors && !jasmine[symbol('GlobalErrors')]) {\n      jasmine[symbol('GlobalErrors')] = globalErrors;\n      jasmine.GlobalErrors = function () {\n        const instance = new globalErrors();\n        const originalInstall = instance.install;\n        if (originalInstall && !instance[symbol('install')]) {\n          instance[symbol('install')] = originalInstall;\n          instance.install = function () {\n            const isNode = typeof process !== 'undefined' && !!process.on;\n            // Note: Jasmine checks internally if `process` and `process.on` is defined. Otherwise,\n            // it installs the browser rejection handler through the `global.addEventListener`.\n            // This code may be run in the browser environment where `process` is not defined, and\n            // this will lead to a runtime exception since Webpack 5 removed automatic Node.js\n            // polyfills. Note, that events are named differently, it's `unhandledRejection` in\n            // Node.js and `unhandledrejection` in the browser.\n            const originalHandlers = isNode ? process.listeners('unhandledRejection') : global.eventListeners('unhandledrejection');\n            const result = originalInstall.apply(this, arguments);\n            isNode ? process.removeAllListeners('unhandledRejection') : global.removeAllListeners('unhandledrejection');\n            if (originalHandlers) {\n              originalHandlers.forEach(handler => {\n                if (isNode) {\n                  process.on('unhandledRejection', handler);\n                } else {\n                  global.addEventListener('unhandledrejection', handler);\n                }\n              });\n            }\n            return result;\n          };\n        }\n        return instance;\n      };\n    }\n  }\n  // Monkey patch all of the jasmine DSL so that each function runs in appropriate zone.\n  const jasmineEnv = jasmine.getEnv();\n  ['describe', 'xdescribe', 'fdescribe'].forEach(methodName => {\n    let originalJasmineFn = jasmineEnv[methodName];\n    jasmineEnv[methodName] = function (description, specDefinitions) {\n      return originalJasmineFn.call(this, description, wrapDescribeInZone(description, specDefinitions));\n    };\n  });\n  ['it', 'xit', 'fit'].forEach(methodName => {\n    let originalJasmineFn = jasmineEnv[methodName];\n    jasmineEnv[symbol(methodName)] = originalJasmineFn;\n    jasmineEnv[methodName] = function (description, specDefinitions, timeout) {\n      arguments[1] = wrapTestInZone(specDefinitions);\n      return originalJasmineFn.apply(this, arguments);\n    };\n  });\n  ['beforeEach', 'afterEach', 'beforeAll', 'afterAll'].forEach(methodName => {\n    let originalJasmineFn = jasmineEnv[methodName];\n    jasmineEnv[symbol(methodName)] = originalJasmineFn;\n    jasmineEnv[methodName] = function (specDefinitions, timeout) {\n      arguments[0] = wrapTestInZone(specDefinitions);\n      return originalJasmineFn.apply(this, arguments);\n    };\n  });\n  if (!disablePatchingJasmineClock) {\n    // need to patch jasmine.clock().mockDate and jasmine.clock().tick() so\n    // they can work properly in FakeAsyncTest\n    const originalClockFn = jasmine[symbol('clock')] = jasmine['clock'];\n    jasmine['clock'] = function () {\n      const clock = originalClockFn.apply(this, arguments);\n      if (!clock[symbol('patched')]) {\n        clock[symbol('patched')] = symbol('patched');\n        const originalTick = clock[symbol('tick')] = clock.tick;\n        clock.tick = function () {\n          const fakeAsyncZoneSpec = Zone.current.get('FakeAsyncTestZoneSpec');\n          if (fakeAsyncZoneSpec) {\n            return fakeAsyncZoneSpec.tick.apply(fakeAsyncZoneSpec, arguments);\n          }\n          return originalTick.apply(this, arguments);\n        };\n        const originalMockDate = clock[symbol('mockDate')] = clock.mockDate;\n        clock.mockDate = function () {\n          const fakeAsyncZoneSpec = Zone.current.get('FakeAsyncTestZoneSpec');\n          if (fakeAsyncZoneSpec) {\n            const dateTime = arguments.length > 0 ? arguments[0] : new Date();\n            return fakeAsyncZoneSpec.setFakeBaseSystemTime.apply(fakeAsyncZoneSpec, dateTime && typeof dateTime.getTime === 'function' ? [dateTime.getTime()] : arguments);\n          }\n          return originalMockDate.apply(this, arguments);\n        };\n        // for auto go into fakeAsync feature, we need the flag to enable it\n        if (enableAutoFakeAsyncWhenClockPatched) {\n          ['install', 'uninstall'].forEach(methodName => {\n            const originalClockFn = clock[symbol(methodName)] = clock[methodName];\n            clock[methodName] = function () {\n              const FakeAsyncTestZoneSpec = Zone['FakeAsyncTestZoneSpec'];\n              if (FakeAsyncTestZoneSpec) {\n                jasmine[symbol('clockInstalled')] = 'install' === methodName;\n                return;\n              }\n              return originalClockFn.apply(this, arguments);\n            };\n          });\n        }\n      }\n      return clock;\n    };\n  }\n  // monkey patch createSpyObj to make properties enumerable to true\n  if (!jasmine[Zone.__symbol__('createSpyObj')]) {\n    const originalCreateSpyObj = jasmine.createSpyObj;\n    jasmine[Zone.__symbol__('createSpyObj')] = originalCreateSpyObj;\n    jasmine.createSpyObj = function () {\n      const args = Array.prototype.slice.call(arguments);\n      const propertyNames = args.length >= 3 ? args[2] : null;\n      let spyObj;\n      if (propertyNames) {\n        const defineProperty = Object.defineProperty;\n        Object.defineProperty = function (obj, p, attributes) {\n          return defineProperty.call(this, obj, p, {\n            ...attributes,\n            configurable: true,\n            enumerable: true\n          });\n        };\n        try {\n          spyObj = originalCreateSpyObj.apply(this, args);\n        } finally {\n          Object.defineProperty = defineProperty;\n        }\n      } else {\n        spyObj = originalCreateSpyObj.apply(this, args);\n      }\n      return spyObj;\n    };\n  }\n  /**\n   * Gets a function wrapping the body of a Jasmine `describe` block to execute in a\n   * synchronous-only zone.\n   */\n  function wrapDescribeInZone(description, describeBody) {\n    return function () {\n      // Create a synchronous-only zone in which to run `describe` blocks in order to raise an\n      // error if any asynchronous operations are attempted inside of a `describe`.\n      const syncZone = ambientZone.fork(new SyncTestZoneSpec(`jasmine.describe#${description}`));\n      return syncZone.run(describeBody, this, arguments);\n    };\n  }\n  function runInTestZone(testBody, applyThis, queueRunner, done) {\n    const isClockInstalled = !!jasmine[symbol('clockInstalled')];\n    queueRunner.testProxyZoneSpec;\n    const testProxyZone = queueRunner.testProxyZone;\n    if (isClockInstalled && enableAutoFakeAsyncWhenClockPatched) {\n      // auto run a fakeAsync\n      const fakeAsyncModule = Zone[Zone.__symbol__('fakeAsyncTest')];\n      if (fakeAsyncModule && typeof fakeAsyncModule.fakeAsync === 'function') {\n        testBody = fakeAsyncModule.fakeAsync(testBody);\n      }\n    }\n    if (done) {\n      return testProxyZone.run(testBody, applyThis, [done]);\n    } else {\n      return testProxyZone.run(testBody, applyThis);\n    }\n  }\n  /**\n   * Gets a function wrapping the body of a Jasmine `it/beforeEach/afterEach` block to\n   * execute in a ProxyZone zone.\n   * This will run in `testProxyZone`. The `testProxyZone` will be reset by the `ZoneQueueRunner`\n   */\n  function wrapTestInZone(testBody) {\n    // The `done` callback is only passed through if the function expects at least one argument.\n    // Note we have to make a function with correct number of arguments, otherwise jasmine will\n    // think that all functions are sync or async.\n    return testBody && (testBody.length ? function (done) {\n      return runInTestZone(testBody, this, this.queueRunner, done);\n    } : function () {\n      return runInTestZone(testBody, this, this.queueRunner);\n    });\n  }\n  const QueueRunner = jasmine.QueueRunner;\n  jasmine.QueueRunner = function (_super) {\n    __extends(ZoneQueueRunner, _super);\n    function ZoneQueueRunner(attrs) {\n      if (attrs.onComplete) {\n        attrs.onComplete = (fn => () => {\n          // All functions are done, clear the test zone.\n          this.testProxyZone = null;\n          this.testProxyZoneSpec = null;\n          ambientZone.scheduleMicroTask('jasmine.onComplete', fn);\n        })(attrs.onComplete);\n      }\n      const nativeSetTimeout = global[Zone.__symbol__('setTimeout')];\n      const nativeClearTimeout = global[Zone.__symbol__('clearTimeout')];\n      if (nativeSetTimeout) {\n        // should run setTimeout inside jasmine outside of zone\n        attrs.timeout = {\n          setTimeout: nativeSetTimeout ? nativeSetTimeout : global.setTimeout,\n          clearTimeout: nativeClearTimeout ? nativeClearTimeout : global.clearTimeout\n        };\n      }\n      // create a userContext to hold the queueRunner itself\n      // so we can access the testProxy in it/xit/beforeEach ...\n      if (jasmine.UserContext) {\n        if (!attrs.userContext) {\n          attrs.userContext = new jasmine.UserContext();\n        }\n        attrs.userContext.queueRunner = this;\n      } else {\n        if (!attrs.userContext) {\n          attrs.userContext = {};\n        }\n        attrs.userContext.queueRunner = this;\n      }\n      // patch attrs.onException\n      const onException = attrs.onException;\n      attrs.onException = function (error) {\n        if (error && error.message === 'Timeout - Async callback was not invoked within timeout specified by jasmine.DEFAULT_TIMEOUT_INTERVAL.') {\n          // jasmine timeout, we can make the error message more\n          // reasonable to tell what tasks are pending\n          const proxyZoneSpec = this && this.testProxyZoneSpec;\n          if (proxyZoneSpec) {\n            const pendingTasksInfo = proxyZoneSpec.getAndClearPendingTasksInfo();\n            try {\n              // try catch here in case error.message is not writable\n              error.message += pendingTasksInfo;\n            } catch (err) {}\n          }\n        }\n        if (onException) {\n          onException.call(this, error);\n        }\n      };\n      _super.call(this, attrs);\n    }\n    ZoneQueueRunner.prototype.execute = function () {\n      let zone = Zone.current;\n      let isChildOfAmbientZone = false;\n      while (zone) {\n        if (zone === ambientZone) {\n          isChildOfAmbientZone = true;\n          break;\n        }\n        zone = zone.parent;\n      }\n      if (!isChildOfAmbientZone) throw new Error('Unexpected Zone: ' + Zone.current.name);\n      // This is the zone which will be used for running individual tests.\n      // It will be a proxy zone, so that the tests function can retroactively install\n      // different zones.\n      // Example:\n      //   - In beforeEach() do childZone = Zone.current.fork(...);\n      //   - In it() try to do fakeAsync(). The issue is that because the beforeEach forked the\n      //     zone outside of fakeAsync it will be able to escape the fakeAsync rules.\n      //   - Because ProxyZone is parent fo `childZone` fakeAsync can retroactively add\n      //     fakeAsync behavior to the childZone.\n      this.testProxyZoneSpec = new ProxyZoneSpec();\n      this.testProxyZone = ambientZone.fork(this.testProxyZoneSpec);\n      if (!Zone.currentTask) {\n        // if we are not running in a task then if someone would register a\n        // element.addEventListener and then calling element.click() the\n        // addEventListener callback would think that it is the top most task and would\n        // drain the microtask queue on element.click() which would be incorrect.\n        // For this reason we always force a task when running jasmine tests.\n        Zone.current.scheduleMicroTask('jasmine.execute().forceTask', () => QueueRunner.prototype.execute.call(this));\n      } else {\n        _super.prototype.execute.call(this);\n      }\n    };\n    return ZoneQueueRunner;\n  }(QueueRunner);\n});\nZone.__load_patch('jest', (context, Zone, api) => {\n  if (typeof jest === 'undefined' || jest['__zone_patch__']) {\n    return;\n  }\n  // From jest 29 and jest-preset-angular v13, the module transform logic\n  // changed, and now jest-preset-angular use the use the tsconfig target\n  // other than the hardcoded one, https://github.com/thymikee/jest-preset-angular/issues/2010\n  // But jest-angular-preset doesn't introduce the @babel/plugin-transform-async-to-generator\n  // which is needed by angular since `async/await` still need to be transformed\n  // to promise for ES2017+ target.\n  // So for now, we disable to output the uncaught error console log for a temp solution,\n  // until jest-preset-angular find a proper solution.\n  Zone[api.symbol('ignoreConsoleErrorUncaughtError')] = true;\n  jest['__zone_patch__'] = true;\n  const ProxyZoneSpec = Zone['ProxyZoneSpec'];\n  const SyncTestZoneSpec = Zone['SyncTestZoneSpec'];\n  if (!ProxyZoneSpec) {\n    throw new Error('Missing ProxyZoneSpec');\n  }\n  const rootZone = Zone.current;\n  const syncZone = rootZone.fork(new SyncTestZoneSpec('jest.describe'));\n  const proxyZoneSpec = new ProxyZoneSpec();\n  const proxyZone = rootZone.fork(proxyZoneSpec);\n  function wrapDescribeFactoryInZone(originalJestFn) {\n    return function (...tableArgs) {\n      const originalDescribeFn = originalJestFn.apply(this, tableArgs);\n      return function (...args) {\n        args[1] = wrapDescribeInZone(args[1]);\n        return originalDescribeFn.apply(this, args);\n      };\n    };\n  }\n  function wrapTestFactoryInZone(originalJestFn) {\n    return function (...tableArgs) {\n      return function (...args) {\n        args[1] = wrapTestInZone(args[1]);\n        return originalJestFn.apply(this, tableArgs).apply(this, args);\n      };\n    };\n  }\n  /**\n   * Gets a function wrapping the body of a jest `describe` block to execute in a\n   * synchronous-only zone.\n   */\n  function wrapDescribeInZone(describeBody) {\n    return function (...args) {\n      return syncZone.run(describeBody, this, args);\n    };\n  }\n  /**\n   * Gets a function wrapping the body of a jest `it/beforeEach/afterEach` block to\n   * execute in a ProxyZone zone.\n   * This will run in the `proxyZone`.\n   */\n  function wrapTestInZone(testBody, isTestFunc = false) {\n    if (typeof testBody !== 'function') {\n      return testBody;\n    }\n    const wrappedFunc = function () {\n      if (Zone[api.symbol('useFakeTimersCalled')] === true && testBody && !testBody.isFakeAsync) {\n        // jest.useFakeTimers is called, run into fakeAsyncTest automatically.\n        const fakeAsyncModule = Zone[Zone.__symbol__('fakeAsyncTest')];\n        if (fakeAsyncModule && typeof fakeAsyncModule.fakeAsync === 'function') {\n          testBody = fakeAsyncModule.fakeAsync(testBody);\n        }\n      }\n      proxyZoneSpec.isTestFunc = isTestFunc;\n      return proxyZone.run(testBody, null, arguments);\n    };\n    // Update the length of wrappedFunc to be the same as the length of the testBody\n    // So jest core can handle whether the test function has `done()` or not correctly\n    Object.defineProperty(wrappedFunc, 'length', {\n      configurable: true,\n      writable: true,\n      enumerable: false\n    });\n    wrappedFunc.length = testBody.length;\n    return wrappedFunc;\n  }\n  ['describe', 'xdescribe', 'fdescribe'].forEach(methodName => {\n    let originalJestFn = context[methodName];\n    if (context[Zone.__symbol__(methodName)]) {\n      return;\n    }\n    context[Zone.__symbol__(methodName)] = originalJestFn;\n    context[methodName] = function (...args) {\n      args[1] = wrapDescribeInZone(args[1]);\n      return originalJestFn.apply(this, args);\n    };\n    context[methodName].each = wrapDescribeFactoryInZone(originalJestFn.each);\n  });\n  context.describe.only = context.fdescribe;\n  context.describe.skip = context.xdescribe;\n  ['it', 'xit', 'fit', 'test', 'xtest'].forEach(methodName => {\n    let originalJestFn = context[methodName];\n    if (context[Zone.__symbol__(methodName)]) {\n      return;\n    }\n    context[Zone.__symbol__(methodName)] = originalJestFn;\n    context[methodName] = function (...args) {\n      args[1] = wrapTestInZone(args[1], true);\n      return originalJestFn.apply(this, args);\n    };\n    context[methodName].each = wrapTestFactoryInZone(originalJestFn.each);\n    context[methodName].todo = originalJestFn.todo;\n  });\n  context.it.only = context.fit;\n  context.it.skip = context.xit;\n  context.test.only = context.fit;\n  context.test.skip = context.xit;\n  ['beforeEach', 'afterEach', 'beforeAll', 'afterAll'].forEach(methodName => {\n    let originalJestFn = context[methodName];\n    if (context[Zone.__symbol__(methodName)]) {\n      return;\n    }\n    context[Zone.__symbol__(methodName)] = originalJestFn;\n    context[methodName] = function (...args) {\n      args[0] = wrapTestInZone(args[0]);\n      return originalJestFn.apply(this, args);\n    };\n  });\n  Zone.patchJestObject = function patchJestObject(Timer, isModern = false) {\n    // check whether currently the test is inside fakeAsync()\n    function isPatchingFakeTimer() {\n      const fakeAsyncZoneSpec = Zone.current.get('FakeAsyncTestZoneSpec');\n      return !!fakeAsyncZoneSpec;\n    }\n    // check whether the current function is inside `test/it` or other methods\n    // such as `describe/beforeEach`\n    function isInTestFunc() {\n      const proxyZoneSpec = Zone.current.get('ProxyZoneSpec');\n      return proxyZoneSpec && proxyZoneSpec.isTestFunc;\n    }\n    if (Timer[api.symbol('fakeTimers')]) {\n      return;\n    }\n    Timer[api.symbol('fakeTimers')] = true;\n    // patch jest fakeTimer internal method to make sure no console.warn print out\n    api.patchMethod(Timer, '_checkFakeTimers', delegate => {\n      return function (self, args) {\n        if (isPatchingFakeTimer()) {\n          return true;\n        } else {\n          return delegate.apply(self, args);\n        }\n      };\n    });\n    // patch useFakeTimers(), set useFakeTimersCalled flag, and make test auto run into fakeAsync\n    api.patchMethod(Timer, 'useFakeTimers', delegate => {\n      return function (self, args) {\n        Zone[api.symbol('useFakeTimersCalled')] = true;\n        if (isModern || isInTestFunc()) {\n          return delegate.apply(self, args);\n        }\n        return self;\n      };\n    });\n    // patch useRealTimers(), unset useFakeTimers flag\n    api.patchMethod(Timer, 'useRealTimers', delegate => {\n      return function (self, args) {\n        Zone[api.symbol('useFakeTimersCalled')] = false;\n        if (isModern || isInTestFunc()) {\n          return delegate.apply(self, args);\n        }\n        return self;\n      };\n    });\n    // patch setSystemTime(), call setCurrentRealTime() in the fakeAsyncTest\n    api.patchMethod(Timer, 'setSystemTime', delegate => {\n      return function (self, args) {\n        const fakeAsyncZoneSpec = Zone.current.get('FakeAsyncTestZoneSpec');\n        if (fakeAsyncZoneSpec && isPatchingFakeTimer()) {\n          fakeAsyncZoneSpec.setFakeBaseSystemTime(args[0]);\n        } else {\n          return delegate.apply(self, args);\n        }\n      };\n    });\n    // patch getSystemTime(), call getCurrentRealTime() in the fakeAsyncTest\n    api.patchMethod(Timer, 'getRealSystemTime', delegate => {\n      return function (self, args) {\n        const fakeAsyncZoneSpec = Zone.current.get('FakeAsyncTestZoneSpec');\n        if (fakeAsyncZoneSpec && isPatchingFakeTimer()) {\n          return fakeAsyncZoneSpec.getRealSystemTime();\n        } else {\n          return delegate.apply(self, args);\n        }\n      };\n    });\n    // patch runAllTicks(), run all microTasks inside fakeAsync\n    api.patchMethod(Timer, 'runAllTicks', delegate => {\n      return function (self, args) {\n        const fakeAsyncZoneSpec = Zone.current.get('FakeAsyncTestZoneSpec');\n        if (fakeAsyncZoneSpec) {\n          fakeAsyncZoneSpec.flushMicrotasks();\n        } else {\n          return delegate.apply(self, args);\n        }\n      };\n    });\n    // patch runAllTimers(), run all macroTasks inside fakeAsync\n    api.patchMethod(Timer, 'runAllTimers', delegate => {\n      return function (self, args) {\n        const fakeAsyncZoneSpec = Zone.current.get('FakeAsyncTestZoneSpec');\n        if (fakeAsyncZoneSpec) {\n          fakeAsyncZoneSpec.flush(100, true);\n        } else {\n          return delegate.apply(self, args);\n        }\n      };\n    });\n    // patch advanceTimersByTime(), call tick() in the fakeAsyncTest\n    api.patchMethod(Timer, 'advanceTimersByTime', delegate => {\n      return function (self, args) {\n        const fakeAsyncZoneSpec = Zone.current.get('FakeAsyncTestZoneSpec');\n        if (fakeAsyncZoneSpec) {\n          fakeAsyncZoneSpec.tick(args[0]);\n        } else {\n          return delegate.apply(self, args);\n        }\n      };\n    });\n    // patch runOnlyPendingTimers(), call flushOnlyPendingTimers() in the fakeAsyncTest\n    api.patchMethod(Timer, 'runOnlyPendingTimers', delegate => {\n      return function (self, args) {\n        const fakeAsyncZoneSpec = Zone.current.get('FakeAsyncTestZoneSpec');\n        if (fakeAsyncZoneSpec) {\n          fakeAsyncZoneSpec.flushOnlyPendingTimers();\n        } else {\n          return delegate.apply(self, args);\n        }\n      };\n    });\n    // patch advanceTimersToNextTimer(), call tickToNext() in the fakeAsyncTest\n    api.patchMethod(Timer, 'advanceTimersToNextTimer', delegate => {\n      return function (self, args) {\n        const fakeAsyncZoneSpec = Zone.current.get('FakeAsyncTestZoneSpec');\n        if (fakeAsyncZoneSpec) {\n          fakeAsyncZoneSpec.tickToNext(args[0]);\n        } else {\n          return delegate.apply(self, args);\n        }\n      };\n    });\n    // patch clearAllTimers(), call removeAllTimers() in the fakeAsyncTest\n    api.patchMethod(Timer, 'clearAllTimers', delegate => {\n      return function (self, args) {\n        const fakeAsyncZoneSpec = Zone.current.get('FakeAsyncTestZoneSpec');\n        if (fakeAsyncZoneSpec) {\n          fakeAsyncZoneSpec.removeAllTimers();\n        } else {\n          return delegate.apply(self, args);\n        }\n      };\n    });\n    // patch getTimerCount(), call getTimerCount() in the fakeAsyncTest\n    api.patchMethod(Timer, 'getTimerCount', delegate => {\n      return function (self, args) {\n        const fakeAsyncZoneSpec = Zone.current.get('FakeAsyncTestZoneSpec');\n        if (fakeAsyncZoneSpec) {\n          return fakeAsyncZoneSpec.getTimerCount();\n        } else {\n          return delegate.apply(self, args);\n        }\n      };\n    });\n  };\n});\nZone.__load_patch('mocha', (global, Zone) => {\n  const Mocha = global.Mocha;\n  if (typeof Mocha === 'undefined') {\n    // return if Mocha is not available, because now zone-testing\n    // will load mocha patch with jasmine/jest patch\n    return;\n  }\n  if (typeof Zone === 'undefined') {\n    throw new Error('Missing Zone.js');\n  }\n  const ProxyZoneSpec = Zone['ProxyZoneSpec'];\n  const SyncTestZoneSpec = Zone['SyncTestZoneSpec'];\n  if (!ProxyZoneSpec) {\n    throw new Error('Missing ProxyZoneSpec');\n  }\n  if (Mocha['__zone_patch__']) {\n    throw new Error('\"Mocha\" has already been patched with \"Zone\".');\n  }\n  Mocha['__zone_patch__'] = true;\n  const rootZone = Zone.current;\n  const syncZone = rootZone.fork(new SyncTestZoneSpec('Mocha.describe'));\n  let testZone = null;\n  const suiteZone = rootZone.fork(new ProxyZoneSpec());\n  const mochaOriginal = {\n    after: global.after,\n    afterEach: global.afterEach,\n    before: global.before,\n    beforeEach: global.beforeEach,\n    describe: global.describe,\n    it: global.it\n  };\n  function modifyArguments(args, syncTest, asyncTest) {\n    for (let i = 0; i < args.length; i++) {\n      let arg = args[i];\n      if (typeof arg === 'function') {\n        // The `done` callback is only passed through if the function expects at\n        // least one argument.\n        // Note we have to make a function with correct number of arguments,\n        // otherwise mocha will\n        // think that all functions are sync or async.\n        args[i] = arg.length === 0 ? syncTest(arg) : asyncTest(arg);\n        // Mocha uses toString to view the test body in the result list, make sure we return the\n        // correct function body\n        args[i].toString = function () {\n          return arg.toString();\n        };\n      }\n    }\n    return args;\n  }\n  function wrapDescribeInZone(args) {\n    const syncTest = function (fn) {\n      return function () {\n        return syncZone.run(fn, this, arguments);\n      };\n    };\n    return modifyArguments(args, syncTest);\n  }\n  function wrapTestInZone(args) {\n    const asyncTest = function (fn) {\n      return function (done) {\n        return testZone.run(fn, this, [done]);\n      };\n    };\n    const syncTest = function (fn) {\n      return function () {\n        return testZone.run(fn, this);\n      };\n    };\n    return modifyArguments(args, syncTest, asyncTest);\n  }\n  function wrapSuiteInZone(args) {\n    const asyncTest = function (fn) {\n      return function (done) {\n        return suiteZone.run(fn, this, [done]);\n      };\n    };\n    const syncTest = function (fn) {\n      return function () {\n        return suiteZone.run(fn, this);\n      };\n    };\n    return modifyArguments(args, syncTest, asyncTest);\n  }\n  global.describe = global.suite = function () {\n    return mochaOriginal.describe.apply(this, wrapDescribeInZone(arguments));\n  };\n  global.xdescribe = global.suite.skip = global.describe.skip = function () {\n    return mochaOriginal.describe.skip.apply(this, wrapDescribeInZone(arguments));\n  };\n  global.describe.only = global.suite.only = function () {\n    return mochaOriginal.describe.only.apply(this, wrapDescribeInZone(arguments));\n  };\n  global.it = global.specify = global.test = function () {\n    return mochaOriginal.it.apply(this, wrapTestInZone(arguments));\n  };\n  global.xit = global.xspecify = global.it.skip = function () {\n    return mochaOriginal.it.skip.apply(this, wrapTestInZone(arguments));\n  };\n  global.it.only = global.test.only = function () {\n    return mochaOriginal.it.only.apply(this, wrapTestInZone(arguments));\n  };\n  global.after = global.suiteTeardown = function () {\n    return mochaOriginal.after.apply(this, wrapSuiteInZone(arguments));\n  };\n  global.afterEach = global.teardown = function () {\n    return mochaOriginal.afterEach.apply(this, wrapTestInZone(arguments));\n  };\n  global.before = global.suiteSetup = function () {\n    return mochaOriginal.before.apply(this, wrapSuiteInZone(arguments));\n  };\n  global.beforeEach = global.setup = function () {\n    return mochaOriginal.beforeEach.apply(this, wrapTestInZone(arguments));\n  };\n  ((originalRunTest, originalRun) => {\n    Mocha.Runner.prototype.runTest = function (fn) {\n      Zone.current.scheduleMicroTask('mocha.forceTask', () => {\n        originalRunTest.call(this, fn);\n      });\n    };\n    Mocha.Runner.prototype.run = function (fn) {\n      this.on('test', e => {\n        testZone = rootZone.fork(new ProxyZoneSpec());\n      });\n      this.on('fail', (test, err) => {\n        const proxyZoneSpec = testZone && testZone.get('ProxyZoneSpec');\n        if (proxyZoneSpec && err) {\n          try {\n            // try catch here in case err.message is not writable\n            err.message += proxyZoneSpec.getAndClearPendingTasksInfo();\n          } catch (error) {}\n        }\n      });\n      return originalRun.call(this, fn);\n    };\n  })(Mocha.Runner.prototype.runTest, Mocha.Runner.prototype.run);\n});\n(function (_global) {\n  class AsyncTestZoneSpec {\n    static {\n      this.symbolParentUnresolved = Zone.__symbol__('parentUnresolved');\n    }\n    constructor(finishCallback, failCallback, namePrefix) {\n      this.finishCallback = finishCallback;\n      this.failCallback = failCallback;\n      this._pendingMicroTasks = false;\n      this._pendingMacroTasks = false;\n      this._alreadyErrored = false;\n      this._isSync = false;\n      this._existingFinishTimer = null;\n      this.entryFunction = null;\n      this.runZone = Zone.current;\n      this.unresolvedChainedPromiseCount = 0;\n      this.supportWaitUnresolvedChainedPromise = false;\n      this.name = 'asyncTestZone for ' + namePrefix;\n      this.properties = {\n        'AsyncTestZoneSpec': this\n      };\n      this.supportWaitUnresolvedChainedPromise = _global[Zone.__symbol__('supportWaitUnResolvedChainedPromise')] === true;\n    }\n    isUnresolvedChainedPromisePending() {\n      return this.unresolvedChainedPromiseCount > 0;\n    }\n    _finishCallbackIfDone() {\n      // NOTE: Technically the `onHasTask` could fire together with the initial synchronous\n      // completion in `onInvoke`. `onHasTask` might call this method when it captured e.g.\n      // microtasks in the proxy zone that now complete as part of this async zone run.\n      // Consider the following scenario:\n      //    1. A test `beforeEach` schedules a microtask in the ProxyZone.\n      //    2. An actual empty `it` spec executes in the AsyncTestZone` (using e.g. `waitForAsync`).\n      //    3. The `onInvoke` invokes `_finishCallbackIfDone` because the spec runs synchronously.\n      //    4. We wait the scheduled timeout (see below) to account for unhandled promises.\n      //    5. The microtask from (1) finishes and `onHasTask` is invoked.\n      //    --> We register a second `_finishCallbackIfDone` even though we have scheduled a timeout.\n      // If the finish timeout from below is already scheduled, terminate the existing scheduled\n      // finish invocation, avoiding calling `jasmine` `done` multiple times. *Note* that we would\n      // want to schedule a new finish callback in case the task state changes again.\n      if (this._existingFinishTimer !== null) {\n        clearTimeout(this._existingFinishTimer);\n        this._existingFinishTimer = null;\n      }\n      if (!(this._pendingMicroTasks || this._pendingMacroTasks || this.supportWaitUnresolvedChainedPromise && this.isUnresolvedChainedPromisePending())) {\n        // We wait until the next tick because we would like to catch unhandled promises which could\n        // cause test logic to be executed. In such cases we cannot finish with tasks pending then.\n        this.runZone.run(() => {\n          this._existingFinishTimer = setTimeout(() => {\n            if (!this._alreadyErrored && !(this._pendingMicroTasks || this._pendingMacroTasks)) {\n              this.finishCallback();\n            }\n          }, 0);\n        });\n      }\n    }\n    patchPromiseForTest() {\n      if (!this.supportWaitUnresolvedChainedPromise) {\n        return;\n      }\n      const patchPromiseForTest = Promise[Zone.__symbol__('patchPromiseForTest')];\n      if (patchPromiseForTest) {\n        patchPromiseForTest();\n      }\n    }\n    unPatchPromiseForTest() {\n      if (!this.supportWaitUnresolvedChainedPromise) {\n        return;\n      }\n      const unPatchPromiseForTest = Promise[Zone.__symbol__('unPatchPromiseForTest')];\n      if (unPatchPromiseForTest) {\n        unPatchPromiseForTest();\n      }\n    }\n    onScheduleTask(delegate, current, target, task) {\n      if (task.type !== 'eventTask') {\n        this._isSync = false;\n      }\n      if (task.type === 'microTask' && task.data && task.data instanceof Promise) {\n        // check whether the promise is a chained promise\n        if (task.data[AsyncTestZoneSpec.symbolParentUnresolved] === true) {\n          // chained promise is being scheduled\n          this.unresolvedChainedPromiseCount--;\n        }\n      }\n      return delegate.scheduleTask(target, task);\n    }\n    onInvokeTask(delegate, current, target, task, applyThis, applyArgs) {\n      if (task.type !== 'eventTask') {\n        this._isSync = false;\n      }\n      return delegate.invokeTask(target, task, applyThis, applyArgs);\n    }\n    onCancelTask(delegate, current, target, task) {\n      if (task.type !== 'eventTask') {\n        this._isSync = false;\n      }\n      return delegate.cancelTask(target, task);\n    }\n    // Note - we need to use onInvoke at the moment to call finish when a test is\n    // fully synchronous. TODO(juliemr): remove this when the logic for\n    // onHasTask changes and it calls whenever the task queues are dirty.\n    // updated by(JiaLiPassion), only call finish callback when no task\n    // was scheduled/invoked/canceled.\n    onInvoke(parentZoneDelegate, currentZone, targetZone, delegate, applyThis, applyArgs, source) {\n      if (!this.entryFunction) {\n        this.entryFunction = delegate;\n      }\n      try {\n        this._isSync = true;\n        return parentZoneDelegate.invoke(targetZone, delegate, applyThis, applyArgs, source);\n      } finally {\n        // We need to check the delegate is the same as entryFunction or not.\n        // Consider the following case.\n        //\n        // asyncTestZone.run(() => { // Here the delegate will be the entryFunction\n        //   Zone.current.run(() => { // Here the delegate will not be the entryFunction\n        //   });\n        // });\n        //\n        // We only want to check whether there are async tasks scheduled\n        // for the entry function.\n        if (this._isSync && this.entryFunction === delegate) {\n          this._finishCallbackIfDone();\n        }\n      }\n    }\n    onHandleError(parentZoneDelegate, currentZone, targetZone, error) {\n      // Let the parent try to handle the error.\n      const result = parentZoneDelegate.handleError(targetZone, error);\n      if (result) {\n        this.failCallback(error);\n        this._alreadyErrored = true;\n      }\n      return false;\n    }\n    onHasTask(delegate, current, target, hasTaskState) {\n      delegate.hasTask(target, hasTaskState);\n      // We should only trigger finishCallback when the target zone is the AsyncTestZone\n      // Consider the following cases.\n      //\n      // const childZone = asyncTestZone.fork({\n      //   name: 'child',\n      //   onHasTask: ...\n      // });\n      //\n      // So we have nested zones declared the onHasTask hook, in this case,\n      // the onHasTask will be triggered twice, and cause the finishCallbackIfDone()\n      // is also be invoked twice. So we need to only trigger the finishCallbackIfDone()\n      // when the current zone is the same as the target zone.\n      if (current !== target) {\n        return;\n      }\n      if (hasTaskState.change == 'microTask') {\n        this._pendingMicroTasks = hasTaskState.microTask;\n        this._finishCallbackIfDone();\n      } else if (hasTaskState.change == 'macroTask') {\n        this._pendingMacroTasks = hasTaskState.macroTask;\n        this._finishCallbackIfDone();\n      }\n    }\n  }\n  // Export the class so that new instances can be created with proper\n  // constructor params.\n  Zone['AsyncTestZoneSpec'] = AsyncTestZoneSpec;\n})(typeof window !== 'undefined' && window || typeof self !== 'undefined' && self || global);\nZone.__load_patch('asynctest', (global, Zone, api) => {\n  /**\n   * Wraps a test function in an asynchronous test zone. The test will automatically\n   * complete when all asynchronous calls within this zone are done.\n   */\n  Zone[api.symbol('asyncTest')] = function asyncTest(fn) {\n    // If we're running using the Jasmine test framework, adapt to call the 'done'\n    // function when asynchronous activity is finished.\n    if (global.jasmine) {\n      // Not using an arrow function to preserve context passed from call site\n      return function (done) {\n        if (!done) {\n          // if we run beforeEach in @angular/core/testing/testing_internal then we get no done\n          // fake it here and assume sync.\n          done = function () {};\n          done.fail = function (e) {\n            throw e;\n          };\n        }\n        runInTestZone(fn, this, done, err => {\n          if (typeof err === 'string') {\n            return done.fail(new Error(err));\n          } else {\n            done.fail(err);\n          }\n        });\n      };\n    }\n    // Otherwise, return a promise which will resolve when asynchronous activity\n    // is finished. This will be correctly consumed by the Mocha framework with\n    // it('...', async(myFn)); or can be used in a custom framework.\n    // Not using an arrow function to preserve context passed from call site\n    return function () {\n      return new Promise((finishCallback, failCallback) => {\n        runInTestZone(fn, this, finishCallback, failCallback);\n      });\n    };\n  };\n  function runInTestZone(fn, context, finishCallback, failCallback) {\n    const currentZone = Zone.current;\n    const AsyncTestZoneSpec = Zone['AsyncTestZoneSpec'];\n    if (AsyncTestZoneSpec === undefined) {\n      throw new Error('AsyncTestZoneSpec is needed for the async() test helper but could not be found. ' + 'Please make sure that your environment includes zone.js/plugins/async-test');\n    }\n    const ProxyZoneSpec = Zone['ProxyZoneSpec'];\n    if (!ProxyZoneSpec) {\n      throw new Error('ProxyZoneSpec is needed for the async() test helper but could not be found. ' + 'Please make sure that your environment includes zone.js/plugins/proxy');\n    }\n    const proxyZoneSpec = ProxyZoneSpec.get();\n    ProxyZoneSpec.assertPresent();\n    // We need to create the AsyncTestZoneSpec outside the ProxyZone.\n    // If we do it in ProxyZone then we will get to infinite recursion.\n    const proxyZone = Zone.current.getZoneWith('ProxyZoneSpec');\n    const previousDelegate = proxyZoneSpec.getDelegate();\n    proxyZone.parent.run(() => {\n      const testZoneSpec = new AsyncTestZoneSpec(() => {\n        // Need to restore the original zone.\n        if (proxyZoneSpec.getDelegate() == testZoneSpec) {\n          // Only reset the zone spec if it's\n          // still this one. Otherwise, assume\n          // it's OK.\n          proxyZoneSpec.setDelegate(previousDelegate);\n        }\n        testZoneSpec.unPatchPromiseForTest();\n        currentZone.run(() => {\n          finishCallback();\n        });\n      }, error => {\n        // Need to restore the original zone.\n        if (proxyZoneSpec.getDelegate() == testZoneSpec) {\n          // Only reset the zone spec if it's sill this one. Otherwise, assume it's OK.\n          proxyZoneSpec.setDelegate(previousDelegate);\n        }\n        testZoneSpec.unPatchPromiseForTest();\n        currentZone.run(() => {\n          failCallback(error);\n        });\n      }, 'test');\n      proxyZoneSpec.setDelegate(testZoneSpec);\n      testZoneSpec.patchPromiseForTest();\n    });\n    return Zone.current.runGuarded(fn, context);\n  }\n});\n(function (global) {\n  const OriginalDate = global.Date;\n  // Since when we compile this file to `es2015`, and if we define\n  // this `FakeDate` as `class FakeDate`, and then set `FakeDate.prototype`\n  // there will be an error which is `Cannot assign to read only property 'prototype'`\n  // so we need to use function implementation here.\n  function FakeDate() {\n    if (arguments.length === 0) {\n      const d = new OriginalDate();\n      d.setTime(FakeDate.now());\n      return d;\n    } else {\n      const args = Array.prototype.slice.call(arguments);\n      return new OriginalDate(...args);\n    }\n  }\n  FakeDate.now = function () {\n    const fakeAsyncTestZoneSpec = Zone.current.get('FakeAsyncTestZoneSpec');\n    if (fakeAsyncTestZoneSpec) {\n      return fakeAsyncTestZoneSpec.getFakeSystemTime();\n    }\n    return OriginalDate.now.apply(this, arguments);\n  };\n  FakeDate.UTC = OriginalDate.UTC;\n  FakeDate.parse = OriginalDate.parse;\n  // keep a reference for zone patched timer function\n  const timers = {\n    setTimeout: global.setTimeout,\n    setInterval: global.setInterval,\n    clearTimeout: global.clearTimeout,\n    clearInterval: global.clearInterval\n  };\n  class Scheduler {\n    // Next scheduler id.\n    static {\n      this.nextId = 1;\n    }\n    constructor() {\n      // Scheduler queue with the tuple of end time and callback function - sorted by end time.\n      this._schedulerQueue = [];\n      // Current simulated time in millis.\n      this._currentTickTime = 0;\n      // Current fake system base time in millis.\n      this._currentFakeBaseSystemTime = OriginalDate.now();\n      // track requeuePeriodicTimer\n      this._currentTickRequeuePeriodicEntries = [];\n    }\n    getCurrentTickTime() {\n      return this._currentTickTime;\n    }\n    getFakeSystemTime() {\n      return this._currentFakeBaseSystemTime + this._currentTickTime;\n    }\n    setFakeBaseSystemTime(fakeBaseSystemTime) {\n      this._currentFakeBaseSystemTime = fakeBaseSystemTime;\n    }\n    getRealSystemTime() {\n      return OriginalDate.now();\n    }\n    scheduleFunction(cb, delay, options) {\n      options = {\n        ...{\n          args: [],\n          isPeriodic: false,\n          isRequestAnimationFrame: false,\n          id: -1,\n          isRequeuePeriodic: false\n        },\n        ...options\n      };\n      let currentId = options.id < 0 ? Scheduler.nextId++ : options.id;\n      let endTime = this._currentTickTime + delay;\n      // Insert so that scheduler queue remains sorted by end time.\n      let newEntry = {\n        endTime: endTime,\n        id: currentId,\n        func: cb,\n        args: options.args,\n        delay: delay,\n        isPeriodic: options.isPeriodic,\n        isRequestAnimationFrame: options.isRequestAnimationFrame\n      };\n      if (options.isRequeuePeriodic) {\n        this._currentTickRequeuePeriodicEntries.push(newEntry);\n      }\n      let i = 0;\n      for (; i < this._schedulerQueue.length; i++) {\n        let currentEntry = this._schedulerQueue[i];\n        if (newEntry.endTime < currentEntry.endTime) {\n          break;\n        }\n      }\n      this._schedulerQueue.splice(i, 0, newEntry);\n      return currentId;\n    }\n    removeScheduledFunctionWithId(id) {\n      for (let i = 0; i < this._schedulerQueue.length; i++) {\n        if (this._schedulerQueue[i].id == id) {\n          this._schedulerQueue.splice(i, 1);\n          break;\n        }\n      }\n    }\n    removeAll() {\n      this._schedulerQueue = [];\n    }\n    getTimerCount() {\n      return this._schedulerQueue.length;\n    }\n    tickToNext(step = 1, doTick, tickOptions) {\n      if (this._schedulerQueue.length < step) {\n        return;\n      }\n      // Find the last task currently queued in the scheduler queue and tick\n      // till that time.\n      const startTime = this._currentTickTime;\n      const targetTask = this._schedulerQueue[step - 1];\n      this.tick(targetTask.endTime - startTime, doTick, tickOptions);\n    }\n    tick(millis = 0, doTick, tickOptions) {\n      let finalTime = this._currentTickTime + millis;\n      let lastCurrentTime = 0;\n      tickOptions = Object.assign({\n        processNewMacroTasksSynchronously: true\n      }, tickOptions);\n      // we need to copy the schedulerQueue so nested timeout\n      // will not be wrongly called in the current tick\n      // https://github.com/angular/angular/issues/33799\n      const schedulerQueue = tickOptions.processNewMacroTasksSynchronously ? this._schedulerQueue : this._schedulerQueue.slice();\n      if (schedulerQueue.length === 0 && doTick) {\n        doTick(millis);\n        return;\n      }\n      while (schedulerQueue.length > 0) {\n        // clear requeueEntries before each loop\n        this._currentTickRequeuePeriodicEntries = [];\n        let current = schedulerQueue[0];\n        if (finalTime < current.endTime) {\n          // Done processing the queue since it's sorted by endTime.\n          break;\n        } else {\n          // Time to run scheduled function. Remove it from the head of queue.\n          let current = schedulerQueue.shift();\n          if (!tickOptions.processNewMacroTasksSynchronously) {\n            const idx = this._schedulerQueue.indexOf(current);\n            if (idx >= 0) {\n              this._schedulerQueue.splice(idx, 1);\n            }\n          }\n          lastCurrentTime = this._currentTickTime;\n          this._currentTickTime = current.endTime;\n          if (doTick) {\n            doTick(this._currentTickTime - lastCurrentTime);\n          }\n          let retval = current.func.apply(global, current.isRequestAnimationFrame ? [this._currentTickTime] : current.args);\n          if (!retval) {\n            // Uncaught exception in the current scheduled function. Stop processing the queue.\n            break;\n          }\n          // check is there any requeue periodic entry is added in\n          // current loop, if there is, we need to add to current loop\n          if (!tickOptions.processNewMacroTasksSynchronously) {\n            this._currentTickRequeuePeriodicEntries.forEach(newEntry => {\n              let i = 0;\n              for (; i < schedulerQueue.length; i++) {\n                const currentEntry = schedulerQueue[i];\n                if (newEntry.endTime < currentEntry.endTime) {\n                  break;\n                }\n              }\n              schedulerQueue.splice(i, 0, newEntry);\n            });\n          }\n        }\n      }\n      lastCurrentTime = this._currentTickTime;\n      this._currentTickTime = finalTime;\n      if (doTick) {\n        doTick(this._currentTickTime - lastCurrentTime);\n      }\n    }\n    flushOnlyPendingTimers(doTick) {\n      if (this._schedulerQueue.length === 0) {\n        return 0;\n      }\n      // Find the last task currently queued in the scheduler queue and tick\n      // till that time.\n      const startTime = this._currentTickTime;\n      const lastTask = this._schedulerQueue[this._schedulerQueue.length - 1];\n      this.tick(lastTask.endTime - startTime, doTick, {\n        processNewMacroTasksSynchronously: false\n      });\n      return this._currentTickTime - startTime;\n    }\n    flush(limit = 20, flushPeriodic = false, doTick) {\n      if (flushPeriodic) {\n        return this.flushPeriodic(doTick);\n      } else {\n        return this.flushNonPeriodic(limit, doTick);\n      }\n    }\n    flushPeriodic(doTick) {\n      if (this._schedulerQueue.length === 0) {\n        return 0;\n      }\n      // Find the last task currently queued in the scheduler queue and tick\n      // till that time.\n      const startTime = this._currentTickTime;\n      const lastTask = this._schedulerQueue[this._schedulerQueue.length - 1];\n      this.tick(lastTask.endTime - startTime, doTick);\n      return this._currentTickTime - startTime;\n    }\n    flushNonPeriodic(limit, doTick) {\n      const startTime = this._currentTickTime;\n      let lastCurrentTime = 0;\n      let count = 0;\n      while (this._schedulerQueue.length > 0) {\n        count++;\n        if (count > limit) {\n          throw new Error('flush failed after reaching the limit of ' + limit + ' tasks. Does your code use a polling timeout?');\n        }\n        // flush only non-periodic timers.\n        // If the only remaining tasks are periodic(or requestAnimationFrame), finish flushing.\n        if (this._schedulerQueue.filter(task => !task.isPeriodic && !task.isRequestAnimationFrame).length === 0) {\n          break;\n        }\n        const current = this._schedulerQueue.shift();\n        lastCurrentTime = this._currentTickTime;\n        this._currentTickTime = current.endTime;\n        if (doTick) {\n          // Update any secondary schedulers like Jasmine mock Date.\n          doTick(this._currentTickTime - lastCurrentTime);\n        }\n        const retval = current.func.apply(global, current.args);\n        if (!retval) {\n          // Uncaught exception in the current scheduled function. Stop processing the queue.\n          break;\n        }\n      }\n      return this._currentTickTime - startTime;\n    }\n  }\n  class FakeAsyncTestZoneSpec {\n    static assertInZone() {\n      if (Zone.current.get('FakeAsyncTestZoneSpec') == null) {\n        throw new Error('The code should be running in the fakeAsync zone to call this function');\n      }\n    }\n    constructor(namePrefix, trackPendingRequestAnimationFrame = false, macroTaskOptions) {\n      this.trackPendingRequestAnimationFrame = trackPendingRequestAnimationFrame;\n      this.macroTaskOptions = macroTaskOptions;\n      this._scheduler = new Scheduler();\n      this._microtasks = [];\n      this._lastError = null;\n      this._uncaughtPromiseErrors = Promise[Zone.__symbol__('uncaughtPromiseErrors')];\n      this.pendingPeriodicTimers = [];\n      this.pendingTimers = [];\n      this.patchDateLocked = false;\n      this.properties = {\n        'FakeAsyncTestZoneSpec': this\n      };\n      this.name = 'fakeAsyncTestZone for ' + namePrefix;\n      // in case user can't access the construction of FakeAsyncTestSpec\n      // user can also define macroTaskOptions by define a global variable.\n      if (!this.macroTaskOptions) {\n        this.macroTaskOptions = global[Zone.__symbol__('FakeAsyncTestMacroTask')];\n      }\n    }\n    _fnAndFlush(fn, completers) {\n      return (...args) => {\n        fn.apply(global, args);\n        if (this._lastError === null) {\n          // Success\n          if (completers.onSuccess != null) {\n            completers.onSuccess.apply(global);\n          }\n          // Flush microtasks only on success.\n          this.flushMicrotasks();\n        } else {\n          // Failure\n          if (completers.onError != null) {\n            completers.onError.apply(global);\n          }\n        }\n        // Return true if there were no errors, false otherwise.\n        return this._lastError === null;\n      };\n    }\n    static _removeTimer(timers, id) {\n      let index = timers.indexOf(id);\n      if (index > -1) {\n        timers.splice(index, 1);\n      }\n    }\n    _dequeueTimer(id) {\n      return () => {\n        FakeAsyncTestZoneSpec._removeTimer(this.pendingTimers, id);\n      };\n    }\n    _requeuePeriodicTimer(fn, interval, args, id) {\n      return () => {\n        // Requeue the timer callback if it's not been canceled.\n        if (this.pendingPeriodicTimers.indexOf(id) !== -1) {\n          this._scheduler.scheduleFunction(fn, interval, {\n            args,\n            isPeriodic: true,\n            id,\n            isRequeuePeriodic: true\n          });\n        }\n      };\n    }\n    _dequeuePeriodicTimer(id) {\n      return () => {\n        FakeAsyncTestZoneSpec._removeTimer(this.pendingPeriodicTimers, id);\n      };\n    }\n    _setTimeout(fn, delay, args, isTimer = true) {\n      let removeTimerFn = this._dequeueTimer(Scheduler.nextId);\n      // Queue the callback and dequeue the timer on success and error.\n      let cb = this._fnAndFlush(fn, {\n        onSuccess: removeTimerFn,\n        onError: removeTimerFn\n      });\n      let id = this._scheduler.scheduleFunction(cb, delay, {\n        args,\n        isRequestAnimationFrame: !isTimer\n      });\n      if (isTimer) {\n        this.pendingTimers.push(id);\n      }\n      return id;\n    }\n    _clearTimeout(id) {\n      FakeAsyncTestZoneSpec._removeTimer(this.pendingTimers, id);\n      this._scheduler.removeScheduledFunctionWithId(id);\n    }\n    _setInterval(fn, interval, args) {\n      let id = Scheduler.nextId;\n      let completers = {\n        onSuccess: null,\n        onError: this._dequeuePeriodicTimer(id)\n      };\n      let cb = this._fnAndFlush(fn, completers);\n      // Use the callback created above to requeue on success.\n      completers.onSuccess = this._requeuePeriodicTimer(cb, interval, args, id);\n      // Queue the callback and dequeue the periodic timer only on error.\n      this._scheduler.scheduleFunction(cb, interval, {\n        args,\n        isPeriodic: true\n      });\n      this.pendingPeriodicTimers.push(id);\n      return id;\n    }\n    _clearInterval(id) {\n      FakeAsyncTestZoneSpec._removeTimer(this.pendingPeriodicTimers, id);\n      this._scheduler.removeScheduledFunctionWithId(id);\n    }\n    _resetLastErrorAndThrow() {\n      let error = this._lastError || this._uncaughtPromiseErrors[0];\n      this._uncaughtPromiseErrors.length = 0;\n      this._lastError = null;\n      throw error;\n    }\n    getCurrentTickTime() {\n      return this._scheduler.getCurrentTickTime();\n    }\n    getFakeSystemTime() {\n      return this._scheduler.getFakeSystemTime();\n    }\n    setFakeBaseSystemTime(realTime) {\n      this._scheduler.setFakeBaseSystemTime(realTime);\n    }\n    getRealSystemTime() {\n      return this._scheduler.getRealSystemTime();\n    }\n    static patchDate() {\n      if (!!global[Zone.__symbol__('disableDatePatching')]) {\n        // we don't want to patch global Date\n        // because in some case, global Date\n        // is already being patched, we need to provide\n        // an option to let user still use their\n        // own version of Date.\n        return;\n      }\n      if (global['Date'] === FakeDate) {\n        // already patched\n        return;\n      }\n      global['Date'] = FakeDate;\n      FakeDate.prototype = OriginalDate.prototype;\n      // try check and reset timers\n      // because jasmine.clock().install() may\n      // have replaced the global timer\n      FakeAsyncTestZoneSpec.checkTimerPatch();\n    }\n    static resetDate() {\n      if (global['Date'] === FakeDate) {\n        global['Date'] = OriginalDate;\n      }\n    }\n    static checkTimerPatch() {\n      if (global.setTimeout !== timers.setTimeout) {\n        global.setTimeout = timers.setTimeout;\n        global.clearTimeout = timers.clearTimeout;\n      }\n      if (global.setInterval !== timers.setInterval) {\n        global.setInterval = timers.setInterval;\n        global.clearInterval = timers.clearInterval;\n      }\n    }\n    lockDatePatch() {\n      this.patchDateLocked = true;\n      FakeAsyncTestZoneSpec.patchDate();\n    }\n    unlockDatePatch() {\n      this.patchDateLocked = false;\n      FakeAsyncTestZoneSpec.resetDate();\n    }\n    tickToNext(steps = 1, doTick, tickOptions = {\n      processNewMacroTasksSynchronously: true\n    }) {\n      if (steps <= 0) {\n        return;\n      }\n      FakeAsyncTestZoneSpec.assertInZone();\n      this.flushMicrotasks();\n      this._scheduler.tickToNext(steps, doTick, tickOptions);\n      if (this._lastError !== null) {\n        this._resetLastErrorAndThrow();\n      }\n    }\n    tick(millis = 0, doTick, tickOptions = {\n      processNewMacroTasksSynchronously: true\n    }) {\n      FakeAsyncTestZoneSpec.assertInZone();\n      this.flushMicrotasks();\n      this._scheduler.tick(millis, doTick, tickOptions);\n      if (this._lastError !== null) {\n        this._resetLastErrorAndThrow();\n      }\n    }\n    flushMicrotasks() {\n      FakeAsyncTestZoneSpec.assertInZone();\n      const flushErrors = () => {\n        if (this._lastError !== null || this._uncaughtPromiseErrors.length) {\n          // If there is an error stop processing the microtask queue and rethrow the error.\n          this._resetLastErrorAndThrow();\n        }\n      };\n      while (this._microtasks.length > 0) {\n        let microtask = this._microtasks.shift();\n        microtask.func.apply(microtask.target, microtask.args);\n      }\n      flushErrors();\n    }\n    flush(limit, flushPeriodic, doTick) {\n      FakeAsyncTestZoneSpec.assertInZone();\n      this.flushMicrotasks();\n      const elapsed = this._scheduler.flush(limit, flushPeriodic, doTick);\n      if (this._lastError !== null) {\n        this._resetLastErrorAndThrow();\n      }\n      return elapsed;\n    }\n    flushOnlyPendingTimers(doTick) {\n      FakeAsyncTestZoneSpec.assertInZone();\n      this.flushMicrotasks();\n      const elapsed = this._scheduler.flushOnlyPendingTimers(doTick);\n      if (this._lastError !== null) {\n        this._resetLastErrorAndThrow();\n      }\n      return elapsed;\n    }\n    removeAllTimers() {\n      FakeAsyncTestZoneSpec.assertInZone();\n      this._scheduler.removeAll();\n      this.pendingPeriodicTimers = [];\n      this.pendingTimers = [];\n    }\n    getTimerCount() {\n      return this._scheduler.getTimerCount() + this._microtasks.length;\n    }\n    onScheduleTask(delegate, current, target, task) {\n      switch (task.type) {\n        case 'microTask':\n          let args = task.data && task.data.args;\n          // should pass additional arguments to callback if have any\n          // currently we know process.nextTick will have such additional\n          // arguments\n          let additionalArgs;\n          if (args) {\n            let callbackIndex = task.data.cbIdx;\n            if (typeof args.length === 'number' && args.length > callbackIndex + 1) {\n              additionalArgs = Array.prototype.slice.call(args, callbackIndex + 1);\n            }\n          }\n          this._microtasks.push({\n            func: task.invoke,\n            args: additionalArgs,\n            target: task.data && task.data.target\n          });\n          break;\n        case 'macroTask':\n          switch (task.source) {\n            case 'setTimeout':\n              task.data['handleId'] = this._setTimeout(task.invoke, task.data['delay'], Array.prototype.slice.call(task.data['args'], 2));\n              break;\n            case 'setImmediate':\n              task.data['handleId'] = this._setTimeout(task.invoke, 0, Array.prototype.slice.call(task.data['args'], 1));\n              break;\n            case 'setInterval':\n              task.data['handleId'] = this._setInterval(task.invoke, task.data['delay'], Array.prototype.slice.call(task.data['args'], 2));\n              break;\n            case 'XMLHttpRequest.send':\n              throw new Error('Cannot make XHRs from within a fake async test. Request URL: ' + task.data['url']);\n            case 'requestAnimationFrame':\n            case 'webkitRequestAnimationFrame':\n            case 'mozRequestAnimationFrame':\n              // Simulate a requestAnimationFrame by using a setTimeout with 16 ms.\n              // (60 frames per second)\n              task.data['handleId'] = this._setTimeout(task.invoke, 16, task.data['args'], this.trackPendingRequestAnimationFrame);\n              break;\n            default:\n              // user can define which macroTask they want to support by passing\n              // macroTaskOptions\n              const macroTaskOption = this.findMacroTaskOption(task);\n              if (macroTaskOption) {\n                const args = task.data && task.data['args'];\n                const delay = args && args.length > 1 ? args[1] : 0;\n                let callbackArgs = macroTaskOption.callbackArgs ? macroTaskOption.callbackArgs : args;\n                if (!!macroTaskOption.isPeriodic) {\n                  // periodic macroTask, use setInterval to simulate\n                  task.data['handleId'] = this._setInterval(task.invoke, delay, callbackArgs);\n                  task.data.isPeriodic = true;\n                } else {\n                  // not periodic, use setTimeout to simulate\n                  task.data['handleId'] = this._setTimeout(task.invoke, delay, callbackArgs);\n                }\n                break;\n              }\n              throw new Error('Unknown macroTask scheduled in fake async test: ' + task.source);\n          }\n          break;\n        case 'eventTask':\n          task = delegate.scheduleTask(target, task);\n          break;\n      }\n      return task;\n    }\n    onCancelTask(delegate, current, target, task) {\n      switch (task.source) {\n        case 'setTimeout':\n        case 'requestAnimationFrame':\n        case 'webkitRequestAnimationFrame':\n        case 'mozRequestAnimationFrame':\n          return this._clearTimeout(task.data['handleId']);\n        case 'setInterval':\n          return this._clearInterval(task.data['handleId']);\n        default:\n          // user can define which macroTask they want to support by passing\n          // macroTaskOptions\n          const macroTaskOption = this.findMacroTaskOption(task);\n          if (macroTaskOption) {\n            const handleId = task.data['handleId'];\n            return macroTaskOption.isPeriodic ? this._clearInterval(handleId) : this._clearTimeout(handleId);\n          }\n          return delegate.cancelTask(target, task);\n      }\n    }\n    onInvoke(delegate, current, target, callback, applyThis, applyArgs, source) {\n      try {\n        FakeAsyncTestZoneSpec.patchDate();\n        return delegate.invoke(target, callback, applyThis, applyArgs, source);\n      } finally {\n        if (!this.patchDateLocked) {\n          FakeAsyncTestZoneSpec.resetDate();\n        }\n      }\n    }\n    findMacroTaskOption(task) {\n      if (!this.macroTaskOptions) {\n        return null;\n      }\n      for (let i = 0; i < this.macroTaskOptions.length; i++) {\n        const macroTaskOption = this.macroTaskOptions[i];\n        if (macroTaskOption.source === task.source) {\n          return macroTaskOption;\n        }\n      }\n      return null;\n    }\n    onHandleError(parentZoneDelegate, currentZone, targetZone, error) {\n      this._lastError = error;\n      return false; // Don't propagate error to parent zone.\n    }\n  }\n  // Export the class so that new instances can be created with proper\n  // constructor params.\n  Zone['FakeAsyncTestZoneSpec'] = FakeAsyncTestZoneSpec;\n})(typeof window === 'object' && window || typeof self === 'object' && self || global);\nZone.__load_patch('fakeasync', (global, Zone, api) => {\n  const FakeAsyncTestZoneSpec = Zone && Zone['FakeAsyncTestZoneSpec'];\n  function getProxyZoneSpec() {\n    return Zone && Zone['ProxyZoneSpec'];\n  }\n  let _fakeAsyncTestZoneSpec = null;\n  /**\n   * Clears out the shared fake async zone for a test.\n   * To be called in a global `beforeEach`.\n   *\n   * @experimental\n   */\n  function resetFakeAsyncZone() {\n    if (_fakeAsyncTestZoneSpec) {\n      _fakeAsyncTestZoneSpec.unlockDatePatch();\n    }\n    _fakeAsyncTestZoneSpec = null;\n    // in node.js testing we may not have ProxyZoneSpec in which case there is nothing to reset.\n    getProxyZoneSpec() && getProxyZoneSpec().assertPresent().resetDelegate();\n  }\n  /**\n   * Wraps a function to be executed in the fakeAsync zone:\n   * - microtasks are manually executed by calling `flushMicrotasks()`,\n   * - timers are synchronous, `tick()` simulates the asynchronous passage of time.\n   *\n   * If there are any pending timers at the end of the function, an exception will be thrown.\n   *\n   * Can be used to wrap inject() calls.\n   *\n   * ## Example\n   *\n   * {@example core/testing/ts/fake_async.ts region='basic'}\n   *\n   * @param fn\n   * @returns The function wrapped to be executed in the fakeAsync zone\n   *\n   * @experimental\n   */\n  function fakeAsync(fn) {\n    // Not using an arrow function to preserve context passed from call site\n    const fakeAsyncFn = function (...args) {\n      const ProxyZoneSpec = getProxyZoneSpec();\n      if (!ProxyZoneSpec) {\n        throw new Error('ProxyZoneSpec is needed for the async() test helper but could not be found. ' + 'Please make sure that your environment includes zone.js/plugins/proxy');\n      }\n      const proxyZoneSpec = ProxyZoneSpec.assertPresent();\n      if (Zone.current.get('FakeAsyncTestZoneSpec')) {\n        throw new Error('fakeAsync() calls can not be nested');\n      }\n      try {\n        // in case jasmine.clock init a fakeAsyncTestZoneSpec\n        if (!_fakeAsyncTestZoneSpec) {\n          if (proxyZoneSpec.getDelegate() instanceof FakeAsyncTestZoneSpec) {\n            throw new Error('fakeAsync() calls can not be nested');\n          }\n          _fakeAsyncTestZoneSpec = new FakeAsyncTestZoneSpec();\n        }\n        let res;\n        const lastProxyZoneSpec = proxyZoneSpec.getDelegate();\n        proxyZoneSpec.setDelegate(_fakeAsyncTestZoneSpec);\n        _fakeAsyncTestZoneSpec.lockDatePatch();\n        try {\n          res = fn.apply(this, args);\n          flushMicrotasks();\n        } finally {\n          proxyZoneSpec.setDelegate(lastProxyZoneSpec);\n        }\n        if (_fakeAsyncTestZoneSpec.pendingPeriodicTimers.length > 0) {\n          throw new Error(`${_fakeAsyncTestZoneSpec.pendingPeriodicTimers.length} ` + `periodic timer(s) still in the queue.`);\n        }\n        if (_fakeAsyncTestZoneSpec.pendingTimers.length > 0) {\n          throw new Error(`${_fakeAsyncTestZoneSpec.pendingTimers.length} timer(s) still in the queue.`);\n        }\n        return res;\n      } finally {\n        resetFakeAsyncZone();\n      }\n    };\n    fakeAsyncFn.isFakeAsync = true;\n    return fakeAsyncFn;\n  }\n  function _getFakeAsyncZoneSpec() {\n    if (_fakeAsyncTestZoneSpec == null) {\n      _fakeAsyncTestZoneSpec = Zone.current.get('FakeAsyncTestZoneSpec');\n      if (_fakeAsyncTestZoneSpec == null) {\n        throw new Error('The code should be running in the fakeAsync zone to call this function');\n      }\n    }\n    return _fakeAsyncTestZoneSpec;\n  }\n  /**\n   * Simulates the asynchronous passage of time for the timers in the fakeAsync zone.\n   *\n   * The microtasks queue is drained at the very start of this function and after any timer callback\n   * has been executed.\n   *\n   * ## Example\n   *\n   * {@example core/testing/ts/fake_async.ts region='basic'}\n   *\n   * @experimental\n   */\n  function tick(millis = 0, ignoreNestedTimeout = false) {\n    _getFakeAsyncZoneSpec().tick(millis, null, ignoreNestedTimeout);\n  }\n  /**\n   * Simulates the asynchronous passage of time for the timers in the fakeAsync zone by\n   * draining the macrotask queue until it is empty. The returned value is the milliseconds\n   * of time that would have been elapsed.\n   *\n   * @param maxTurns\n   * @returns The simulated time elapsed, in millis.\n   *\n   * @experimental\n   */\n  function flush(maxTurns) {\n    return _getFakeAsyncZoneSpec().flush(maxTurns);\n  }\n  /**\n   * Discard all remaining periodic tasks.\n   *\n   * @experimental\n   */\n  function discardPeriodicTasks() {\n    const zoneSpec = _getFakeAsyncZoneSpec();\n    zoneSpec.pendingPeriodicTimers;\n    zoneSpec.pendingPeriodicTimers.length = 0;\n  }\n  /**\n   * Flush any pending microtasks.\n   *\n   * @experimental\n   */\n  function flushMicrotasks() {\n    _getFakeAsyncZoneSpec().flushMicrotasks();\n  }\n  Zone[api.symbol('fakeAsyncTest')] = {\n    resetFakeAsyncZone,\n    flushMicrotasks,\n    discardPeriodicTasks,\n    tick,\n    flush,\n    fakeAsync\n  };\n}, true);\n\n/**\n * Promise for async/fakeAsync zoneSpec test\n * can support async operation which not supported by zone.js\n * such as\n * it ('test jsonp in AsyncZone', async() => {\n *   new Promise(res => {\n *     jsonp(url, (data) => {\n *       // success callback\n *       res(data);\n *     });\n *   }).then((jsonpResult) => {\n *     // get jsonp result.\n *\n *     // user will expect AsyncZoneSpec wait for\n *     // then, but because jsonp is not zone aware\n *     // AsyncZone will finish before then is called.\n *   });\n * });\n */\nZone.__load_patch('promisefortest', (global, Zone, api) => {\n  const symbolState = api.symbol('state');\n  const UNRESOLVED = null;\n  const symbolParentUnresolved = api.symbol('parentUnresolved');\n  // patch Promise.prototype.then to keep an internal\n  // number for tracking unresolved chained promise\n  // we will decrease this number when the parent promise\n  // being resolved/rejected and chained promise was\n  // scheduled as a microTask.\n  // so we can know such kind of chained promise still\n  // not resolved in AsyncTestZone\n  Promise[api.symbol('patchPromiseForTest')] = function patchPromiseForTest() {\n    let oriThen = Promise[Zone.__symbol__('ZonePromiseThen')];\n    if (oriThen) {\n      return;\n    }\n    oriThen = Promise[Zone.__symbol__('ZonePromiseThen')] = Promise.prototype.then;\n    Promise.prototype.then = function () {\n      const chained = oriThen.apply(this, arguments);\n      if (this[symbolState] === UNRESOLVED) {\n        // parent promise is unresolved.\n        const asyncTestZoneSpec = Zone.current.get('AsyncTestZoneSpec');\n        if (asyncTestZoneSpec) {\n          asyncTestZoneSpec.unresolvedChainedPromiseCount++;\n          chained[symbolParentUnresolved] = true;\n        }\n      }\n      return chained;\n    };\n  };\n  Promise[api.symbol('unPatchPromiseForTest')] = function unpatchPromiseForTest() {\n    // restore origin then\n    const oriThen = Promise[Zone.__symbol__('ZonePromiseThen')];\n    if (oriThen) {\n      Promise.prototype.then = oriThen;\n      Promise[Zone.__symbol__('ZonePromiseThen')] = undefined;\n    }\n  };\n});", "map": {"version": 3, "names": ["NEWLINE", "IGNORE_FRAMES", "creationTrace", "ERROR_TAG", "SEP_TAG", "sepTemplate", "LongStackTrace", "constructor", "error", "getStacktrace", "timestamp", "Date", "getStacktraceWithUncaughtError", "Error", "getStacktraceWithCaughtError", "err", "caughtError", "stack", "getFrames", "split", "addErrorStack", "lines", "trace", "i", "length", "frame", "hasOwnProperty", "push", "renderLongStackTrace", "frames", "longTrace", "trim", "getTime", "traceFrames", "lastTime", "separator", "replace", "join", "stackTracesEnabled", "stackTraceLimit", "Zone", "name", "longStackTraceLimit", "getLongStackTrace", "undefined", "__symbol__", "onScheduleTask", "parentZoneDelegate", "currentZone", "targetZone", "task", "currentTask", "data", "concat", "type", "scheduleTask", "onHandleError", "parentTask", "longStack", "handleError", "captureStackTraces", "stackTraces", "count", "computeIgnoreFrames", "frames1", "frames2", "frame1", "indexOf", "match", "frame2", "ProxyZoneSpec", "get", "current", "isLoaded", "assertPresent", "defaultSpecDelegate", "_delegateSpec", "properties", "propertyKeys", "lastTaskState", "isNeedToTriggerHasTask", "tasks", "setDelegate", "delegateSpec", "isNewDelegate", "for<PERSON>ach", "key", "Object", "keys", "k", "macroTask", "microTask", "getDelegate", "resetDelegate", "tryTriggerHasTask", "onHasTask", "removeFromTasks", "splice", "getAndClearPendingTasksInfo", "taskInfo", "map", "dataInfo", "source", "pendingTasksInfo", "onFork", "zoneSpec", "fork", "onIntercept", "delegate", "intercept", "onInvoke", "applyThis", "applyArgs", "invoke", "onInvokeTask", "invokeTask", "onCancelTask", "cancelTask", "target", "hasTaskState", "hasTask", "SyncTestZoneSpec", "namePrefix", "runZone", "__load_patch", "global", "api", "__extends", "d", "b", "p", "__", "prototype", "create", "jest", "jasmine", "ambientZone", "symbol", "disablePatchingJasmineClock", "enableAutoFakeAsyncWhenClockPatched", "ignoreUnhandledRejection", "globalErrors", "GlobalErrors", "instance", "originalInstall", "install", "isNode", "process", "on", "originalHandlers", "listeners", "eventListeners", "result", "apply", "arguments", "removeAllListeners", "handler", "addEventListener", "jasmineEnv", "getEnv", "methodName", "originalJasmineFn", "description", "specDefinitions", "call", "wrapDescribeInZone", "timeout", "wrapTestInZone", "originalClockFn", "clock", "originalTick", "tick", "fakeAsyncZoneSpec", "originalMockDate", "mockDate", "dateTime", "setFakeBaseSystemTime", "FakeAsyncTestZoneSpec", "originalCreateSpyObj", "createSpyObj", "args", "Array", "slice", "propertyNames", "spyObj", "defineProperty", "obj", "attributes", "configurable", "enumerable", "describeBody", "syncZone", "run", "runInTestZone", "testBody", "queueRunner", "done", "isClockInstalled", "testProxyZoneSpec", "testProxyZone", "fakeAsyncModule", "fakeAsync", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_super", "ZoneQueueRunner", "attrs", "onComplete", "fn", "scheduleMicroTask", "nativeSetTimeout", "nativeClearTimeout", "setTimeout", "clearTimeout", "UserContext", "userContext", "onException", "message", "proxyZoneSpec", "execute", "zone", "isChildOfAmbientZone", "parent", "context", "rootZone", "proxyZone", "wrapDescribeFactoryInZone", "originalJestFn", "tableArgs", "originalDescribeFn", "wrapTestFactoryInZone", "isTestFunc", "wrappedFunc", "isFakeAsync", "writable", "each", "describe", "only", "fdescribe", "skip", "xdescribe", "todo", "it", "fit", "xit", "test", "patchJestObject", "Timer", "isModern", "isPatchingFakeTimer", "isInTestFunc", "patchMethod", "self", "getRealSystemTime", "flushMicrotasks", "flush", "flushOnlyPendingTimers", "tickToNext", "removeAllTimers", "getTimerCount", "<PERSON><PERSON>", "testZone", "suiteZone", "mochaOriginal", "after", "after<PERSON>ach", "before", "beforeEach", "modifyArguments", "syncTest", "asyncTest", "arg", "toString", "wrapSuiteInZone", "suite", "specify", "xspecify", "suiteTeardown", "teardown", "suiteSetup", "setup", "originalRunTest", "originalRun", "Runner", "runTest", "e", "_global", "AsyncTestZoneSpec", "symbolParentUnresolved", "finishCallback", "fail<PERSON><PERSON>back", "_pendingMicroTasks", "_pendingMacroTasks", "_alreadyErrored", "_isSync", "_existingFinishTimer", "entryFunction", "unresolvedChainedPromiseCount", "supportWaitUnresolvedChainedPromise", "isUnresolvedChainedPromisePending", "_finishCallbackIfDone", "patchPromiseForTest", "Promise", "unPatchPromiseForTest", "change", "window", "fail", "getZoneWith", "previousDelegate", "testZoneSpec", "runGuarded", "OriginalDate", "FakeDate", "setTime", "now", "fakeAsyncTestZoneSpec", "getFakeSystemTime", "UTC", "parse", "timers", "setInterval", "clearInterval", "Scheduler", "nextId", "_schedulerQueue", "_currentTickTime", "_currentFakeBaseSystemTime", "_currentTickRequeuePeriodicEntries", "getCurrentTickTime", "fakeBaseSystemTime", "scheduleFunction", "cb", "delay", "options", "isPeriodic", "isRequestAnimationFrame", "id", "isRequeuePeriodic", "currentId", "endTime", "newEntry", "func", "currentEntry", "removeScheduledFunctionWithId", "removeAll", "step", "doTick", "tickOptions", "startTime", "targetTask", "millis", "finalTime", "lastCurrentTime", "assign", "processNewMacroTasksSynchronously", "schedulerQueue", "shift", "idx", "retval", "lastTask", "limit", "flushPeriodic", "flushNonPeriodic", "filter", "assertInZone", "trackPendingRequestAnimationFrame", "macroTaskOptions", "_scheduler", "_microtasks", "_lastError", "_uncaughtPromiseErrors", "pendingPeriodicTimers", "pendingTimers", "patchDateLocked", "_fnAndFlush", "completers", "onSuccess", "onError", "_removeTimer", "index", "_dequeueTimer", "_requeuePeriodicTimer", "interval", "_dequeuePeriodicTimer", "_setTimeout", "isTimer", "removeTimerFn", "_clearTimeout", "_setInterval", "_clearInterval", "_resetLastErrorAndThrow", "realTime", "patchDate", "checkTimerPatch", "resetDate", "lockDatePatch", "unlockDatePatch", "steps", "flushErrors", "microtask", "elapsed", "additionalArgs", "callbackIndex", "cbIdx", "macroTaskOption", "findMacroTaskOption", "callback<PERSON><PERSON><PERSON>", "handleId", "callback", "getProxyZoneSpec", "_fakeAsyncTestZoneSpec", "resetFakeAsyncZone", "fakeAsyncFn", "res", "lastProxyZoneSpec", "_getFakeAsyncZoneSpec", "ignoreNestedTimeout", "maxTurns", "discardPeriodicTasks", "symbolState", "UNRESOLVED", "ori<PERSON><PERSON>", "then", "chained", "asyncTestZoneSpec", "unpatchPromiseForTest"], "sources": ["C:/Users/<USER>/Desktop/angular/front/node_modules/zone.js/fesm2015/zone-testing.js"], "sourcesContent": ["'use strict';\n/**\n * @license Angular v<unknown>\n * (c) 2010-2022 Google LLC. https://angular.io/\n * License: MIT\n */\n/**\n * @fileoverview\n * @suppress {globalThis}\n */\nconst NEWLINE = '\\n';\nconst IGNORE_FRAMES = {};\nconst creationTrace = '__creationTrace__';\nconst ERROR_TAG = 'STACKTRACE TRACKING';\nconst SEP_TAG = '__SEP_TAG__';\nlet sepTemplate = SEP_TAG + '@[native]';\nclass LongStackTrace {\n    constructor() {\n        this.error = getStacktrace();\n        this.timestamp = new Date();\n    }\n}\nfunction getStacktraceWithUncaughtError() {\n    return new Error(ERROR_TAG);\n}\nfunction getStacktraceWithCaughtError() {\n    try {\n        throw getStacktraceWithUncaughtError();\n    }\n    catch (err) {\n        return err;\n    }\n}\n// Some implementations of exception handling don't create a stack trace if the exception\n// isn't thrown, however it's faster not to actually throw the exception.\nconst error = getStacktraceWithUncaughtError();\nconst caughtError = getStacktraceWithCaughtError();\nconst getStacktrace = error.stack ?\n    getStacktraceWithUncaughtError :\n    (caughtError.stack ? getStacktraceWithCaughtError : getStacktraceWithUncaughtError);\nfunction getFrames(error) {\n    return error.stack ? error.stack.split(NEWLINE) : [];\n}\nfunction addErrorStack(lines, error) {\n    let trace = getFrames(error);\n    for (let i = 0; i < trace.length; i++) {\n        const frame = trace[i];\n        // Filter out the Frames which are part of stack capturing.\n        if (!IGNORE_FRAMES.hasOwnProperty(frame)) {\n            lines.push(trace[i]);\n        }\n    }\n}\nfunction renderLongStackTrace(frames, stack) {\n    const longTrace = [stack ? stack.trim() : ''];\n    if (frames) {\n        let timestamp = new Date().getTime();\n        for (let i = 0; i < frames.length; i++) {\n            const traceFrames = frames[i];\n            const lastTime = traceFrames.timestamp;\n            let separator = `____________________Elapsed ${timestamp - lastTime.getTime()} ms; At: ${lastTime}`;\n            separator = separator.replace(/[^\\w\\d]/g, '_');\n            longTrace.push(sepTemplate.replace(SEP_TAG, separator));\n            addErrorStack(longTrace, traceFrames.error);\n            timestamp = lastTime.getTime();\n        }\n    }\n    return longTrace.join(NEWLINE);\n}\n// if Error.stackTraceLimit is 0, means stack trace\n// is disabled, so we don't need to generate long stack trace\n// this will improve performance in some test(some test will\n// set stackTraceLimit to 0, https://github.com/angular/zone.js/issues/698\nfunction stackTracesEnabled() {\n    // Cast through any since this property only exists on Error in the nodejs\n    // typings.\n    return Error.stackTraceLimit > 0;\n}\nZone['longStackTraceZoneSpec'] = {\n    name: 'long-stack-trace',\n    longStackTraceLimit: 10,\n    // add a getLongStackTrace method in spec to\n    // handle handled reject promise error.\n    getLongStackTrace: function (error) {\n        if (!error) {\n            return undefined;\n        }\n        const trace = error[Zone.__symbol__('currentTaskTrace')];\n        if (!trace) {\n            return error.stack;\n        }\n        return renderLongStackTrace(trace, error.stack);\n    },\n    onScheduleTask: function (parentZoneDelegate, currentZone, targetZone, task) {\n        if (stackTracesEnabled()) {\n            const currentTask = Zone.currentTask;\n            let trace = currentTask && currentTask.data && currentTask.data[creationTrace] || [];\n            trace = [new LongStackTrace()].concat(trace);\n            if (trace.length > this.longStackTraceLimit) {\n                trace.length = this.longStackTraceLimit;\n            }\n            if (!task.data)\n                task.data = {};\n            if (task.type === 'eventTask') {\n                // Fix issue https://github.com/angular/zone.js/issues/1195,\n                // For event task of browser, by default, all task will share a\n                // singleton instance of data object, we should create a new one here\n                // The cast to `any` is required to workaround a closure bug which wrongly applies\n                // URL sanitization rules to .data access.\n                task.data = { ...task.data };\n            }\n            task.data[creationTrace] = trace;\n        }\n        return parentZoneDelegate.scheduleTask(targetZone, task);\n    },\n    onHandleError: function (parentZoneDelegate, currentZone, targetZone, error) {\n        if (stackTracesEnabled()) {\n            const parentTask = Zone.currentTask || error.task;\n            if (error instanceof Error && parentTask) {\n                const longStack = renderLongStackTrace(parentTask.data && parentTask.data[creationTrace], error.stack);\n                try {\n                    error.stack = error.longStack = longStack;\n                }\n                catch (err) {\n                }\n            }\n        }\n        return parentZoneDelegate.handleError(targetZone, error);\n    }\n};\nfunction captureStackTraces(stackTraces, count) {\n    if (count > 0) {\n        stackTraces.push(getFrames((new LongStackTrace()).error));\n        captureStackTraces(stackTraces, count - 1);\n    }\n}\nfunction computeIgnoreFrames() {\n    if (!stackTracesEnabled()) {\n        return;\n    }\n    const frames = [];\n    captureStackTraces(frames, 2);\n    const frames1 = frames[0];\n    const frames2 = frames[1];\n    for (let i = 0; i < frames1.length; i++) {\n        const frame1 = frames1[i];\n        if (frame1.indexOf(ERROR_TAG) == -1) {\n            let match = frame1.match(/^\\s*at\\s+/);\n            if (match) {\n                sepTemplate = match[0] + SEP_TAG + ' (http://localhost)';\n                break;\n            }\n        }\n    }\n    for (let i = 0; i < frames1.length; i++) {\n        const frame1 = frames1[i];\n        const frame2 = frames2[i];\n        if (frame1 === frame2) {\n            IGNORE_FRAMES[frame1] = true;\n        }\n        else {\n            break;\n        }\n    }\n}\ncomputeIgnoreFrames();\n\nclass ProxyZoneSpec {\n    static get() {\n        return Zone.current.get('ProxyZoneSpec');\n    }\n    static isLoaded() {\n        return ProxyZoneSpec.get() instanceof ProxyZoneSpec;\n    }\n    static assertPresent() {\n        if (!ProxyZoneSpec.isLoaded()) {\n            throw new Error(`Expected to be running in 'ProxyZone', but it was not found.`);\n        }\n        return ProxyZoneSpec.get();\n    }\n    constructor(defaultSpecDelegate = null) {\n        this.defaultSpecDelegate = defaultSpecDelegate;\n        this.name = 'ProxyZone';\n        this._delegateSpec = null;\n        this.properties = { 'ProxyZoneSpec': this };\n        this.propertyKeys = null;\n        this.lastTaskState = null;\n        this.isNeedToTriggerHasTask = false;\n        this.tasks = [];\n        this.setDelegate(defaultSpecDelegate);\n    }\n    setDelegate(delegateSpec) {\n        const isNewDelegate = this._delegateSpec !== delegateSpec;\n        this._delegateSpec = delegateSpec;\n        this.propertyKeys && this.propertyKeys.forEach((key) => delete this.properties[key]);\n        this.propertyKeys = null;\n        if (delegateSpec && delegateSpec.properties) {\n            this.propertyKeys = Object.keys(delegateSpec.properties);\n            this.propertyKeys.forEach((k) => this.properties[k] = delegateSpec.properties[k]);\n        }\n        // if a new delegateSpec was set, check if we need to trigger hasTask\n        if (isNewDelegate && this.lastTaskState &&\n            (this.lastTaskState.macroTask || this.lastTaskState.microTask)) {\n            this.isNeedToTriggerHasTask = true;\n        }\n    }\n    getDelegate() {\n        return this._delegateSpec;\n    }\n    resetDelegate() {\n        this.getDelegate();\n        this.setDelegate(this.defaultSpecDelegate);\n    }\n    tryTriggerHasTask(parentZoneDelegate, currentZone, targetZone) {\n        if (this.isNeedToTriggerHasTask && this.lastTaskState) {\n            // last delegateSpec has microTask or macroTask\n            // should call onHasTask in current delegateSpec\n            this.isNeedToTriggerHasTask = false;\n            this.onHasTask(parentZoneDelegate, currentZone, targetZone, this.lastTaskState);\n        }\n    }\n    removeFromTasks(task) {\n        if (!this.tasks) {\n            return;\n        }\n        for (let i = 0; i < this.tasks.length; i++) {\n            if (this.tasks[i] === task) {\n                this.tasks.splice(i, 1);\n                return;\n            }\n        }\n    }\n    getAndClearPendingTasksInfo() {\n        if (this.tasks.length === 0) {\n            return '';\n        }\n        const taskInfo = this.tasks.map((task) => {\n            const dataInfo = task.data &&\n                Object.keys(task.data)\n                    .map((key) => {\n                    return key + ':' + task.data[key];\n                })\n                    .join(',');\n            return `type: ${task.type}, source: ${task.source}, args: {${dataInfo}}`;\n        });\n        const pendingTasksInfo = '--Pending async tasks are: [' + taskInfo + ']';\n        // clear tasks\n        this.tasks = [];\n        return pendingTasksInfo;\n    }\n    onFork(parentZoneDelegate, currentZone, targetZone, zoneSpec) {\n        if (this._delegateSpec && this._delegateSpec.onFork) {\n            return this._delegateSpec.onFork(parentZoneDelegate, currentZone, targetZone, zoneSpec);\n        }\n        else {\n            return parentZoneDelegate.fork(targetZone, zoneSpec);\n        }\n    }\n    onIntercept(parentZoneDelegate, currentZone, targetZone, delegate, source) {\n        if (this._delegateSpec && this._delegateSpec.onIntercept) {\n            return this._delegateSpec.onIntercept(parentZoneDelegate, currentZone, targetZone, delegate, source);\n        }\n        else {\n            return parentZoneDelegate.intercept(targetZone, delegate, source);\n        }\n    }\n    onInvoke(parentZoneDelegate, currentZone, targetZone, delegate, applyThis, applyArgs, source) {\n        this.tryTriggerHasTask(parentZoneDelegate, currentZone, targetZone);\n        if (this._delegateSpec && this._delegateSpec.onInvoke) {\n            return this._delegateSpec.onInvoke(parentZoneDelegate, currentZone, targetZone, delegate, applyThis, applyArgs, source);\n        }\n        else {\n            return parentZoneDelegate.invoke(targetZone, delegate, applyThis, applyArgs, source);\n        }\n    }\n    onHandleError(parentZoneDelegate, currentZone, targetZone, error) {\n        if (this._delegateSpec && this._delegateSpec.onHandleError) {\n            return this._delegateSpec.onHandleError(parentZoneDelegate, currentZone, targetZone, error);\n        }\n        else {\n            return parentZoneDelegate.handleError(targetZone, error);\n        }\n    }\n    onScheduleTask(parentZoneDelegate, currentZone, targetZone, task) {\n        if (task.type !== 'eventTask') {\n            this.tasks.push(task);\n        }\n        if (this._delegateSpec && this._delegateSpec.onScheduleTask) {\n            return this._delegateSpec.onScheduleTask(parentZoneDelegate, currentZone, targetZone, task);\n        }\n        else {\n            return parentZoneDelegate.scheduleTask(targetZone, task);\n        }\n    }\n    onInvokeTask(parentZoneDelegate, currentZone, targetZone, task, applyThis, applyArgs) {\n        if (task.type !== 'eventTask') {\n            this.removeFromTasks(task);\n        }\n        this.tryTriggerHasTask(parentZoneDelegate, currentZone, targetZone);\n        if (this._delegateSpec && this._delegateSpec.onInvokeTask) {\n            return this._delegateSpec.onInvokeTask(parentZoneDelegate, currentZone, targetZone, task, applyThis, applyArgs);\n        }\n        else {\n            return parentZoneDelegate.invokeTask(targetZone, task, applyThis, applyArgs);\n        }\n    }\n    onCancelTask(parentZoneDelegate, currentZone, targetZone, task) {\n        if (task.type !== 'eventTask') {\n            this.removeFromTasks(task);\n        }\n        this.tryTriggerHasTask(parentZoneDelegate, currentZone, targetZone);\n        if (this._delegateSpec && this._delegateSpec.onCancelTask) {\n            return this._delegateSpec.onCancelTask(parentZoneDelegate, currentZone, targetZone, task);\n        }\n        else {\n            return parentZoneDelegate.cancelTask(targetZone, task);\n        }\n    }\n    onHasTask(delegate, current, target, hasTaskState) {\n        this.lastTaskState = hasTaskState;\n        if (this._delegateSpec && this._delegateSpec.onHasTask) {\n            this._delegateSpec.onHasTask(delegate, current, target, hasTaskState);\n        }\n        else {\n            delegate.hasTask(target, hasTaskState);\n        }\n    }\n}\n// Export the class so that new instances can be created with proper\n// constructor params.\nZone['ProxyZoneSpec'] = ProxyZoneSpec;\n\nclass SyncTestZoneSpec {\n    constructor(namePrefix) {\n        this.runZone = Zone.current;\n        this.name = 'syncTestZone for ' + namePrefix;\n    }\n    onScheduleTask(delegate, current, target, task) {\n        switch (task.type) {\n            case 'microTask':\n            case 'macroTask':\n                throw new Error(`Cannot call ${task.source} from within a sync test (${this.name}).`);\n            case 'eventTask':\n                task = delegate.scheduleTask(target, task);\n                break;\n        }\n        return task;\n    }\n}\n// Export the class so that new instances can be created with proper\n// constructor params.\nZone['SyncTestZoneSpec'] = SyncTestZoneSpec;\n\n/// <reference types=\"jasmine\"/>\nZone.__load_patch('jasmine', (global, Zone, api) => {\n    const __extends = function (d, b) {\n        for (const p in b)\n            if (b.hasOwnProperty(p))\n                d[p] = b[p];\n        function __() {\n            this.constructor = d;\n        }\n        d.prototype = b === null ? Object.create(b) : ((__.prototype = b.prototype), new __());\n    };\n    // Patch jasmine's describe/it/beforeEach/afterEach functions so test code always runs\n    // in a testZone (ProxyZone). (See: angular/zone.js#91 & angular/angular#10503)\n    if (!Zone)\n        throw new Error('Missing: zone.js');\n    if (typeof jest !== 'undefined') {\n        // return if jasmine is a light implementation inside jest\n        // in this case, we are running inside jest not jasmine\n        return;\n    }\n    if (typeof jasmine == 'undefined' || jasmine['__zone_patch__']) {\n        return;\n    }\n    jasmine['__zone_patch__'] = true;\n    const SyncTestZoneSpec = Zone['SyncTestZoneSpec'];\n    const ProxyZoneSpec = Zone['ProxyZoneSpec'];\n    if (!SyncTestZoneSpec)\n        throw new Error('Missing: SyncTestZoneSpec');\n    if (!ProxyZoneSpec)\n        throw new Error('Missing: ProxyZoneSpec');\n    const ambientZone = Zone.current;\n    const symbol = Zone.__symbol__;\n    // whether patch jasmine clock when in fakeAsync\n    const disablePatchingJasmineClock = global[symbol('fakeAsyncDisablePatchingClock')] === true;\n    // the original variable name fakeAsyncPatchLock is not accurate, so the name will be\n    // fakeAsyncAutoFakeAsyncWhenClockPatched and if this enablePatchingJasmineClock is false, we also\n    // automatically disable the auto jump into fakeAsync feature\n    const enableAutoFakeAsyncWhenClockPatched = !disablePatchingJasmineClock &&\n        ((global[symbol('fakeAsyncPatchLock')] === true) ||\n            (global[symbol('fakeAsyncAutoFakeAsyncWhenClockPatched')] === true));\n    const ignoreUnhandledRejection = global[symbol('ignoreUnhandledRejection')] === true;\n    if (!ignoreUnhandledRejection) {\n        const globalErrors = jasmine.GlobalErrors;\n        if (globalErrors && !jasmine[symbol('GlobalErrors')]) {\n            jasmine[symbol('GlobalErrors')] = globalErrors;\n            jasmine.GlobalErrors = function () {\n                const instance = new globalErrors();\n                const originalInstall = instance.install;\n                if (originalInstall && !instance[symbol('install')]) {\n                    instance[symbol('install')] = originalInstall;\n                    instance.install = function () {\n                        const isNode = typeof process !== 'undefined' && !!process.on;\n                        // Note: Jasmine checks internally if `process` and `process.on` is defined. Otherwise,\n                        // it installs the browser rejection handler through the `global.addEventListener`.\n                        // This code may be run in the browser environment where `process` is not defined, and\n                        // this will lead to a runtime exception since Webpack 5 removed automatic Node.js\n                        // polyfills. Note, that events are named differently, it's `unhandledRejection` in\n                        // Node.js and `unhandledrejection` in the browser.\n                        const originalHandlers = isNode ? process.listeners('unhandledRejection') :\n                            global.eventListeners('unhandledrejection');\n                        const result = originalInstall.apply(this, arguments);\n                        isNode ? process.removeAllListeners('unhandledRejection') :\n                            global.removeAllListeners('unhandledrejection');\n                        if (originalHandlers) {\n                            originalHandlers.forEach(handler => {\n                                if (isNode) {\n                                    process.on('unhandledRejection', handler);\n                                }\n                                else {\n                                    global.addEventListener('unhandledrejection', handler);\n                                }\n                            });\n                        }\n                        return result;\n                    };\n                }\n                return instance;\n            };\n        }\n    }\n    // Monkey patch all of the jasmine DSL so that each function runs in appropriate zone.\n    const jasmineEnv = jasmine.getEnv();\n    ['describe', 'xdescribe', 'fdescribe'].forEach(methodName => {\n        let originalJasmineFn = jasmineEnv[methodName];\n        jasmineEnv[methodName] = function (description, specDefinitions) {\n            return originalJasmineFn.call(this, description, wrapDescribeInZone(description, specDefinitions));\n        };\n    });\n    ['it', 'xit', 'fit'].forEach(methodName => {\n        let originalJasmineFn = jasmineEnv[methodName];\n        jasmineEnv[symbol(methodName)] = originalJasmineFn;\n        jasmineEnv[methodName] = function (description, specDefinitions, timeout) {\n            arguments[1] = wrapTestInZone(specDefinitions);\n            return originalJasmineFn.apply(this, arguments);\n        };\n    });\n    ['beforeEach', 'afterEach', 'beforeAll', 'afterAll'].forEach(methodName => {\n        let originalJasmineFn = jasmineEnv[methodName];\n        jasmineEnv[symbol(methodName)] = originalJasmineFn;\n        jasmineEnv[methodName] = function (specDefinitions, timeout) {\n            arguments[0] = wrapTestInZone(specDefinitions);\n            return originalJasmineFn.apply(this, arguments);\n        };\n    });\n    if (!disablePatchingJasmineClock) {\n        // need to patch jasmine.clock().mockDate and jasmine.clock().tick() so\n        // they can work properly in FakeAsyncTest\n        const originalClockFn = (jasmine[symbol('clock')] = jasmine['clock']);\n        jasmine['clock'] = function () {\n            const clock = originalClockFn.apply(this, arguments);\n            if (!clock[symbol('patched')]) {\n                clock[symbol('patched')] = symbol('patched');\n                const originalTick = (clock[symbol('tick')] = clock.tick);\n                clock.tick = function () {\n                    const fakeAsyncZoneSpec = Zone.current.get('FakeAsyncTestZoneSpec');\n                    if (fakeAsyncZoneSpec) {\n                        return fakeAsyncZoneSpec.tick.apply(fakeAsyncZoneSpec, arguments);\n                    }\n                    return originalTick.apply(this, arguments);\n                };\n                const originalMockDate = (clock[symbol('mockDate')] = clock.mockDate);\n                clock.mockDate = function () {\n                    const fakeAsyncZoneSpec = Zone.current.get('FakeAsyncTestZoneSpec');\n                    if (fakeAsyncZoneSpec) {\n                        const dateTime = arguments.length > 0 ? arguments[0] : new Date();\n                        return fakeAsyncZoneSpec.setFakeBaseSystemTime.apply(fakeAsyncZoneSpec, dateTime && typeof dateTime.getTime === 'function' ? [dateTime.getTime()] :\n                            arguments);\n                    }\n                    return originalMockDate.apply(this, arguments);\n                };\n                // for auto go into fakeAsync feature, we need the flag to enable it\n                if (enableAutoFakeAsyncWhenClockPatched) {\n                    ['install', 'uninstall'].forEach(methodName => {\n                        const originalClockFn = (clock[symbol(methodName)] = clock[methodName]);\n                        clock[methodName] = function () {\n                            const FakeAsyncTestZoneSpec = Zone['FakeAsyncTestZoneSpec'];\n                            if (FakeAsyncTestZoneSpec) {\n                                jasmine[symbol('clockInstalled')] = 'install' === methodName;\n                                return;\n                            }\n                            return originalClockFn.apply(this, arguments);\n                        };\n                    });\n                }\n            }\n            return clock;\n        };\n    }\n    // monkey patch createSpyObj to make properties enumerable to true\n    if (!jasmine[Zone.__symbol__('createSpyObj')]) {\n        const originalCreateSpyObj = jasmine.createSpyObj;\n        jasmine[Zone.__symbol__('createSpyObj')] = originalCreateSpyObj;\n        jasmine.createSpyObj = function () {\n            const args = Array.prototype.slice.call(arguments);\n            const propertyNames = args.length >= 3 ? args[2] : null;\n            let spyObj;\n            if (propertyNames) {\n                const defineProperty = Object.defineProperty;\n                Object.defineProperty = function (obj, p, attributes) {\n                    return defineProperty.call(this, obj, p, { ...attributes, configurable: true, enumerable: true });\n                };\n                try {\n                    spyObj = originalCreateSpyObj.apply(this, args);\n                }\n                finally {\n                    Object.defineProperty = defineProperty;\n                }\n            }\n            else {\n                spyObj = originalCreateSpyObj.apply(this, args);\n            }\n            return spyObj;\n        };\n    }\n    /**\n     * Gets a function wrapping the body of a Jasmine `describe` block to execute in a\n     * synchronous-only zone.\n     */\n    function wrapDescribeInZone(description, describeBody) {\n        return function () {\n            // Create a synchronous-only zone in which to run `describe` blocks in order to raise an\n            // error if any asynchronous operations are attempted inside of a `describe`.\n            const syncZone = ambientZone.fork(new SyncTestZoneSpec(`jasmine.describe#${description}`));\n            return syncZone.run(describeBody, this, arguments);\n        };\n    }\n    function runInTestZone(testBody, applyThis, queueRunner, done) {\n        const isClockInstalled = !!jasmine[symbol('clockInstalled')];\n        queueRunner.testProxyZoneSpec;\n        const testProxyZone = queueRunner.testProxyZone;\n        if (isClockInstalled && enableAutoFakeAsyncWhenClockPatched) {\n            // auto run a fakeAsync\n            const fakeAsyncModule = Zone[Zone.__symbol__('fakeAsyncTest')];\n            if (fakeAsyncModule && typeof fakeAsyncModule.fakeAsync === 'function') {\n                testBody = fakeAsyncModule.fakeAsync(testBody);\n            }\n        }\n        if (done) {\n            return testProxyZone.run(testBody, applyThis, [done]);\n        }\n        else {\n            return testProxyZone.run(testBody, applyThis);\n        }\n    }\n    /**\n     * Gets a function wrapping the body of a Jasmine `it/beforeEach/afterEach` block to\n     * execute in a ProxyZone zone.\n     * This will run in `testProxyZone`. The `testProxyZone` will be reset by the `ZoneQueueRunner`\n     */\n    function wrapTestInZone(testBody) {\n        // The `done` callback is only passed through if the function expects at least one argument.\n        // Note we have to make a function with correct number of arguments, otherwise jasmine will\n        // think that all functions are sync or async.\n        return (testBody && (testBody.length ? function (done) {\n            return runInTestZone(testBody, this, this.queueRunner, done);\n        } : function () {\n            return runInTestZone(testBody, this, this.queueRunner);\n        }));\n    }\n    const QueueRunner = jasmine.QueueRunner;\n    jasmine.QueueRunner = (function (_super) {\n        __extends(ZoneQueueRunner, _super);\n        function ZoneQueueRunner(attrs) {\n            if (attrs.onComplete) {\n                attrs.onComplete = (fn => () => {\n                    // All functions are done, clear the test zone.\n                    this.testProxyZone = null;\n                    this.testProxyZoneSpec = null;\n                    ambientZone.scheduleMicroTask('jasmine.onComplete', fn);\n                })(attrs.onComplete);\n            }\n            const nativeSetTimeout = global[Zone.__symbol__('setTimeout')];\n            const nativeClearTimeout = global[Zone.__symbol__('clearTimeout')];\n            if (nativeSetTimeout) {\n                // should run setTimeout inside jasmine outside of zone\n                attrs.timeout = {\n                    setTimeout: nativeSetTimeout ? nativeSetTimeout : global.setTimeout,\n                    clearTimeout: nativeClearTimeout ? nativeClearTimeout : global.clearTimeout\n                };\n            }\n            // create a userContext to hold the queueRunner itself\n            // so we can access the testProxy in it/xit/beforeEach ...\n            if (jasmine.UserContext) {\n                if (!attrs.userContext) {\n                    attrs.userContext = new jasmine.UserContext();\n                }\n                attrs.userContext.queueRunner = this;\n            }\n            else {\n                if (!attrs.userContext) {\n                    attrs.userContext = {};\n                }\n                attrs.userContext.queueRunner = this;\n            }\n            // patch attrs.onException\n            const onException = attrs.onException;\n            attrs.onException = function (error) {\n                if (error &&\n                    error.message ===\n                        'Timeout - Async callback was not invoked within timeout specified by jasmine.DEFAULT_TIMEOUT_INTERVAL.') {\n                    // jasmine timeout, we can make the error message more\n                    // reasonable to tell what tasks are pending\n                    const proxyZoneSpec = this && this.testProxyZoneSpec;\n                    if (proxyZoneSpec) {\n                        const pendingTasksInfo = proxyZoneSpec.getAndClearPendingTasksInfo();\n                        try {\n                            // try catch here in case error.message is not writable\n                            error.message += pendingTasksInfo;\n                        }\n                        catch (err) {\n                        }\n                    }\n                }\n                if (onException) {\n                    onException.call(this, error);\n                }\n            };\n            _super.call(this, attrs);\n        }\n        ZoneQueueRunner.prototype.execute = function () {\n            let zone = Zone.current;\n            let isChildOfAmbientZone = false;\n            while (zone) {\n                if (zone === ambientZone) {\n                    isChildOfAmbientZone = true;\n                    break;\n                }\n                zone = zone.parent;\n            }\n            if (!isChildOfAmbientZone)\n                throw new Error('Unexpected Zone: ' + Zone.current.name);\n            // This is the zone which will be used for running individual tests.\n            // It will be a proxy zone, so that the tests function can retroactively install\n            // different zones.\n            // Example:\n            //   - In beforeEach() do childZone = Zone.current.fork(...);\n            //   - In it() try to do fakeAsync(). The issue is that because the beforeEach forked the\n            //     zone outside of fakeAsync it will be able to escape the fakeAsync rules.\n            //   - Because ProxyZone is parent fo `childZone` fakeAsync can retroactively add\n            //     fakeAsync behavior to the childZone.\n            this.testProxyZoneSpec = new ProxyZoneSpec();\n            this.testProxyZone = ambientZone.fork(this.testProxyZoneSpec);\n            if (!Zone.currentTask) {\n                // if we are not running in a task then if someone would register a\n                // element.addEventListener and then calling element.click() the\n                // addEventListener callback would think that it is the top most task and would\n                // drain the microtask queue on element.click() which would be incorrect.\n                // For this reason we always force a task when running jasmine tests.\n                Zone.current.scheduleMicroTask('jasmine.execute().forceTask', () => QueueRunner.prototype.execute.call(this));\n            }\n            else {\n                _super.prototype.execute.call(this);\n            }\n        };\n        return ZoneQueueRunner;\n    })(QueueRunner);\n});\n\nZone.__load_patch('jest', (context, Zone, api) => {\n    if (typeof jest === 'undefined' || jest['__zone_patch__']) {\n        return;\n    }\n    // From jest 29 and jest-preset-angular v13, the module transform logic\n    // changed, and now jest-preset-angular use the use the tsconfig target\n    // other than the hardcoded one, https://github.com/thymikee/jest-preset-angular/issues/2010\n    // But jest-angular-preset doesn't introduce the @babel/plugin-transform-async-to-generator\n    // which is needed by angular since `async/await` still need to be transformed\n    // to promise for ES2017+ target.\n    // So for now, we disable to output the uncaught error console log for a temp solution,\n    // until jest-preset-angular find a proper solution.\n    Zone[api.symbol('ignoreConsoleErrorUncaughtError')] = true;\n    jest['__zone_patch__'] = true;\n    const ProxyZoneSpec = Zone['ProxyZoneSpec'];\n    const SyncTestZoneSpec = Zone['SyncTestZoneSpec'];\n    if (!ProxyZoneSpec) {\n        throw new Error('Missing ProxyZoneSpec');\n    }\n    const rootZone = Zone.current;\n    const syncZone = rootZone.fork(new SyncTestZoneSpec('jest.describe'));\n    const proxyZoneSpec = new ProxyZoneSpec();\n    const proxyZone = rootZone.fork(proxyZoneSpec);\n    function wrapDescribeFactoryInZone(originalJestFn) {\n        return function (...tableArgs) {\n            const originalDescribeFn = originalJestFn.apply(this, tableArgs);\n            return function (...args) {\n                args[1] = wrapDescribeInZone(args[1]);\n                return originalDescribeFn.apply(this, args);\n            };\n        };\n    }\n    function wrapTestFactoryInZone(originalJestFn) {\n        return function (...tableArgs) {\n            return function (...args) {\n                args[1] = wrapTestInZone(args[1]);\n                return originalJestFn.apply(this, tableArgs).apply(this, args);\n            };\n        };\n    }\n    /**\n     * Gets a function wrapping the body of a jest `describe` block to execute in a\n     * synchronous-only zone.\n     */\n    function wrapDescribeInZone(describeBody) {\n        return function (...args) {\n            return syncZone.run(describeBody, this, args);\n        };\n    }\n    /**\n     * Gets a function wrapping the body of a jest `it/beforeEach/afterEach` block to\n     * execute in a ProxyZone zone.\n     * This will run in the `proxyZone`.\n     */\n    function wrapTestInZone(testBody, isTestFunc = false) {\n        if (typeof testBody !== 'function') {\n            return testBody;\n        }\n        const wrappedFunc = function () {\n            if (Zone[api.symbol('useFakeTimersCalled')] === true && testBody &&\n                !testBody.isFakeAsync) {\n                // jest.useFakeTimers is called, run into fakeAsyncTest automatically.\n                const fakeAsyncModule = Zone[Zone.__symbol__('fakeAsyncTest')];\n                if (fakeAsyncModule && typeof fakeAsyncModule.fakeAsync === 'function') {\n                    testBody = fakeAsyncModule.fakeAsync(testBody);\n                }\n            }\n            proxyZoneSpec.isTestFunc = isTestFunc;\n            return proxyZone.run(testBody, null, arguments);\n        };\n        // Update the length of wrappedFunc to be the same as the length of the testBody\n        // So jest core can handle whether the test function has `done()` or not correctly\n        Object.defineProperty(wrappedFunc, 'length', { configurable: true, writable: true, enumerable: false });\n        wrappedFunc.length = testBody.length;\n        return wrappedFunc;\n    }\n    ['describe', 'xdescribe', 'fdescribe'].forEach(methodName => {\n        let originalJestFn = context[methodName];\n        if (context[Zone.__symbol__(methodName)]) {\n            return;\n        }\n        context[Zone.__symbol__(methodName)] = originalJestFn;\n        context[methodName] = function (...args) {\n            args[1] = wrapDescribeInZone(args[1]);\n            return originalJestFn.apply(this, args);\n        };\n        context[methodName].each = wrapDescribeFactoryInZone(originalJestFn.each);\n    });\n    context.describe.only = context.fdescribe;\n    context.describe.skip = context.xdescribe;\n    ['it', 'xit', 'fit', 'test', 'xtest'].forEach(methodName => {\n        let originalJestFn = context[methodName];\n        if (context[Zone.__symbol__(methodName)]) {\n            return;\n        }\n        context[Zone.__symbol__(methodName)] = originalJestFn;\n        context[methodName] = function (...args) {\n            args[1] = wrapTestInZone(args[1], true);\n            return originalJestFn.apply(this, args);\n        };\n        context[methodName].each = wrapTestFactoryInZone(originalJestFn.each);\n        context[methodName].todo = originalJestFn.todo;\n    });\n    context.it.only = context.fit;\n    context.it.skip = context.xit;\n    context.test.only = context.fit;\n    context.test.skip = context.xit;\n    ['beforeEach', 'afterEach', 'beforeAll', 'afterAll'].forEach(methodName => {\n        let originalJestFn = context[methodName];\n        if (context[Zone.__symbol__(methodName)]) {\n            return;\n        }\n        context[Zone.__symbol__(methodName)] = originalJestFn;\n        context[methodName] = function (...args) {\n            args[0] = wrapTestInZone(args[0]);\n            return originalJestFn.apply(this, args);\n        };\n    });\n    Zone.patchJestObject = function patchJestObject(Timer, isModern = false) {\n        // check whether currently the test is inside fakeAsync()\n        function isPatchingFakeTimer() {\n            const fakeAsyncZoneSpec = Zone.current.get('FakeAsyncTestZoneSpec');\n            return !!fakeAsyncZoneSpec;\n        }\n        // check whether the current function is inside `test/it` or other methods\n        // such as `describe/beforeEach`\n        function isInTestFunc() {\n            const proxyZoneSpec = Zone.current.get('ProxyZoneSpec');\n            return proxyZoneSpec && proxyZoneSpec.isTestFunc;\n        }\n        if (Timer[api.symbol('fakeTimers')]) {\n            return;\n        }\n        Timer[api.symbol('fakeTimers')] = true;\n        // patch jest fakeTimer internal method to make sure no console.warn print out\n        api.patchMethod(Timer, '_checkFakeTimers', delegate => {\n            return function (self, args) {\n                if (isPatchingFakeTimer()) {\n                    return true;\n                }\n                else {\n                    return delegate.apply(self, args);\n                }\n            };\n        });\n        // patch useFakeTimers(), set useFakeTimersCalled flag, and make test auto run into fakeAsync\n        api.patchMethod(Timer, 'useFakeTimers', delegate => {\n            return function (self, args) {\n                Zone[api.symbol('useFakeTimersCalled')] = true;\n                if (isModern || isInTestFunc()) {\n                    return delegate.apply(self, args);\n                }\n                return self;\n            };\n        });\n        // patch useRealTimers(), unset useFakeTimers flag\n        api.patchMethod(Timer, 'useRealTimers', delegate => {\n            return function (self, args) {\n                Zone[api.symbol('useFakeTimersCalled')] = false;\n                if (isModern || isInTestFunc()) {\n                    return delegate.apply(self, args);\n                }\n                return self;\n            };\n        });\n        // patch setSystemTime(), call setCurrentRealTime() in the fakeAsyncTest\n        api.patchMethod(Timer, 'setSystemTime', delegate => {\n            return function (self, args) {\n                const fakeAsyncZoneSpec = Zone.current.get('FakeAsyncTestZoneSpec');\n                if (fakeAsyncZoneSpec && isPatchingFakeTimer()) {\n                    fakeAsyncZoneSpec.setFakeBaseSystemTime(args[0]);\n                }\n                else {\n                    return delegate.apply(self, args);\n                }\n            };\n        });\n        // patch getSystemTime(), call getCurrentRealTime() in the fakeAsyncTest\n        api.patchMethod(Timer, 'getRealSystemTime', delegate => {\n            return function (self, args) {\n                const fakeAsyncZoneSpec = Zone.current.get('FakeAsyncTestZoneSpec');\n                if (fakeAsyncZoneSpec && isPatchingFakeTimer()) {\n                    return fakeAsyncZoneSpec.getRealSystemTime();\n                }\n                else {\n                    return delegate.apply(self, args);\n                }\n            };\n        });\n        // patch runAllTicks(), run all microTasks inside fakeAsync\n        api.patchMethod(Timer, 'runAllTicks', delegate => {\n            return function (self, args) {\n                const fakeAsyncZoneSpec = Zone.current.get('FakeAsyncTestZoneSpec');\n                if (fakeAsyncZoneSpec) {\n                    fakeAsyncZoneSpec.flushMicrotasks();\n                }\n                else {\n                    return delegate.apply(self, args);\n                }\n            };\n        });\n        // patch runAllTimers(), run all macroTasks inside fakeAsync\n        api.patchMethod(Timer, 'runAllTimers', delegate => {\n            return function (self, args) {\n                const fakeAsyncZoneSpec = Zone.current.get('FakeAsyncTestZoneSpec');\n                if (fakeAsyncZoneSpec) {\n                    fakeAsyncZoneSpec.flush(100, true);\n                }\n                else {\n                    return delegate.apply(self, args);\n                }\n            };\n        });\n        // patch advanceTimersByTime(), call tick() in the fakeAsyncTest\n        api.patchMethod(Timer, 'advanceTimersByTime', delegate => {\n            return function (self, args) {\n                const fakeAsyncZoneSpec = Zone.current.get('FakeAsyncTestZoneSpec');\n                if (fakeAsyncZoneSpec) {\n                    fakeAsyncZoneSpec.tick(args[0]);\n                }\n                else {\n                    return delegate.apply(self, args);\n                }\n            };\n        });\n        // patch runOnlyPendingTimers(), call flushOnlyPendingTimers() in the fakeAsyncTest\n        api.patchMethod(Timer, 'runOnlyPendingTimers', delegate => {\n            return function (self, args) {\n                const fakeAsyncZoneSpec = Zone.current.get('FakeAsyncTestZoneSpec');\n                if (fakeAsyncZoneSpec) {\n                    fakeAsyncZoneSpec.flushOnlyPendingTimers();\n                }\n                else {\n                    return delegate.apply(self, args);\n                }\n            };\n        });\n        // patch advanceTimersToNextTimer(), call tickToNext() in the fakeAsyncTest\n        api.patchMethod(Timer, 'advanceTimersToNextTimer', delegate => {\n            return function (self, args) {\n                const fakeAsyncZoneSpec = Zone.current.get('FakeAsyncTestZoneSpec');\n                if (fakeAsyncZoneSpec) {\n                    fakeAsyncZoneSpec.tickToNext(args[0]);\n                }\n                else {\n                    return delegate.apply(self, args);\n                }\n            };\n        });\n        // patch clearAllTimers(), call removeAllTimers() in the fakeAsyncTest\n        api.patchMethod(Timer, 'clearAllTimers', delegate => {\n            return function (self, args) {\n                const fakeAsyncZoneSpec = Zone.current.get('FakeAsyncTestZoneSpec');\n                if (fakeAsyncZoneSpec) {\n                    fakeAsyncZoneSpec.removeAllTimers();\n                }\n                else {\n                    return delegate.apply(self, args);\n                }\n            };\n        });\n        // patch getTimerCount(), call getTimerCount() in the fakeAsyncTest\n        api.patchMethod(Timer, 'getTimerCount', delegate => {\n            return function (self, args) {\n                const fakeAsyncZoneSpec = Zone.current.get('FakeAsyncTestZoneSpec');\n                if (fakeAsyncZoneSpec) {\n                    return fakeAsyncZoneSpec.getTimerCount();\n                }\n                else {\n                    return delegate.apply(self, args);\n                }\n            };\n        });\n    };\n});\n\nZone.__load_patch('mocha', (global, Zone) => {\n    const Mocha = global.Mocha;\n    if (typeof Mocha === 'undefined') {\n        // return if Mocha is not available, because now zone-testing\n        // will load mocha patch with jasmine/jest patch\n        return;\n    }\n    if (typeof Zone === 'undefined') {\n        throw new Error('Missing Zone.js');\n    }\n    const ProxyZoneSpec = Zone['ProxyZoneSpec'];\n    const SyncTestZoneSpec = Zone['SyncTestZoneSpec'];\n    if (!ProxyZoneSpec) {\n        throw new Error('Missing ProxyZoneSpec');\n    }\n    if (Mocha['__zone_patch__']) {\n        throw new Error('\"Mocha\" has already been patched with \"Zone\".');\n    }\n    Mocha['__zone_patch__'] = true;\n    const rootZone = Zone.current;\n    const syncZone = rootZone.fork(new SyncTestZoneSpec('Mocha.describe'));\n    let testZone = null;\n    const suiteZone = rootZone.fork(new ProxyZoneSpec());\n    const mochaOriginal = {\n        after: global.after,\n        afterEach: global.afterEach,\n        before: global.before,\n        beforeEach: global.beforeEach,\n        describe: global.describe,\n        it: global.it\n    };\n    function modifyArguments(args, syncTest, asyncTest) {\n        for (let i = 0; i < args.length; i++) {\n            let arg = args[i];\n            if (typeof arg === 'function') {\n                // The `done` callback is only passed through if the function expects at\n                // least one argument.\n                // Note we have to make a function with correct number of arguments,\n                // otherwise mocha will\n                // think that all functions are sync or async.\n                args[i] = (arg.length === 0) ? syncTest(arg) : asyncTest(arg);\n                // Mocha uses toString to view the test body in the result list, make sure we return the\n                // correct function body\n                args[i].toString = function () {\n                    return arg.toString();\n                };\n            }\n        }\n        return args;\n    }\n    function wrapDescribeInZone(args) {\n        const syncTest = function (fn) {\n            return function () {\n                return syncZone.run(fn, this, arguments);\n            };\n        };\n        return modifyArguments(args, syncTest);\n    }\n    function wrapTestInZone(args) {\n        const asyncTest = function (fn) {\n            return function (done) {\n                return testZone.run(fn, this, [done]);\n            };\n        };\n        const syncTest = function (fn) {\n            return function () {\n                return testZone.run(fn, this);\n            };\n        };\n        return modifyArguments(args, syncTest, asyncTest);\n    }\n    function wrapSuiteInZone(args) {\n        const asyncTest = function (fn) {\n            return function (done) {\n                return suiteZone.run(fn, this, [done]);\n            };\n        };\n        const syncTest = function (fn) {\n            return function () {\n                return suiteZone.run(fn, this);\n            };\n        };\n        return modifyArguments(args, syncTest, asyncTest);\n    }\n    global.describe = global.suite = function () {\n        return mochaOriginal.describe.apply(this, wrapDescribeInZone(arguments));\n    };\n    global.xdescribe = global.suite.skip = global.describe.skip = function () {\n        return mochaOriginal.describe.skip.apply(this, wrapDescribeInZone(arguments));\n    };\n    global.describe.only = global.suite.only = function () {\n        return mochaOriginal.describe.only.apply(this, wrapDescribeInZone(arguments));\n    };\n    global.it = global.specify = global.test = function () {\n        return mochaOriginal.it.apply(this, wrapTestInZone(arguments));\n    };\n    global.xit = global.xspecify = global.it.skip = function () {\n        return mochaOriginal.it.skip.apply(this, wrapTestInZone(arguments));\n    };\n    global.it.only = global.test.only = function () {\n        return mochaOriginal.it.only.apply(this, wrapTestInZone(arguments));\n    };\n    global.after = global.suiteTeardown = function () {\n        return mochaOriginal.after.apply(this, wrapSuiteInZone(arguments));\n    };\n    global.afterEach = global.teardown = function () {\n        return mochaOriginal.afterEach.apply(this, wrapTestInZone(arguments));\n    };\n    global.before = global.suiteSetup = function () {\n        return mochaOriginal.before.apply(this, wrapSuiteInZone(arguments));\n    };\n    global.beforeEach = global.setup = function () {\n        return mochaOriginal.beforeEach.apply(this, wrapTestInZone(arguments));\n    };\n    ((originalRunTest, originalRun) => {\n        Mocha.Runner.prototype.runTest = function (fn) {\n            Zone.current.scheduleMicroTask('mocha.forceTask', () => {\n                originalRunTest.call(this, fn);\n            });\n        };\n        Mocha.Runner.prototype.run = function (fn) {\n            this.on('test', (e) => {\n                testZone = rootZone.fork(new ProxyZoneSpec());\n            });\n            this.on('fail', (test, err) => {\n                const proxyZoneSpec = testZone && testZone.get('ProxyZoneSpec');\n                if (proxyZoneSpec && err) {\n                    try {\n                        // try catch here in case err.message is not writable\n                        err.message += proxyZoneSpec.getAndClearPendingTasksInfo();\n                    }\n                    catch (error) {\n                    }\n                }\n            });\n            return originalRun.call(this, fn);\n        };\n    })(Mocha.Runner.prototype.runTest, Mocha.Runner.prototype.run);\n});\n\n(function (_global) {\n    class AsyncTestZoneSpec {\n        static { this.symbolParentUnresolved = Zone.__symbol__('parentUnresolved'); }\n        constructor(finishCallback, failCallback, namePrefix) {\n            this.finishCallback = finishCallback;\n            this.failCallback = failCallback;\n            this._pendingMicroTasks = false;\n            this._pendingMacroTasks = false;\n            this._alreadyErrored = false;\n            this._isSync = false;\n            this._existingFinishTimer = null;\n            this.entryFunction = null;\n            this.runZone = Zone.current;\n            this.unresolvedChainedPromiseCount = 0;\n            this.supportWaitUnresolvedChainedPromise = false;\n            this.name = 'asyncTestZone for ' + namePrefix;\n            this.properties = { 'AsyncTestZoneSpec': this };\n            this.supportWaitUnresolvedChainedPromise =\n                _global[Zone.__symbol__('supportWaitUnResolvedChainedPromise')] === true;\n        }\n        isUnresolvedChainedPromisePending() {\n            return this.unresolvedChainedPromiseCount > 0;\n        }\n        _finishCallbackIfDone() {\n            // NOTE: Technically the `onHasTask` could fire together with the initial synchronous\n            // completion in `onInvoke`. `onHasTask` might call this method when it captured e.g.\n            // microtasks in the proxy zone that now complete as part of this async zone run.\n            // Consider the following scenario:\n            //    1. A test `beforeEach` schedules a microtask in the ProxyZone.\n            //    2. An actual empty `it` spec executes in the AsyncTestZone` (using e.g. `waitForAsync`).\n            //    3. The `onInvoke` invokes `_finishCallbackIfDone` because the spec runs synchronously.\n            //    4. We wait the scheduled timeout (see below) to account for unhandled promises.\n            //    5. The microtask from (1) finishes and `onHasTask` is invoked.\n            //    --> We register a second `_finishCallbackIfDone` even though we have scheduled a timeout.\n            // If the finish timeout from below is already scheduled, terminate the existing scheduled\n            // finish invocation, avoiding calling `jasmine` `done` multiple times. *Note* that we would\n            // want to schedule a new finish callback in case the task state changes again.\n            if (this._existingFinishTimer !== null) {\n                clearTimeout(this._existingFinishTimer);\n                this._existingFinishTimer = null;\n            }\n            if (!(this._pendingMicroTasks || this._pendingMacroTasks ||\n                (this.supportWaitUnresolvedChainedPromise && this.isUnresolvedChainedPromisePending()))) {\n                // We wait until the next tick because we would like to catch unhandled promises which could\n                // cause test logic to be executed. In such cases we cannot finish with tasks pending then.\n                this.runZone.run(() => {\n                    this._existingFinishTimer = setTimeout(() => {\n                        if (!this._alreadyErrored && !(this._pendingMicroTasks || this._pendingMacroTasks)) {\n                            this.finishCallback();\n                        }\n                    }, 0);\n                });\n            }\n        }\n        patchPromiseForTest() {\n            if (!this.supportWaitUnresolvedChainedPromise) {\n                return;\n            }\n            const patchPromiseForTest = Promise[Zone.__symbol__('patchPromiseForTest')];\n            if (patchPromiseForTest) {\n                patchPromiseForTest();\n            }\n        }\n        unPatchPromiseForTest() {\n            if (!this.supportWaitUnresolvedChainedPromise) {\n                return;\n            }\n            const unPatchPromiseForTest = Promise[Zone.__symbol__('unPatchPromiseForTest')];\n            if (unPatchPromiseForTest) {\n                unPatchPromiseForTest();\n            }\n        }\n        onScheduleTask(delegate, current, target, task) {\n            if (task.type !== 'eventTask') {\n                this._isSync = false;\n            }\n            if (task.type === 'microTask' && task.data && task.data instanceof Promise) {\n                // check whether the promise is a chained promise\n                if (task.data[AsyncTestZoneSpec.symbolParentUnresolved] === true) {\n                    // chained promise is being scheduled\n                    this.unresolvedChainedPromiseCount--;\n                }\n            }\n            return delegate.scheduleTask(target, task);\n        }\n        onInvokeTask(delegate, current, target, task, applyThis, applyArgs) {\n            if (task.type !== 'eventTask') {\n                this._isSync = false;\n            }\n            return delegate.invokeTask(target, task, applyThis, applyArgs);\n        }\n        onCancelTask(delegate, current, target, task) {\n            if (task.type !== 'eventTask') {\n                this._isSync = false;\n            }\n            return delegate.cancelTask(target, task);\n        }\n        // Note - we need to use onInvoke at the moment to call finish when a test is\n        // fully synchronous. TODO(juliemr): remove this when the logic for\n        // onHasTask changes and it calls whenever the task queues are dirty.\n        // updated by(JiaLiPassion), only call finish callback when no task\n        // was scheduled/invoked/canceled.\n        onInvoke(parentZoneDelegate, currentZone, targetZone, delegate, applyThis, applyArgs, source) {\n            if (!this.entryFunction) {\n                this.entryFunction = delegate;\n            }\n            try {\n                this._isSync = true;\n                return parentZoneDelegate.invoke(targetZone, delegate, applyThis, applyArgs, source);\n            }\n            finally {\n                // We need to check the delegate is the same as entryFunction or not.\n                // Consider the following case.\n                //\n                // asyncTestZone.run(() => { // Here the delegate will be the entryFunction\n                //   Zone.current.run(() => { // Here the delegate will not be the entryFunction\n                //   });\n                // });\n                //\n                // We only want to check whether there are async tasks scheduled\n                // for the entry function.\n                if (this._isSync && this.entryFunction === delegate) {\n                    this._finishCallbackIfDone();\n                }\n            }\n        }\n        onHandleError(parentZoneDelegate, currentZone, targetZone, error) {\n            // Let the parent try to handle the error.\n            const result = parentZoneDelegate.handleError(targetZone, error);\n            if (result) {\n                this.failCallback(error);\n                this._alreadyErrored = true;\n            }\n            return false;\n        }\n        onHasTask(delegate, current, target, hasTaskState) {\n            delegate.hasTask(target, hasTaskState);\n            // We should only trigger finishCallback when the target zone is the AsyncTestZone\n            // Consider the following cases.\n            //\n            // const childZone = asyncTestZone.fork({\n            //   name: 'child',\n            //   onHasTask: ...\n            // });\n            //\n            // So we have nested zones declared the onHasTask hook, in this case,\n            // the onHasTask will be triggered twice, and cause the finishCallbackIfDone()\n            // is also be invoked twice. So we need to only trigger the finishCallbackIfDone()\n            // when the current zone is the same as the target zone.\n            if (current !== target) {\n                return;\n            }\n            if (hasTaskState.change == 'microTask') {\n                this._pendingMicroTasks = hasTaskState.microTask;\n                this._finishCallbackIfDone();\n            }\n            else if (hasTaskState.change == 'macroTask') {\n                this._pendingMacroTasks = hasTaskState.macroTask;\n                this._finishCallbackIfDone();\n            }\n        }\n    }\n    // Export the class so that new instances can be created with proper\n    // constructor params.\n    Zone['AsyncTestZoneSpec'] = AsyncTestZoneSpec;\n})(typeof window !== 'undefined' && window || typeof self !== 'undefined' && self || global);\nZone.__load_patch('asynctest', (global, Zone, api) => {\n    /**\n     * Wraps a test function in an asynchronous test zone. The test will automatically\n     * complete when all asynchronous calls within this zone are done.\n     */\n    Zone[api.symbol('asyncTest')] = function asyncTest(fn) {\n        // If we're running using the Jasmine test framework, adapt to call the 'done'\n        // function when asynchronous activity is finished.\n        if (global.jasmine) {\n            // Not using an arrow function to preserve context passed from call site\n            return function (done) {\n                if (!done) {\n                    // if we run beforeEach in @angular/core/testing/testing_internal then we get no done\n                    // fake it here and assume sync.\n                    done = function () { };\n                    done.fail = function (e) {\n                        throw e;\n                    };\n                }\n                runInTestZone(fn, this, done, (err) => {\n                    if (typeof err === 'string') {\n                        return done.fail(new Error(err));\n                    }\n                    else {\n                        done.fail(err);\n                    }\n                });\n            };\n        }\n        // Otherwise, return a promise which will resolve when asynchronous activity\n        // is finished. This will be correctly consumed by the Mocha framework with\n        // it('...', async(myFn)); or can be used in a custom framework.\n        // Not using an arrow function to preserve context passed from call site\n        return function () {\n            return new Promise((finishCallback, failCallback) => {\n                runInTestZone(fn, this, finishCallback, failCallback);\n            });\n        };\n    };\n    function runInTestZone(fn, context, finishCallback, failCallback) {\n        const currentZone = Zone.current;\n        const AsyncTestZoneSpec = Zone['AsyncTestZoneSpec'];\n        if (AsyncTestZoneSpec === undefined) {\n            throw new Error('AsyncTestZoneSpec is needed for the async() test helper but could not be found. ' +\n                'Please make sure that your environment includes zone.js/plugins/async-test');\n        }\n        const ProxyZoneSpec = Zone['ProxyZoneSpec'];\n        if (!ProxyZoneSpec) {\n            throw new Error('ProxyZoneSpec is needed for the async() test helper but could not be found. ' +\n                'Please make sure that your environment includes zone.js/plugins/proxy');\n        }\n        const proxyZoneSpec = ProxyZoneSpec.get();\n        ProxyZoneSpec.assertPresent();\n        // We need to create the AsyncTestZoneSpec outside the ProxyZone.\n        // If we do it in ProxyZone then we will get to infinite recursion.\n        const proxyZone = Zone.current.getZoneWith('ProxyZoneSpec');\n        const previousDelegate = proxyZoneSpec.getDelegate();\n        proxyZone.parent.run(() => {\n            const testZoneSpec = new AsyncTestZoneSpec(() => {\n                // Need to restore the original zone.\n                if (proxyZoneSpec.getDelegate() == testZoneSpec) {\n                    // Only reset the zone spec if it's\n                    // still this one. Otherwise, assume\n                    // it's OK.\n                    proxyZoneSpec.setDelegate(previousDelegate);\n                }\n                testZoneSpec.unPatchPromiseForTest();\n                currentZone.run(() => {\n                    finishCallback();\n                });\n            }, (error) => {\n                // Need to restore the original zone.\n                if (proxyZoneSpec.getDelegate() == testZoneSpec) {\n                    // Only reset the zone spec if it's sill this one. Otherwise, assume it's OK.\n                    proxyZoneSpec.setDelegate(previousDelegate);\n                }\n                testZoneSpec.unPatchPromiseForTest();\n                currentZone.run(() => {\n                    failCallback(error);\n                });\n            }, 'test');\n            proxyZoneSpec.setDelegate(testZoneSpec);\n            testZoneSpec.patchPromiseForTest();\n        });\n        return Zone.current.runGuarded(fn, context);\n    }\n});\n\n(function (global) {\n    const OriginalDate = global.Date;\n    // Since when we compile this file to `es2015`, and if we define\n    // this `FakeDate` as `class FakeDate`, and then set `FakeDate.prototype`\n    // there will be an error which is `Cannot assign to read only property 'prototype'`\n    // so we need to use function implementation here.\n    function FakeDate() {\n        if (arguments.length === 0) {\n            const d = new OriginalDate();\n            d.setTime(FakeDate.now());\n            return d;\n        }\n        else {\n            const args = Array.prototype.slice.call(arguments);\n            return new OriginalDate(...args);\n        }\n    }\n    FakeDate.now = function () {\n        const fakeAsyncTestZoneSpec = Zone.current.get('FakeAsyncTestZoneSpec');\n        if (fakeAsyncTestZoneSpec) {\n            return fakeAsyncTestZoneSpec.getFakeSystemTime();\n        }\n        return OriginalDate.now.apply(this, arguments);\n    };\n    FakeDate.UTC = OriginalDate.UTC;\n    FakeDate.parse = OriginalDate.parse;\n    // keep a reference for zone patched timer function\n    const timers = {\n        setTimeout: global.setTimeout,\n        setInterval: global.setInterval,\n        clearTimeout: global.clearTimeout,\n        clearInterval: global.clearInterval\n    };\n    class Scheduler {\n        // Next scheduler id.\n        static { this.nextId = 1; }\n        constructor() {\n            // Scheduler queue with the tuple of end time and callback function - sorted by end time.\n            this._schedulerQueue = [];\n            // Current simulated time in millis.\n            this._currentTickTime = 0;\n            // Current fake system base time in millis.\n            this._currentFakeBaseSystemTime = OriginalDate.now();\n            // track requeuePeriodicTimer\n            this._currentTickRequeuePeriodicEntries = [];\n        }\n        getCurrentTickTime() {\n            return this._currentTickTime;\n        }\n        getFakeSystemTime() {\n            return this._currentFakeBaseSystemTime + this._currentTickTime;\n        }\n        setFakeBaseSystemTime(fakeBaseSystemTime) {\n            this._currentFakeBaseSystemTime = fakeBaseSystemTime;\n        }\n        getRealSystemTime() {\n            return OriginalDate.now();\n        }\n        scheduleFunction(cb, delay, options) {\n            options = {\n                ...{\n                    args: [],\n                    isPeriodic: false,\n                    isRequestAnimationFrame: false,\n                    id: -1,\n                    isRequeuePeriodic: false\n                },\n                ...options\n            };\n            let currentId = options.id < 0 ? Scheduler.nextId++ : options.id;\n            let endTime = this._currentTickTime + delay;\n            // Insert so that scheduler queue remains sorted by end time.\n            let newEntry = {\n                endTime: endTime,\n                id: currentId,\n                func: cb,\n                args: options.args,\n                delay: delay,\n                isPeriodic: options.isPeriodic,\n                isRequestAnimationFrame: options.isRequestAnimationFrame\n            };\n            if (options.isRequeuePeriodic) {\n                this._currentTickRequeuePeriodicEntries.push(newEntry);\n            }\n            let i = 0;\n            for (; i < this._schedulerQueue.length; i++) {\n                let currentEntry = this._schedulerQueue[i];\n                if (newEntry.endTime < currentEntry.endTime) {\n                    break;\n                }\n            }\n            this._schedulerQueue.splice(i, 0, newEntry);\n            return currentId;\n        }\n        removeScheduledFunctionWithId(id) {\n            for (let i = 0; i < this._schedulerQueue.length; i++) {\n                if (this._schedulerQueue[i].id == id) {\n                    this._schedulerQueue.splice(i, 1);\n                    break;\n                }\n            }\n        }\n        removeAll() {\n            this._schedulerQueue = [];\n        }\n        getTimerCount() {\n            return this._schedulerQueue.length;\n        }\n        tickToNext(step = 1, doTick, tickOptions) {\n            if (this._schedulerQueue.length < step) {\n                return;\n            }\n            // Find the last task currently queued in the scheduler queue and tick\n            // till that time.\n            const startTime = this._currentTickTime;\n            const targetTask = this._schedulerQueue[step - 1];\n            this.tick(targetTask.endTime - startTime, doTick, tickOptions);\n        }\n        tick(millis = 0, doTick, tickOptions) {\n            let finalTime = this._currentTickTime + millis;\n            let lastCurrentTime = 0;\n            tickOptions = Object.assign({ processNewMacroTasksSynchronously: true }, tickOptions);\n            // we need to copy the schedulerQueue so nested timeout\n            // will not be wrongly called in the current tick\n            // https://github.com/angular/angular/issues/33799\n            const schedulerQueue = tickOptions.processNewMacroTasksSynchronously ?\n                this._schedulerQueue :\n                this._schedulerQueue.slice();\n            if (schedulerQueue.length === 0 && doTick) {\n                doTick(millis);\n                return;\n            }\n            while (schedulerQueue.length > 0) {\n                // clear requeueEntries before each loop\n                this._currentTickRequeuePeriodicEntries = [];\n                let current = schedulerQueue[0];\n                if (finalTime < current.endTime) {\n                    // Done processing the queue since it's sorted by endTime.\n                    break;\n                }\n                else {\n                    // Time to run scheduled function. Remove it from the head of queue.\n                    let current = schedulerQueue.shift();\n                    if (!tickOptions.processNewMacroTasksSynchronously) {\n                        const idx = this._schedulerQueue.indexOf(current);\n                        if (idx >= 0) {\n                            this._schedulerQueue.splice(idx, 1);\n                        }\n                    }\n                    lastCurrentTime = this._currentTickTime;\n                    this._currentTickTime = current.endTime;\n                    if (doTick) {\n                        doTick(this._currentTickTime - lastCurrentTime);\n                    }\n                    let retval = current.func.apply(global, current.isRequestAnimationFrame ? [this._currentTickTime] : current.args);\n                    if (!retval) {\n                        // Uncaught exception in the current scheduled function. Stop processing the queue.\n                        break;\n                    }\n                    // check is there any requeue periodic entry is added in\n                    // current loop, if there is, we need to add to current loop\n                    if (!tickOptions.processNewMacroTasksSynchronously) {\n                        this._currentTickRequeuePeriodicEntries.forEach(newEntry => {\n                            let i = 0;\n                            for (; i < schedulerQueue.length; i++) {\n                                const currentEntry = schedulerQueue[i];\n                                if (newEntry.endTime < currentEntry.endTime) {\n                                    break;\n                                }\n                            }\n                            schedulerQueue.splice(i, 0, newEntry);\n                        });\n                    }\n                }\n            }\n            lastCurrentTime = this._currentTickTime;\n            this._currentTickTime = finalTime;\n            if (doTick) {\n                doTick(this._currentTickTime - lastCurrentTime);\n            }\n        }\n        flushOnlyPendingTimers(doTick) {\n            if (this._schedulerQueue.length === 0) {\n                return 0;\n            }\n            // Find the last task currently queued in the scheduler queue and tick\n            // till that time.\n            const startTime = this._currentTickTime;\n            const lastTask = this._schedulerQueue[this._schedulerQueue.length - 1];\n            this.tick(lastTask.endTime - startTime, doTick, { processNewMacroTasksSynchronously: false });\n            return this._currentTickTime - startTime;\n        }\n        flush(limit = 20, flushPeriodic = false, doTick) {\n            if (flushPeriodic) {\n                return this.flushPeriodic(doTick);\n            }\n            else {\n                return this.flushNonPeriodic(limit, doTick);\n            }\n        }\n        flushPeriodic(doTick) {\n            if (this._schedulerQueue.length === 0) {\n                return 0;\n            }\n            // Find the last task currently queued in the scheduler queue and tick\n            // till that time.\n            const startTime = this._currentTickTime;\n            const lastTask = this._schedulerQueue[this._schedulerQueue.length - 1];\n            this.tick(lastTask.endTime - startTime, doTick);\n            return this._currentTickTime - startTime;\n        }\n        flushNonPeriodic(limit, doTick) {\n            const startTime = this._currentTickTime;\n            let lastCurrentTime = 0;\n            let count = 0;\n            while (this._schedulerQueue.length > 0) {\n                count++;\n                if (count > limit) {\n                    throw new Error('flush failed after reaching the limit of ' + limit +\n                        ' tasks. Does your code use a polling timeout?');\n                }\n                // flush only non-periodic timers.\n                // If the only remaining tasks are periodic(or requestAnimationFrame), finish flushing.\n                if (this._schedulerQueue.filter(task => !task.isPeriodic && !task.isRequestAnimationFrame)\n                    .length === 0) {\n                    break;\n                }\n                const current = this._schedulerQueue.shift();\n                lastCurrentTime = this._currentTickTime;\n                this._currentTickTime = current.endTime;\n                if (doTick) {\n                    // Update any secondary schedulers like Jasmine mock Date.\n                    doTick(this._currentTickTime - lastCurrentTime);\n                }\n                const retval = current.func.apply(global, current.args);\n                if (!retval) {\n                    // Uncaught exception in the current scheduled function. Stop processing the queue.\n                    break;\n                }\n            }\n            return this._currentTickTime - startTime;\n        }\n    }\n    class FakeAsyncTestZoneSpec {\n        static assertInZone() {\n            if (Zone.current.get('FakeAsyncTestZoneSpec') == null) {\n                throw new Error('The code should be running in the fakeAsync zone to call this function');\n            }\n        }\n        constructor(namePrefix, trackPendingRequestAnimationFrame = false, macroTaskOptions) {\n            this.trackPendingRequestAnimationFrame = trackPendingRequestAnimationFrame;\n            this.macroTaskOptions = macroTaskOptions;\n            this._scheduler = new Scheduler();\n            this._microtasks = [];\n            this._lastError = null;\n            this._uncaughtPromiseErrors = Promise[Zone.__symbol__('uncaughtPromiseErrors')];\n            this.pendingPeriodicTimers = [];\n            this.pendingTimers = [];\n            this.patchDateLocked = false;\n            this.properties = { 'FakeAsyncTestZoneSpec': this };\n            this.name = 'fakeAsyncTestZone for ' + namePrefix;\n            // in case user can't access the construction of FakeAsyncTestSpec\n            // user can also define macroTaskOptions by define a global variable.\n            if (!this.macroTaskOptions) {\n                this.macroTaskOptions = global[Zone.__symbol__('FakeAsyncTestMacroTask')];\n            }\n        }\n        _fnAndFlush(fn, completers) {\n            return (...args) => {\n                fn.apply(global, args);\n                if (this._lastError === null) { // Success\n                    if (completers.onSuccess != null) {\n                        completers.onSuccess.apply(global);\n                    }\n                    // Flush microtasks only on success.\n                    this.flushMicrotasks();\n                }\n                else { // Failure\n                    if (completers.onError != null) {\n                        completers.onError.apply(global);\n                    }\n                }\n                // Return true if there were no errors, false otherwise.\n                return this._lastError === null;\n            };\n        }\n        static _removeTimer(timers, id) {\n            let index = timers.indexOf(id);\n            if (index > -1) {\n                timers.splice(index, 1);\n            }\n        }\n        _dequeueTimer(id) {\n            return () => {\n                FakeAsyncTestZoneSpec._removeTimer(this.pendingTimers, id);\n            };\n        }\n        _requeuePeriodicTimer(fn, interval, args, id) {\n            return () => {\n                // Requeue the timer callback if it's not been canceled.\n                if (this.pendingPeriodicTimers.indexOf(id) !== -1) {\n                    this._scheduler.scheduleFunction(fn, interval, { args, isPeriodic: true, id, isRequeuePeriodic: true });\n                }\n            };\n        }\n        _dequeuePeriodicTimer(id) {\n            return () => {\n                FakeAsyncTestZoneSpec._removeTimer(this.pendingPeriodicTimers, id);\n            };\n        }\n        _setTimeout(fn, delay, args, isTimer = true) {\n            let removeTimerFn = this._dequeueTimer(Scheduler.nextId);\n            // Queue the callback and dequeue the timer on success and error.\n            let cb = this._fnAndFlush(fn, { onSuccess: removeTimerFn, onError: removeTimerFn });\n            let id = this._scheduler.scheduleFunction(cb, delay, { args, isRequestAnimationFrame: !isTimer });\n            if (isTimer) {\n                this.pendingTimers.push(id);\n            }\n            return id;\n        }\n        _clearTimeout(id) {\n            FakeAsyncTestZoneSpec._removeTimer(this.pendingTimers, id);\n            this._scheduler.removeScheduledFunctionWithId(id);\n        }\n        _setInterval(fn, interval, args) {\n            let id = Scheduler.nextId;\n            let completers = { onSuccess: null, onError: this._dequeuePeriodicTimer(id) };\n            let cb = this._fnAndFlush(fn, completers);\n            // Use the callback created above to requeue on success.\n            completers.onSuccess = this._requeuePeriodicTimer(cb, interval, args, id);\n            // Queue the callback and dequeue the periodic timer only on error.\n            this._scheduler.scheduleFunction(cb, interval, { args, isPeriodic: true });\n            this.pendingPeriodicTimers.push(id);\n            return id;\n        }\n        _clearInterval(id) {\n            FakeAsyncTestZoneSpec._removeTimer(this.pendingPeriodicTimers, id);\n            this._scheduler.removeScheduledFunctionWithId(id);\n        }\n        _resetLastErrorAndThrow() {\n            let error = this._lastError || this._uncaughtPromiseErrors[0];\n            this._uncaughtPromiseErrors.length = 0;\n            this._lastError = null;\n            throw error;\n        }\n        getCurrentTickTime() {\n            return this._scheduler.getCurrentTickTime();\n        }\n        getFakeSystemTime() {\n            return this._scheduler.getFakeSystemTime();\n        }\n        setFakeBaseSystemTime(realTime) {\n            this._scheduler.setFakeBaseSystemTime(realTime);\n        }\n        getRealSystemTime() {\n            return this._scheduler.getRealSystemTime();\n        }\n        static patchDate() {\n            if (!!global[Zone.__symbol__('disableDatePatching')]) {\n                // we don't want to patch global Date\n                // because in some case, global Date\n                // is already being patched, we need to provide\n                // an option to let user still use their\n                // own version of Date.\n                return;\n            }\n            if (global['Date'] === FakeDate) {\n                // already patched\n                return;\n            }\n            global['Date'] = FakeDate;\n            FakeDate.prototype = OriginalDate.prototype;\n            // try check and reset timers\n            // because jasmine.clock().install() may\n            // have replaced the global timer\n            FakeAsyncTestZoneSpec.checkTimerPatch();\n        }\n        static resetDate() {\n            if (global['Date'] === FakeDate) {\n                global['Date'] = OriginalDate;\n            }\n        }\n        static checkTimerPatch() {\n            if (global.setTimeout !== timers.setTimeout) {\n                global.setTimeout = timers.setTimeout;\n                global.clearTimeout = timers.clearTimeout;\n            }\n            if (global.setInterval !== timers.setInterval) {\n                global.setInterval = timers.setInterval;\n                global.clearInterval = timers.clearInterval;\n            }\n        }\n        lockDatePatch() {\n            this.patchDateLocked = true;\n            FakeAsyncTestZoneSpec.patchDate();\n        }\n        unlockDatePatch() {\n            this.patchDateLocked = false;\n            FakeAsyncTestZoneSpec.resetDate();\n        }\n        tickToNext(steps = 1, doTick, tickOptions = { processNewMacroTasksSynchronously: true }) {\n            if (steps <= 0) {\n                return;\n            }\n            FakeAsyncTestZoneSpec.assertInZone();\n            this.flushMicrotasks();\n            this._scheduler.tickToNext(steps, doTick, tickOptions);\n            if (this._lastError !== null) {\n                this._resetLastErrorAndThrow();\n            }\n        }\n        tick(millis = 0, doTick, tickOptions = { processNewMacroTasksSynchronously: true }) {\n            FakeAsyncTestZoneSpec.assertInZone();\n            this.flushMicrotasks();\n            this._scheduler.tick(millis, doTick, tickOptions);\n            if (this._lastError !== null) {\n                this._resetLastErrorAndThrow();\n            }\n        }\n        flushMicrotasks() {\n            FakeAsyncTestZoneSpec.assertInZone();\n            const flushErrors = () => {\n                if (this._lastError !== null || this._uncaughtPromiseErrors.length) {\n                    // If there is an error stop processing the microtask queue and rethrow the error.\n                    this._resetLastErrorAndThrow();\n                }\n            };\n            while (this._microtasks.length > 0) {\n                let microtask = this._microtasks.shift();\n                microtask.func.apply(microtask.target, microtask.args);\n            }\n            flushErrors();\n        }\n        flush(limit, flushPeriodic, doTick) {\n            FakeAsyncTestZoneSpec.assertInZone();\n            this.flushMicrotasks();\n            const elapsed = this._scheduler.flush(limit, flushPeriodic, doTick);\n            if (this._lastError !== null) {\n                this._resetLastErrorAndThrow();\n            }\n            return elapsed;\n        }\n        flushOnlyPendingTimers(doTick) {\n            FakeAsyncTestZoneSpec.assertInZone();\n            this.flushMicrotasks();\n            const elapsed = this._scheduler.flushOnlyPendingTimers(doTick);\n            if (this._lastError !== null) {\n                this._resetLastErrorAndThrow();\n            }\n            return elapsed;\n        }\n        removeAllTimers() {\n            FakeAsyncTestZoneSpec.assertInZone();\n            this._scheduler.removeAll();\n            this.pendingPeriodicTimers = [];\n            this.pendingTimers = [];\n        }\n        getTimerCount() {\n            return this._scheduler.getTimerCount() + this._microtasks.length;\n        }\n        onScheduleTask(delegate, current, target, task) {\n            switch (task.type) {\n                case 'microTask':\n                    let args = task.data && task.data.args;\n                    // should pass additional arguments to callback if have any\n                    // currently we know process.nextTick will have such additional\n                    // arguments\n                    let additionalArgs;\n                    if (args) {\n                        let callbackIndex = task.data.cbIdx;\n                        if (typeof args.length === 'number' && args.length > callbackIndex + 1) {\n                            additionalArgs = Array.prototype.slice.call(args, callbackIndex + 1);\n                        }\n                    }\n                    this._microtasks.push({\n                        func: task.invoke,\n                        args: additionalArgs,\n                        target: task.data && task.data.target\n                    });\n                    break;\n                case 'macroTask':\n                    switch (task.source) {\n                        case 'setTimeout':\n                            task.data['handleId'] = this._setTimeout(task.invoke, task.data['delay'], Array.prototype.slice.call(task.data['args'], 2));\n                            break;\n                        case 'setImmediate':\n                            task.data['handleId'] = this._setTimeout(task.invoke, 0, Array.prototype.slice.call(task.data['args'], 1));\n                            break;\n                        case 'setInterval':\n                            task.data['handleId'] = this._setInterval(task.invoke, task.data['delay'], Array.prototype.slice.call(task.data['args'], 2));\n                            break;\n                        case 'XMLHttpRequest.send':\n                            throw new Error('Cannot make XHRs from within a fake async test. Request URL: ' +\n                                task.data['url']);\n                        case 'requestAnimationFrame':\n                        case 'webkitRequestAnimationFrame':\n                        case 'mozRequestAnimationFrame':\n                            // Simulate a requestAnimationFrame by using a setTimeout with 16 ms.\n                            // (60 frames per second)\n                            task.data['handleId'] = this._setTimeout(task.invoke, 16, task.data['args'], this.trackPendingRequestAnimationFrame);\n                            break;\n                        default:\n                            // user can define which macroTask they want to support by passing\n                            // macroTaskOptions\n                            const macroTaskOption = this.findMacroTaskOption(task);\n                            if (macroTaskOption) {\n                                const args = task.data && task.data['args'];\n                                const delay = args && args.length > 1 ? args[1] : 0;\n                                let callbackArgs = macroTaskOption.callbackArgs ? macroTaskOption.callbackArgs : args;\n                                if (!!macroTaskOption.isPeriodic) {\n                                    // periodic macroTask, use setInterval to simulate\n                                    task.data['handleId'] = this._setInterval(task.invoke, delay, callbackArgs);\n                                    task.data.isPeriodic = true;\n                                }\n                                else {\n                                    // not periodic, use setTimeout to simulate\n                                    task.data['handleId'] = this._setTimeout(task.invoke, delay, callbackArgs);\n                                }\n                                break;\n                            }\n                            throw new Error('Unknown macroTask scheduled in fake async test: ' + task.source);\n                    }\n                    break;\n                case 'eventTask':\n                    task = delegate.scheduleTask(target, task);\n                    break;\n            }\n            return task;\n        }\n        onCancelTask(delegate, current, target, task) {\n            switch (task.source) {\n                case 'setTimeout':\n                case 'requestAnimationFrame':\n                case 'webkitRequestAnimationFrame':\n                case 'mozRequestAnimationFrame':\n                    return this._clearTimeout(task.data['handleId']);\n                case 'setInterval':\n                    return this._clearInterval(task.data['handleId']);\n                default:\n                    // user can define which macroTask they want to support by passing\n                    // macroTaskOptions\n                    const macroTaskOption = this.findMacroTaskOption(task);\n                    if (macroTaskOption) {\n                        const handleId = task.data['handleId'];\n                        return macroTaskOption.isPeriodic ? this._clearInterval(handleId) :\n                            this._clearTimeout(handleId);\n                    }\n                    return delegate.cancelTask(target, task);\n            }\n        }\n        onInvoke(delegate, current, target, callback, applyThis, applyArgs, source) {\n            try {\n                FakeAsyncTestZoneSpec.patchDate();\n                return delegate.invoke(target, callback, applyThis, applyArgs, source);\n            }\n            finally {\n                if (!this.patchDateLocked) {\n                    FakeAsyncTestZoneSpec.resetDate();\n                }\n            }\n        }\n        findMacroTaskOption(task) {\n            if (!this.macroTaskOptions) {\n                return null;\n            }\n            for (let i = 0; i < this.macroTaskOptions.length; i++) {\n                const macroTaskOption = this.macroTaskOptions[i];\n                if (macroTaskOption.source === task.source) {\n                    return macroTaskOption;\n                }\n            }\n            return null;\n        }\n        onHandleError(parentZoneDelegate, currentZone, targetZone, error) {\n            this._lastError = error;\n            return false; // Don't propagate error to parent zone.\n        }\n    }\n    // Export the class so that new instances can be created with proper\n    // constructor params.\n    Zone['FakeAsyncTestZoneSpec'] = FakeAsyncTestZoneSpec;\n})(typeof window === 'object' && window || typeof self === 'object' && self || global);\nZone.__load_patch('fakeasync', (global, Zone, api) => {\n    const FakeAsyncTestZoneSpec = Zone && Zone['FakeAsyncTestZoneSpec'];\n    function getProxyZoneSpec() {\n        return Zone && Zone['ProxyZoneSpec'];\n    }\n    let _fakeAsyncTestZoneSpec = null;\n    /**\n     * Clears out the shared fake async zone for a test.\n     * To be called in a global `beforeEach`.\n     *\n     * @experimental\n     */\n    function resetFakeAsyncZone() {\n        if (_fakeAsyncTestZoneSpec) {\n            _fakeAsyncTestZoneSpec.unlockDatePatch();\n        }\n        _fakeAsyncTestZoneSpec = null;\n        // in node.js testing we may not have ProxyZoneSpec in which case there is nothing to reset.\n        getProxyZoneSpec() && getProxyZoneSpec().assertPresent().resetDelegate();\n    }\n    /**\n     * Wraps a function to be executed in the fakeAsync zone:\n     * - microtasks are manually executed by calling `flushMicrotasks()`,\n     * - timers are synchronous, `tick()` simulates the asynchronous passage of time.\n     *\n     * If there are any pending timers at the end of the function, an exception will be thrown.\n     *\n     * Can be used to wrap inject() calls.\n     *\n     * ## Example\n     *\n     * {@example core/testing/ts/fake_async.ts region='basic'}\n     *\n     * @param fn\n     * @returns The function wrapped to be executed in the fakeAsync zone\n     *\n     * @experimental\n     */\n    function fakeAsync(fn) {\n        // Not using an arrow function to preserve context passed from call site\n        const fakeAsyncFn = function (...args) {\n            const ProxyZoneSpec = getProxyZoneSpec();\n            if (!ProxyZoneSpec) {\n                throw new Error('ProxyZoneSpec is needed for the async() test helper but could not be found. ' +\n                    'Please make sure that your environment includes zone.js/plugins/proxy');\n            }\n            const proxyZoneSpec = ProxyZoneSpec.assertPresent();\n            if (Zone.current.get('FakeAsyncTestZoneSpec')) {\n                throw new Error('fakeAsync() calls can not be nested');\n            }\n            try {\n                // in case jasmine.clock init a fakeAsyncTestZoneSpec\n                if (!_fakeAsyncTestZoneSpec) {\n                    if (proxyZoneSpec.getDelegate() instanceof FakeAsyncTestZoneSpec) {\n                        throw new Error('fakeAsync() calls can not be nested');\n                    }\n                    _fakeAsyncTestZoneSpec = new FakeAsyncTestZoneSpec();\n                }\n                let res;\n                const lastProxyZoneSpec = proxyZoneSpec.getDelegate();\n                proxyZoneSpec.setDelegate(_fakeAsyncTestZoneSpec);\n                _fakeAsyncTestZoneSpec.lockDatePatch();\n                try {\n                    res = fn.apply(this, args);\n                    flushMicrotasks();\n                }\n                finally {\n                    proxyZoneSpec.setDelegate(lastProxyZoneSpec);\n                }\n                if (_fakeAsyncTestZoneSpec.pendingPeriodicTimers.length > 0) {\n                    throw new Error(`${_fakeAsyncTestZoneSpec.pendingPeriodicTimers.length} ` +\n                        `periodic timer(s) still in the queue.`);\n                }\n                if (_fakeAsyncTestZoneSpec.pendingTimers.length > 0) {\n                    throw new Error(`${_fakeAsyncTestZoneSpec.pendingTimers.length} timer(s) still in the queue.`);\n                }\n                return res;\n            }\n            finally {\n                resetFakeAsyncZone();\n            }\n        };\n        fakeAsyncFn.isFakeAsync = true;\n        return fakeAsyncFn;\n    }\n    function _getFakeAsyncZoneSpec() {\n        if (_fakeAsyncTestZoneSpec == null) {\n            _fakeAsyncTestZoneSpec = Zone.current.get('FakeAsyncTestZoneSpec');\n            if (_fakeAsyncTestZoneSpec == null) {\n                throw new Error('The code should be running in the fakeAsync zone to call this function');\n            }\n        }\n        return _fakeAsyncTestZoneSpec;\n    }\n    /**\n     * Simulates the asynchronous passage of time for the timers in the fakeAsync zone.\n     *\n     * The microtasks queue is drained at the very start of this function and after any timer callback\n     * has been executed.\n     *\n     * ## Example\n     *\n     * {@example core/testing/ts/fake_async.ts region='basic'}\n     *\n     * @experimental\n     */\n    function tick(millis = 0, ignoreNestedTimeout = false) {\n        _getFakeAsyncZoneSpec().tick(millis, null, ignoreNestedTimeout);\n    }\n    /**\n     * Simulates the asynchronous passage of time for the timers in the fakeAsync zone by\n     * draining the macrotask queue until it is empty. The returned value is the milliseconds\n     * of time that would have been elapsed.\n     *\n     * @param maxTurns\n     * @returns The simulated time elapsed, in millis.\n     *\n     * @experimental\n     */\n    function flush(maxTurns) {\n        return _getFakeAsyncZoneSpec().flush(maxTurns);\n    }\n    /**\n     * Discard all remaining periodic tasks.\n     *\n     * @experimental\n     */\n    function discardPeriodicTasks() {\n        const zoneSpec = _getFakeAsyncZoneSpec();\n        zoneSpec.pendingPeriodicTimers;\n        zoneSpec.pendingPeriodicTimers.length = 0;\n    }\n    /**\n     * Flush any pending microtasks.\n     *\n     * @experimental\n     */\n    function flushMicrotasks() {\n        _getFakeAsyncZoneSpec().flushMicrotasks();\n    }\n    Zone[api.symbol('fakeAsyncTest')] =\n        { resetFakeAsyncZone, flushMicrotasks, discardPeriodicTasks, tick, flush, fakeAsync };\n}, true);\n\n/**\n * Promise for async/fakeAsync zoneSpec test\n * can support async operation which not supported by zone.js\n * such as\n * it ('test jsonp in AsyncZone', async() => {\n *   new Promise(res => {\n *     jsonp(url, (data) => {\n *       // success callback\n *       res(data);\n *     });\n *   }).then((jsonpResult) => {\n *     // get jsonp result.\n *\n *     // user will expect AsyncZoneSpec wait for\n *     // then, but because jsonp is not zone aware\n *     // AsyncZone will finish before then is called.\n *   });\n * });\n */\nZone.__load_patch('promisefortest', (global, Zone, api) => {\n    const symbolState = api.symbol('state');\n    const UNRESOLVED = null;\n    const symbolParentUnresolved = api.symbol('parentUnresolved');\n    // patch Promise.prototype.then to keep an internal\n    // number for tracking unresolved chained promise\n    // we will decrease this number when the parent promise\n    // being resolved/rejected and chained promise was\n    // scheduled as a microTask.\n    // so we can know such kind of chained promise still\n    // not resolved in AsyncTestZone\n    Promise[api.symbol('patchPromiseForTest')] = function patchPromiseForTest() {\n        let oriThen = Promise[Zone.__symbol__('ZonePromiseThen')];\n        if (oriThen) {\n            return;\n        }\n        oriThen = Promise[Zone.__symbol__('ZonePromiseThen')] = Promise.prototype.then;\n        Promise.prototype.then = function () {\n            const chained = oriThen.apply(this, arguments);\n            if (this[symbolState] === UNRESOLVED) {\n                // parent promise is unresolved.\n                const asyncTestZoneSpec = Zone.current.get('AsyncTestZoneSpec');\n                if (asyncTestZoneSpec) {\n                    asyncTestZoneSpec.unresolvedChainedPromiseCount++;\n                    chained[symbolParentUnresolved] = true;\n                }\n            }\n            return chained;\n        };\n    };\n    Promise[api.symbol('unPatchPromiseForTest')] = function unpatchPromiseForTest() {\n        // restore origin then\n        const oriThen = Promise[Zone.__symbol__('ZonePromiseThen')];\n        if (oriThen) {\n            Promise.prototype.then = oriThen;\n            Promise[Zone.__symbol__('ZonePromiseThen')] = undefined;\n        }\n    };\n});\n"], "mappings": "AAAA,YAAY;;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,OAAO,GAAG,IAAI;AACpB,MAAMC,aAAa,GAAG,CAAC,CAAC;AACxB,MAAMC,aAAa,GAAG,mBAAmB;AACzC,MAAMC,SAAS,GAAG,qBAAqB;AACvC,MAAMC,OAAO,GAAG,aAAa;AAC7B,IAAIC,WAAW,GAAGD,OAAO,GAAG,WAAW;AACvC,MAAME,cAAc,CAAC;EACjBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,KAAK,GAAGC,aAAa,CAAC,CAAC;IAC5B,IAAI,CAACC,SAAS,GAAG,IAAIC,IAAI,CAAC,CAAC;EAC/B;AACJ;AACA,SAASC,8BAA8BA,CAAA,EAAG;EACtC,OAAO,IAAIC,KAAK,CAACV,SAAS,CAAC;AAC/B;AACA,SAASW,4BAA4BA,CAAA,EAAG;EACpC,IAAI;IACA,MAAMF,8BAA8B,CAAC,CAAC;EAC1C,CAAC,CACD,OAAOG,GAAG,EAAE;IACR,OAAOA,GAAG;EACd;AACJ;AACA;AACA;AACA,MAAMP,KAAK,GAAGI,8BAA8B,CAAC,CAAC;AAC9C,MAAMI,WAAW,GAAGF,4BAA4B,CAAC,CAAC;AAClD,MAAML,aAAa,GAAGD,KAAK,CAACS,KAAK,GAC7BL,8BAA8B,GAC7BI,WAAW,CAACC,KAAK,GAAGH,4BAA4B,GAAGF,8BAA+B;AACvF,SAASM,SAASA,CAACV,KAAK,EAAE;EACtB,OAAOA,KAAK,CAACS,KAAK,GAAGT,KAAK,CAACS,KAAK,CAACE,KAAK,CAACnB,OAAO,CAAC,GAAG,EAAE;AACxD;AACA,SAASoB,aAAaA,CAACC,KAAK,EAAEb,KAAK,EAAE;EACjC,IAAIc,KAAK,GAAGJ,SAAS,CAACV,KAAK,CAAC;EAC5B,KAAK,IAAIe,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,KAAK,CAACE,MAAM,EAAED,CAAC,EAAE,EAAE;IACnC,MAAME,KAAK,GAAGH,KAAK,CAACC,CAAC,CAAC;IACtB;IACA,IAAI,CAACtB,aAAa,CAACyB,cAAc,CAACD,KAAK,CAAC,EAAE;MACtCJ,KAAK,CAACM,IAAI,CAACL,KAAK,CAACC,CAAC,CAAC,CAAC;IACxB;EACJ;AACJ;AACA,SAASK,oBAAoBA,CAACC,MAAM,EAAEZ,KAAK,EAAE;EACzC,MAAMa,SAAS,GAAG,CAACb,KAAK,GAAGA,KAAK,CAACc,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC;EAC7C,IAAIF,MAAM,EAAE;IACR,IAAInB,SAAS,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACqB,OAAO,CAAC,CAAC;IACpC,KAAK,IAAIT,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGM,MAAM,CAACL,MAAM,EAAED,CAAC,EAAE,EAAE;MACpC,MAAMU,WAAW,GAAGJ,MAAM,CAACN,CAAC,CAAC;MAC7B,MAAMW,QAAQ,GAAGD,WAAW,CAACvB,SAAS;MACtC,IAAIyB,SAAS,GAAI,+BAA8BzB,SAAS,GAAGwB,QAAQ,CAACF,OAAO,CAAC,CAAE,YAAWE,QAAS,EAAC;MACnGC,SAAS,GAAGA,SAAS,CAACC,OAAO,CAAC,UAAU,EAAE,GAAG,CAAC;MAC9CN,SAAS,CAACH,IAAI,CAACtB,WAAW,CAAC+B,OAAO,CAAChC,OAAO,EAAE+B,SAAS,CAAC,CAAC;MACvDf,aAAa,CAACU,SAAS,EAAEG,WAAW,CAACzB,KAAK,CAAC;MAC3CE,SAAS,GAAGwB,QAAQ,CAACF,OAAO,CAAC,CAAC;IAClC;EACJ;EACA,OAAOF,SAAS,CAACO,IAAI,CAACrC,OAAO,CAAC;AAClC;AACA;AACA;AACA;AACA;AACA,SAASsC,kBAAkBA,CAAA,EAAG;EAC1B;EACA;EACA,OAAOzB,KAAK,CAAC0B,eAAe,GAAG,CAAC;AACpC;AACAC,IAAI,CAAC,wBAAwB,CAAC,GAAG;EAC7BC,IAAI,EAAE,kBAAkB;EACxBC,mBAAmB,EAAE,EAAE;EACvB;EACA;EACAC,iBAAiB,EAAE,SAAAA,CAAUnC,KAAK,EAAE;IAChC,IAAI,CAACA,KAAK,EAAE;MACR,OAAOoC,SAAS;IACpB;IACA,MAAMtB,KAAK,GAAGd,KAAK,CAACgC,IAAI,CAACK,UAAU,CAAC,kBAAkB,CAAC,CAAC;IACxD,IAAI,CAACvB,KAAK,EAAE;MACR,OAAOd,KAAK,CAACS,KAAK;IACtB;IACA,OAAOW,oBAAoB,CAACN,KAAK,EAAEd,KAAK,CAACS,KAAK,CAAC;EACnD,CAAC;EACD6B,cAAc,EAAE,SAAAA,CAAUC,kBAAkB,EAAEC,WAAW,EAAEC,UAAU,EAAEC,IAAI,EAAE;IACzE,IAAIZ,kBAAkB,CAAC,CAAC,EAAE;MACtB,MAAMa,WAAW,GAAGX,IAAI,CAACW,WAAW;MACpC,IAAI7B,KAAK,GAAG6B,WAAW,IAAIA,WAAW,CAACC,IAAI,IAAID,WAAW,CAACC,IAAI,CAAClD,aAAa,CAAC,IAAI,EAAE;MACpFoB,KAAK,GAAG,CAAC,IAAIhB,cAAc,CAAC,CAAC,CAAC,CAAC+C,MAAM,CAAC/B,KAAK,CAAC;MAC5C,IAAIA,KAAK,CAACE,MAAM,GAAG,IAAI,CAACkB,mBAAmB,EAAE;QACzCpB,KAAK,CAACE,MAAM,GAAG,IAAI,CAACkB,mBAAmB;MAC3C;MACA,IAAI,CAACQ,IAAI,CAACE,IAAI,EACVF,IAAI,CAACE,IAAI,GAAG,CAAC,CAAC;MAClB,IAAIF,IAAI,CAACI,IAAI,KAAK,WAAW,EAAE;QAC3B;QACA;QACA;QACA;QACA;QACAJ,IAAI,CAACE,IAAI,GAAG;UAAE,GAAGF,IAAI,CAACE;QAAK,CAAC;MAChC;MACAF,IAAI,CAACE,IAAI,CAAClD,aAAa,CAAC,GAAGoB,KAAK;IACpC;IACA,OAAOyB,kBAAkB,CAACQ,YAAY,CAACN,UAAU,EAAEC,IAAI,CAAC;EAC5D,CAAC;EACDM,aAAa,EAAE,SAAAA,CAAUT,kBAAkB,EAAEC,WAAW,EAAEC,UAAU,EAAEzC,KAAK,EAAE;IACzE,IAAI8B,kBAAkB,CAAC,CAAC,EAAE;MACtB,MAAMmB,UAAU,GAAGjB,IAAI,CAACW,WAAW,IAAI3C,KAAK,CAAC0C,IAAI;MACjD,IAAI1C,KAAK,YAAYK,KAAK,IAAI4C,UAAU,EAAE;QACtC,MAAMC,SAAS,GAAG9B,oBAAoB,CAAC6B,UAAU,CAACL,IAAI,IAAIK,UAAU,CAACL,IAAI,CAAClD,aAAa,CAAC,EAAEM,KAAK,CAACS,KAAK,CAAC;QACtG,IAAI;UACAT,KAAK,CAACS,KAAK,GAAGT,KAAK,CAACkD,SAAS,GAAGA,SAAS;QAC7C,CAAC,CACD,OAAO3C,GAAG,EAAE,CACZ;MACJ;IACJ;IACA,OAAOgC,kBAAkB,CAACY,WAAW,CAACV,UAAU,EAAEzC,KAAK,CAAC;EAC5D;AACJ,CAAC;AACD,SAASoD,kBAAkBA,CAACC,WAAW,EAAEC,KAAK,EAAE;EAC5C,IAAIA,KAAK,GAAG,CAAC,EAAE;IACXD,WAAW,CAAClC,IAAI,CAACT,SAAS,CAAE,IAAIZ,cAAc,CAAC,CAAC,CAAEE,KAAK,CAAC,CAAC;IACzDoD,kBAAkB,CAACC,WAAW,EAAEC,KAAK,GAAG,CAAC,CAAC;EAC9C;AACJ;AACA,SAASC,mBAAmBA,CAAA,EAAG;EAC3B,IAAI,CAACzB,kBAAkB,CAAC,CAAC,EAAE;IACvB;EACJ;EACA,MAAMT,MAAM,GAAG,EAAE;EACjB+B,kBAAkB,CAAC/B,MAAM,EAAE,CAAC,CAAC;EAC7B,MAAMmC,OAAO,GAAGnC,MAAM,CAAC,CAAC,CAAC;EACzB,MAAMoC,OAAO,GAAGpC,MAAM,CAAC,CAAC,CAAC;EACzB,KAAK,IAAIN,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyC,OAAO,CAACxC,MAAM,EAAED,CAAC,EAAE,EAAE;IACrC,MAAM2C,MAAM,GAAGF,OAAO,CAACzC,CAAC,CAAC;IACzB,IAAI2C,MAAM,CAACC,OAAO,CAAChE,SAAS,CAAC,IAAI,CAAC,CAAC,EAAE;MACjC,IAAIiE,KAAK,GAAGF,MAAM,CAACE,KAAK,CAAC,WAAW,CAAC;MACrC,IAAIA,KAAK,EAAE;QACP/D,WAAW,GAAG+D,KAAK,CAAC,CAAC,CAAC,GAAGhE,OAAO,GAAG,qBAAqB;QACxD;MACJ;IACJ;EACJ;EACA,KAAK,IAAImB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyC,OAAO,CAACxC,MAAM,EAAED,CAAC,EAAE,EAAE;IACrC,MAAM2C,MAAM,GAAGF,OAAO,CAACzC,CAAC,CAAC;IACzB,MAAM8C,MAAM,GAAGJ,OAAO,CAAC1C,CAAC,CAAC;IACzB,IAAI2C,MAAM,KAAKG,MAAM,EAAE;MACnBpE,aAAa,CAACiE,MAAM,CAAC,GAAG,IAAI;IAChC,CAAC,MACI;MACD;IACJ;EACJ;AACJ;AACAH,mBAAmB,CAAC,CAAC;AAErB,MAAMO,aAAa,CAAC;EAChB,OAAOC,GAAGA,CAAA,EAAG;IACT,OAAO/B,IAAI,CAACgC,OAAO,CAACD,GAAG,CAAC,eAAe,CAAC;EAC5C;EACA,OAAOE,QAAQA,CAAA,EAAG;IACd,OAAOH,aAAa,CAACC,GAAG,CAAC,CAAC,YAAYD,aAAa;EACvD;EACA,OAAOI,aAAaA,CAAA,EAAG;IACnB,IAAI,CAACJ,aAAa,CAACG,QAAQ,CAAC,CAAC,EAAE;MAC3B,MAAM,IAAI5D,KAAK,CAAE,8DAA6D,CAAC;IACnF;IACA,OAAOyD,aAAa,CAACC,GAAG,CAAC,CAAC;EAC9B;EACAhE,WAAWA,CAACoE,mBAAmB,GAAG,IAAI,EAAE;IACpC,IAAI,CAACA,mBAAmB,GAAGA,mBAAmB;IAC9C,IAAI,CAAClC,IAAI,GAAG,WAAW;IACvB,IAAI,CAACmC,aAAa,GAAG,IAAI;IACzB,IAAI,CAACC,UAAU,GAAG;MAAE,eAAe,EAAE;IAAK,CAAC;IAC3C,IAAI,CAACC,YAAY,GAAG,IAAI;IACxB,IAAI,CAACC,aAAa,GAAG,IAAI;IACzB,IAAI,CAACC,sBAAsB,GAAG,KAAK;IACnC,IAAI,CAACC,KAAK,GAAG,EAAE;IACf,IAAI,CAACC,WAAW,CAACP,mBAAmB,CAAC;EACzC;EACAO,WAAWA,CAACC,YAAY,EAAE;IACtB,MAAMC,aAAa,GAAG,IAAI,CAACR,aAAa,KAAKO,YAAY;IACzD,IAAI,CAACP,aAAa,GAAGO,YAAY;IACjC,IAAI,CAACL,YAAY,IAAI,IAAI,CAACA,YAAY,CAACO,OAAO,CAAEC,GAAG,IAAK,OAAO,IAAI,CAACT,UAAU,CAACS,GAAG,CAAC,CAAC;IACpF,IAAI,CAACR,YAAY,GAAG,IAAI;IACxB,IAAIK,YAAY,IAAIA,YAAY,CAACN,UAAU,EAAE;MACzC,IAAI,CAACC,YAAY,GAAGS,MAAM,CAACC,IAAI,CAACL,YAAY,CAACN,UAAU,CAAC;MACxD,IAAI,CAACC,YAAY,CAACO,OAAO,CAAEI,CAAC,IAAK,IAAI,CAACZ,UAAU,CAACY,CAAC,CAAC,GAAGN,YAAY,CAACN,UAAU,CAACY,CAAC,CAAC,CAAC;IACrF;IACA;IACA,IAAIL,aAAa,IAAI,IAAI,CAACL,aAAa,KAClC,IAAI,CAACA,aAAa,CAACW,SAAS,IAAI,IAAI,CAACX,aAAa,CAACY,SAAS,CAAC,EAAE;MAChE,IAAI,CAACX,sBAAsB,GAAG,IAAI;IACtC;EACJ;EACAY,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAAChB,aAAa;EAC7B;EACAiB,aAAaA,CAAA,EAAG;IACZ,IAAI,CAACD,WAAW,CAAC,CAAC;IAClB,IAAI,CAACV,WAAW,CAAC,IAAI,CAACP,mBAAmB,CAAC;EAC9C;EACAmB,iBAAiBA,CAAC/C,kBAAkB,EAAEC,WAAW,EAAEC,UAAU,EAAE;IAC3D,IAAI,IAAI,CAAC+B,sBAAsB,IAAI,IAAI,CAACD,aAAa,EAAE;MACnD;MACA;MACA,IAAI,CAACC,sBAAsB,GAAG,KAAK;MACnC,IAAI,CAACe,SAAS,CAAChD,kBAAkB,EAAEC,WAAW,EAAEC,UAAU,EAAE,IAAI,CAAC8B,aAAa,CAAC;IACnF;EACJ;EACAiB,eAAeA,CAAC9C,IAAI,EAAE;IAClB,IAAI,CAAC,IAAI,CAAC+B,KAAK,EAAE;MACb;IACJ;IACA,KAAK,IAAI1D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAAC0D,KAAK,CAACzD,MAAM,EAAED,CAAC,EAAE,EAAE;MACxC,IAAI,IAAI,CAAC0D,KAAK,CAAC1D,CAAC,CAAC,KAAK2B,IAAI,EAAE;QACxB,IAAI,CAAC+B,KAAK,CAACgB,MAAM,CAAC1E,CAAC,EAAE,CAAC,CAAC;QACvB;MACJ;IACJ;EACJ;EACA2E,2BAA2BA,CAAA,EAAG;IAC1B,IAAI,IAAI,CAACjB,KAAK,CAACzD,MAAM,KAAK,CAAC,EAAE;MACzB,OAAO,EAAE;IACb;IACA,MAAM2E,QAAQ,GAAG,IAAI,CAAClB,KAAK,CAACmB,GAAG,CAAElD,IAAI,IAAK;MACtC,MAAMmD,QAAQ,GAAGnD,IAAI,CAACE,IAAI,IACtBmC,MAAM,CAACC,IAAI,CAACtC,IAAI,CAACE,IAAI,CAAC,CACjBgD,GAAG,CAAEd,GAAG,IAAK;QACd,OAAOA,GAAG,GAAG,GAAG,GAAGpC,IAAI,CAACE,IAAI,CAACkC,GAAG,CAAC;MACrC,CAAC,CAAC,CACGjD,IAAI,CAAC,GAAG,CAAC;MAClB,OAAQ,SAAQa,IAAI,CAACI,IAAK,aAAYJ,IAAI,CAACoD,MAAO,YAAWD,QAAS,GAAE;IAC5E,CAAC,CAAC;IACF,MAAME,gBAAgB,GAAG,8BAA8B,GAAGJ,QAAQ,GAAG,GAAG;IACxE;IACA,IAAI,CAAClB,KAAK,GAAG,EAAE;IACf,OAAOsB,gBAAgB;EAC3B;EACAC,MAAMA,CAACzD,kBAAkB,EAAEC,WAAW,EAAEC,UAAU,EAAEwD,QAAQ,EAAE;IAC1D,IAAI,IAAI,CAAC7B,aAAa,IAAI,IAAI,CAACA,aAAa,CAAC4B,MAAM,EAAE;MACjD,OAAO,IAAI,CAAC5B,aAAa,CAAC4B,MAAM,CAACzD,kBAAkB,EAAEC,WAAW,EAAEC,UAAU,EAAEwD,QAAQ,CAAC;IAC3F,CAAC,MACI;MACD,OAAO1D,kBAAkB,CAAC2D,IAAI,CAACzD,UAAU,EAAEwD,QAAQ,CAAC;IACxD;EACJ;EACAE,WAAWA,CAAC5D,kBAAkB,EAAEC,WAAW,EAAEC,UAAU,EAAE2D,QAAQ,EAAEN,MAAM,EAAE;IACvE,IAAI,IAAI,CAAC1B,aAAa,IAAI,IAAI,CAACA,aAAa,CAAC+B,WAAW,EAAE;MACtD,OAAO,IAAI,CAAC/B,aAAa,CAAC+B,WAAW,CAAC5D,kBAAkB,EAAEC,WAAW,EAAEC,UAAU,EAAE2D,QAAQ,EAAEN,MAAM,CAAC;IACxG,CAAC,MACI;MACD,OAAOvD,kBAAkB,CAAC8D,SAAS,CAAC5D,UAAU,EAAE2D,QAAQ,EAAEN,MAAM,CAAC;IACrE;EACJ;EACAQ,QAAQA,CAAC/D,kBAAkB,EAAEC,WAAW,EAAEC,UAAU,EAAE2D,QAAQ,EAAEG,SAAS,EAAEC,SAAS,EAAEV,MAAM,EAAE;IAC1F,IAAI,CAACR,iBAAiB,CAAC/C,kBAAkB,EAAEC,WAAW,EAAEC,UAAU,CAAC;IACnE,IAAI,IAAI,CAAC2B,aAAa,IAAI,IAAI,CAACA,aAAa,CAACkC,QAAQ,EAAE;MACnD,OAAO,IAAI,CAAClC,aAAa,CAACkC,QAAQ,CAAC/D,kBAAkB,EAAEC,WAAW,EAAEC,UAAU,EAAE2D,QAAQ,EAAEG,SAAS,EAAEC,SAAS,EAAEV,MAAM,CAAC;IAC3H,CAAC,MACI;MACD,OAAOvD,kBAAkB,CAACkE,MAAM,CAAChE,UAAU,EAAE2D,QAAQ,EAAEG,SAAS,EAAEC,SAAS,EAAEV,MAAM,CAAC;IACxF;EACJ;EACA9C,aAAaA,CAACT,kBAAkB,EAAEC,WAAW,EAAEC,UAAU,EAAEzC,KAAK,EAAE;IAC9D,IAAI,IAAI,CAACoE,aAAa,IAAI,IAAI,CAACA,aAAa,CAACpB,aAAa,EAAE;MACxD,OAAO,IAAI,CAACoB,aAAa,CAACpB,aAAa,CAACT,kBAAkB,EAAEC,WAAW,EAAEC,UAAU,EAAEzC,KAAK,CAAC;IAC/F,CAAC,MACI;MACD,OAAOuC,kBAAkB,CAACY,WAAW,CAACV,UAAU,EAAEzC,KAAK,CAAC;IAC5D;EACJ;EACAsC,cAAcA,CAACC,kBAAkB,EAAEC,WAAW,EAAEC,UAAU,EAAEC,IAAI,EAAE;IAC9D,IAAIA,IAAI,CAACI,IAAI,KAAK,WAAW,EAAE;MAC3B,IAAI,CAAC2B,KAAK,CAACtD,IAAI,CAACuB,IAAI,CAAC;IACzB;IACA,IAAI,IAAI,CAAC0B,aAAa,IAAI,IAAI,CAACA,aAAa,CAAC9B,cAAc,EAAE;MACzD,OAAO,IAAI,CAAC8B,aAAa,CAAC9B,cAAc,CAACC,kBAAkB,EAAEC,WAAW,EAAEC,UAAU,EAAEC,IAAI,CAAC;IAC/F,CAAC,MACI;MACD,OAAOH,kBAAkB,CAACQ,YAAY,CAACN,UAAU,EAAEC,IAAI,CAAC;IAC5D;EACJ;EACAgE,YAAYA,CAACnE,kBAAkB,EAAEC,WAAW,EAAEC,UAAU,EAAEC,IAAI,EAAE6D,SAAS,EAAEC,SAAS,EAAE;IAClF,IAAI9D,IAAI,CAACI,IAAI,KAAK,WAAW,EAAE;MAC3B,IAAI,CAAC0C,eAAe,CAAC9C,IAAI,CAAC;IAC9B;IACA,IAAI,CAAC4C,iBAAiB,CAAC/C,kBAAkB,EAAEC,WAAW,EAAEC,UAAU,CAAC;IACnE,IAAI,IAAI,CAAC2B,aAAa,IAAI,IAAI,CAACA,aAAa,CAACsC,YAAY,EAAE;MACvD,OAAO,IAAI,CAACtC,aAAa,CAACsC,YAAY,CAACnE,kBAAkB,EAAEC,WAAW,EAAEC,UAAU,EAAEC,IAAI,EAAE6D,SAAS,EAAEC,SAAS,CAAC;IACnH,CAAC,MACI;MACD,OAAOjE,kBAAkB,CAACoE,UAAU,CAAClE,UAAU,EAAEC,IAAI,EAAE6D,SAAS,EAAEC,SAAS,CAAC;IAChF;EACJ;EACAI,YAAYA,CAACrE,kBAAkB,EAAEC,WAAW,EAAEC,UAAU,EAAEC,IAAI,EAAE;IAC5D,IAAIA,IAAI,CAACI,IAAI,KAAK,WAAW,EAAE;MAC3B,IAAI,CAAC0C,eAAe,CAAC9C,IAAI,CAAC;IAC9B;IACA,IAAI,CAAC4C,iBAAiB,CAAC/C,kBAAkB,EAAEC,WAAW,EAAEC,UAAU,CAAC;IACnE,IAAI,IAAI,CAAC2B,aAAa,IAAI,IAAI,CAACA,aAAa,CAACwC,YAAY,EAAE;MACvD,OAAO,IAAI,CAACxC,aAAa,CAACwC,YAAY,CAACrE,kBAAkB,EAAEC,WAAW,EAAEC,UAAU,EAAEC,IAAI,CAAC;IAC7F,CAAC,MACI;MACD,OAAOH,kBAAkB,CAACsE,UAAU,CAACpE,UAAU,EAAEC,IAAI,CAAC;IAC1D;EACJ;EACA6C,SAASA,CAACa,QAAQ,EAAEpC,OAAO,EAAE8C,MAAM,EAAEC,YAAY,EAAE;IAC/C,IAAI,CAACxC,aAAa,GAAGwC,YAAY;IACjC,IAAI,IAAI,CAAC3C,aAAa,IAAI,IAAI,CAACA,aAAa,CAACmB,SAAS,EAAE;MACpD,IAAI,CAACnB,aAAa,CAACmB,SAAS,CAACa,QAAQ,EAAEpC,OAAO,EAAE8C,MAAM,EAAEC,YAAY,CAAC;IACzE,CAAC,MACI;MACDX,QAAQ,CAACY,OAAO,CAACF,MAAM,EAAEC,YAAY,CAAC;IAC1C;EACJ;AACJ;AACA;AACA;AACA/E,IAAI,CAAC,eAAe,CAAC,GAAG8B,aAAa;AAErC,MAAMmD,gBAAgB,CAAC;EACnBlH,WAAWA,CAACmH,UAAU,EAAE;IACpB,IAAI,CAACC,OAAO,GAAGnF,IAAI,CAACgC,OAAO;IAC3B,IAAI,CAAC/B,IAAI,GAAG,mBAAmB,GAAGiF,UAAU;EAChD;EACA5E,cAAcA,CAAC8D,QAAQ,EAAEpC,OAAO,EAAE8C,MAAM,EAAEpE,IAAI,EAAE;IAC5C,QAAQA,IAAI,CAACI,IAAI;MACb,KAAK,WAAW;MAChB,KAAK,WAAW;QACZ,MAAM,IAAIzC,KAAK,CAAE,eAAcqC,IAAI,CAACoD,MAAO,6BAA4B,IAAI,CAAC7D,IAAK,IAAG,CAAC;MACzF,KAAK,WAAW;QACZS,IAAI,GAAG0D,QAAQ,CAACrD,YAAY,CAAC+D,MAAM,EAAEpE,IAAI,CAAC;QAC1C;IACR;IACA,OAAOA,IAAI;EACf;AACJ;AACA;AACA;AACAV,IAAI,CAAC,kBAAkB,CAAC,GAAGiF,gBAAgB;;AAE3C;AACAjF,IAAI,CAACoF,YAAY,CAAC,SAAS,EAAE,CAACC,MAAM,EAAErF,IAAI,EAAEsF,GAAG,KAAK;EAChD,MAAMC,SAAS,GAAG,SAAAA,CAAUC,CAAC,EAAEC,CAAC,EAAE;IAC9B,KAAK,MAAMC,CAAC,IAAID,CAAC,EACb,IAAIA,CAAC,CAACvG,cAAc,CAACwG,CAAC,CAAC,EACnBF,CAAC,CAACE,CAAC,CAAC,GAAGD,CAAC,CAACC,CAAC,CAAC;IACnB,SAASC,EAAEA,CAAA,EAAG;MACV,IAAI,CAAC5H,WAAW,GAAGyH,CAAC;IACxB;IACAA,CAAC,CAACI,SAAS,GAAGH,CAAC,KAAK,IAAI,GAAG1C,MAAM,CAAC8C,MAAM,CAACJ,CAAC,CAAC,IAAKE,EAAE,CAACC,SAAS,GAAGH,CAAC,CAACG,SAAS,EAAG,IAAID,EAAE,CAAC,CAAC,CAAC;EAC1F,CAAC;EACD;EACA;EACA,IAAI,CAAC3F,IAAI,EACL,MAAM,IAAI3B,KAAK,CAAC,kBAAkB,CAAC;EACvC,IAAI,OAAOyH,IAAI,KAAK,WAAW,EAAE;IAC7B;IACA;IACA;EACJ;EACA,IAAI,OAAOC,OAAO,IAAI,WAAW,IAAIA,OAAO,CAAC,gBAAgB,CAAC,EAAE;IAC5D;EACJ;EACAA,OAAO,CAAC,gBAAgB,CAAC,GAAG,IAAI;EAChC,MAAMd,gBAAgB,GAAGjF,IAAI,CAAC,kBAAkB,CAAC;EACjD,MAAM8B,aAAa,GAAG9B,IAAI,CAAC,eAAe,CAAC;EAC3C,IAAI,CAACiF,gBAAgB,EACjB,MAAM,IAAI5G,KAAK,CAAC,2BAA2B,CAAC;EAChD,IAAI,CAACyD,aAAa,EACd,MAAM,IAAIzD,KAAK,CAAC,wBAAwB,CAAC;EAC7C,MAAM2H,WAAW,GAAGhG,IAAI,CAACgC,OAAO;EAChC,MAAMiE,MAAM,GAAGjG,IAAI,CAACK,UAAU;EAC9B;EACA,MAAM6F,2BAA2B,GAAGb,MAAM,CAACY,MAAM,CAAC,+BAA+B,CAAC,CAAC,KAAK,IAAI;EAC5F;EACA;EACA;EACA,MAAME,mCAAmC,GAAG,CAACD,2BAA2B,KAClEb,MAAM,CAACY,MAAM,CAAC,oBAAoB,CAAC,CAAC,KAAK,IAAI,IAC1CZ,MAAM,CAACY,MAAM,CAAC,wCAAwC,CAAC,CAAC,KAAK,IAAK,CAAC;EAC5E,MAAMG,wBAAwB,GAAGf,MAAM,CAACY,MAAM,CAAC,0BAA0B,CAAC,CAAC,KAAK,IAAI;EACpF,IAAI,CAACG,wBAAwB,EAAE;IAC3B,MAAMC,YAAY,GAAGN,OAAO,CAACO,YAAY;IACzC,IAAID,YAAY,IAAI,CAACN,OAAO,CAACE,MAAM,CAAC,cAAc,CAAC,CAAC,EAAE;MAClDF,OAAO,CAACE,MAAM,CAAC,cAAc,CAAC,CAAC,GAAGI,YAAY;MAC9CN,OAAO,CAACO,YAAY,GAAG,YAAY;QAC/B,MAAMC,QAAQ,GAAG,IAAIF,YAAY,CAAC,CAAC;QACnC,MAAMG,eAAe,GAAGD,QAAQ,CAACE,OAAO;QACxC,IAAID,eAAe,IAAI,CAACD,QAAQ,CAACN,MAAM,CAAC,SAAS,CAAC,CAAC,EAAE;UACjDM,QAAQ,CAACN,MAAM,CAAC,SAAS,CAAC,CAAC,GAAGO,eAAe;UAC7CD,QAAQ,CAACE,OAAO,GAAG,YAAY;YAC3B,MAAMC,MAAM,GAAG,OAAOC,OAAO,KAAK,WAAW,IAAI,CAAC,CAACA,OAAO,CAACC,EAAE;YAC7D;YACA;YACA;YACA;YACA;YACA;YACA,MAAMC,gBAAgB,GAAGH,MAAM,GAAGC,OAAO,CAACG,SAAS,CAAC,oBAAoB,CAAC,GACrEzB,MAAM,CAAC0B,cAAc,CAAC,oBAAoB,CAAC;YAC/C,MAAMC,MAAM,GAAGR,eAAe,CAACS,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;YACrDR,MAAM,GAAGC,OAAO,CAACQ,kBAAkB,CAAC,oBAAoB,CAAC,GACrD9B,MAAM,CAAC8B,kBAAkB,CAAC,oBAAoB,CAAC;YACnD,IAAIN,gBAAgB,EAAE;cAClBA,gBAAgB,CAAChE,OAAO,CAACuE,OAAO,IAAI;gBAChC,IAAIV,MAAM,EAAE;kBACRC,OAAO,CAACC,EAAE,CAAC,oBAAoB,EAAEQ,OAAO,CAAC;gBAC7C,CAAC,MACI;kBACD/B,MAAM,CAACgC,gBAAgB,CAAC,oBAAoB,EAAED,OAAO,CAAC;gBAC1D;cACJ,CAAC,CAAC;YACN;YACA,OAAOJ,MAAM;UACjB,CAAC;QACL;QACA,OAAOT,QAAQ;MACnB,CAAC;IACL;EACJ;EACA;EACA,MAAMe,UAAU,GAAGvB,OAAO,CAACwB,MAAM,CAAC,CAAC;EACnC,CAAC,UAAU,EAAE,WAAW,EAAE,WAAW,CAAC,CAAC1E,OAAO,CAAC2E,UAAU,IAAI;IACzD,IAAIC,iBAAiB,GAAGH,UAAU,CAACE,UAAU,CAAC;IAC9CF,UAAU,CAACE,UAAU,CAAC,GAAG,UAAUE,WAAW,EAAEC,eAAe,EAAE;MAC7D,OAAOF,iBAAiB,CAACG,IAAI,CAAC,IAAI,EAAEF,WAAW,EAAEG,kBAAkB,CAACH,WAAW,EAAEC,eAAe,CAAC,CAAC;IACtG,CAAC;EACL,CAAC,CAAC;EACF,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC9E,OAAO,CAAC2E,UAAU,IAAI;IACvC,IAAIC,iBAAiB,GAAGH,UAAU,CAACE,UAAU,CAAC;IAC9CF,UAAU,CAACrB,MAAM,CAACuB,UAAU,CAAC,CAAC,GAAGC,iBAAiB;IAClDH,UAAU,CAACE,UAAU,CAAC,GAAG,UAAUE,WAAW,EAAEC,eAAe,EAAEG,OAAO,EAAE;MACtEZ,SAAS,CAAC,CAAC,CAAC,GAAGa,cAAc,CAACJ,eAAe,CAAC;MAC9C,OAAOF,iBAAiB,CAACR,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;IACnD,CAAC;EACL,CAAC,CAAC;EACF,CAAC,YAAY,EAAE,WAAW,EAAE,WAAW,EAAE,UAAU,CAAC,CAACrE,OAAO,CAAC2E,UAAU,IAAI;IACvE,IAAIC,iBAAiB,GAAGH,UAAU,CAACE,UAAU,CAAC;IAC9CF,UAAU,CAACrB,MAAM,CAACuB,UAAU,CAAC,CAAC,GAAGC,iBAAiB;IAClDH,UAAU,CAACE,UAAU,CAAC,GAAG,UAAUG,eAAe,EAAEG,OAAO,EAAE;MACzDZ,SAAS,CAAC,CAAC,CAAC,GAAGa,cAAc,CAACJ,eAAe,CAAC;MAC9C,OAAOF,iBAAiB,CAACR,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;IACnD,CAAC;EACL,CAAC,CAAC;EACF,IAAI,CAAChB,2BAA2B,EAAE;IAC9B;IACA;IACA,MAAM8B,eAAe,GAAIjC,OAAO,CAACE,MAAM,CAAC,OAAO,CAAC,CAAC,GAAGF,OAAO,CAAC,OAAO,CAAE;IACrEA,OAAO,CAAC,OAAO,CAAC,GAAG,YAAY;MAC3B,MAAMkC,KAAK,GAAGD,eAAe,CAACf,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;MACpD,IAAI,CAACe,KAAK,CAAChC,MAAM,CAAC,SAAS,CAAC,CAAC,EAAE;QAC3BgC,KAAK,CAAChC,MAAM,CAAC,SAAS,CAAC,CAAC,GAAGA,MAAM,CAAC,SAAS,CAAC;QAC5C,MAAMiC,YAAY,GAAID,KAAK,CAAChC,MAAM,CAAC,MAAM,CAAC,CAAC,GAAGgC,KAAK,CAACE,IAAK;QACzDF,KAAK,CAACE,IAAI,GAAG,YAAY;UACrB,MAAMC,iBAAiB,GAAGpI,IAAI,CAACgC,OAAO,CAACD,GAAG,CAAC,uBAAuB,CAAC;UACnE,IAAIqG,iBAAiB,EAAE;YACnB,OAAOA,iBAAiB,CAACD,IAAI,CAAClB,KAAK,CAACmB,iBAAiB,EAAElB,SAAS,CAAC;UACrE;UACA,OAAOgB,YAAY,CAACjB,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;QAC9C,CAAC;QACD,MAAMmB,gBAAgB,GAAIJ,KAAK,CAAChC,MAAM,CAAC,UAAU,CAAC,CAAC,GAAGgC,KAAK,CAACK,QAAS;QACrEL,KAAK,CAACK,QAAQ,GAAG,YAAY;UACzB,MAAMF,iBAAiB,GAAGpI,IAAI,CAACgC,OAAO,CAACD,GAAG,CAAC,uBAAuB,CAAC;UACnE,IAAIqG,iBAAiB,EAAE;YACnB,MAAMG,QAAQ,GAAGrB,SAAS,CAAClI,MAAM,GAAG,CAAC,GAAGkI,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI/I,IAAI,CAAC,CAAC;YACjE,OAAOiK,iBAAiB,CAACI,qBAAqB,CAACvB,KAAK,CAACmB,iBAAiB,EAAEG,QAAQ,IAAI,OAAOA,QAAQ,CAAC/I,OAAO,KAAK,UAAU,GAAG,CAAC+I,QAAQ,CAAC/I,OAAO,CAAC,CAAC,CAAC,GAC7I0H,SAAS,CAAC;UAClB;UACA,OAAOmB,gBAAgB,CAACpB,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;QAClD,CAAC;QACD;QACA,IAAIf,mCAAmC,EAAE;UACrC,CAAC,SAAS,EAAE,WAAW,CAAC,CAACtD,OAAO,CAAC2E,UAAU,IAAI;YAC3C,MAAMQ,eAAe,GAAIC,KAAK,CAAChC,MAAM,CAACuB,UAAU,CAAC,CAAC,GAAGS,KAAK,CAACT,UAAU,CAAE;YACvES,KAAK,CAACT,UAAU,CAAC,GAAG,YAAY;cAC5B,MAAMiB,qBAAqB,GAAGzI,IAAI,CAAC,uBAAuB,CAAC;cAC3D,IAAIyI,qBAAqB,EAAE;gBACvB1C,OAAO,CAACE,MAAM,CAAC,gBAAgB,CAAC,CAAC,GAAG,SAAS,KAAKuB,UAAU;gBAC5D;cACJ;cACA,OAAOQ,eAAe,CAACf,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;YACjD,CAAC;UACL,CAAC,CAAC;QACN;MACJ;MACA,OAAOe,KAAK;IAChB,CAAC;EACL;EACA;EACA,IAAI,CAAClC,OAAO,CAAC/F,IAAI,CAACK,UAAU,CAAC,cAAc,CAAC,CAAC,EAAE;IAC3C,MAAMqI,oBAAoB,GAAG3C,OAAO,CAAC4C,YAAY;IACjD5C,OAAO,CAAC/F,IAAI,CAACK,UAAU,CAAC,cAAc,CAAC,CAAC,GAAGqI,oBAAoB;IAC/D3C,OAAO,CAAC4C,YAAY,GAAG,YAAY;MAC/B,MAAMC,IAAI,GAAGC,KAAK,CAACjD,SAAS,CAACkD,KAAK,CAAClB,IAAI,CAACV,SAAS,CAAC;MAClD,MAAM6B,aAAa,GAAGH,IAAI,CAAC5J,MAAM,IAAI,CAAC,GAAG4J,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI;MACvD,IAAII,MAAM;MACV,IAAID,aAAa,EAAE;QACf,MAAME,cAAc,GAAGlG,MAAM,CAACkG,cAAc;QAC5ClG,MAAM,CAACkG,cAAc,GAAG,UAAUC,GAAG,EAAExD,CAAC,EAAEyD,UAAU,EAAE;UAClD,OAAOF,cAAc,CAACrB,IAAI,CAAC,IAAI,EAAEsB,GAAG,EAAExD,CAAC,EAAE;YAAE,GAAGyD,UAAU;YAAEC,YAAY,EAAE,IAAI;YAAEC,UAAU,EAAE;UAAK,CAAC,CAAC;QACrG,CAAC;QACD,IAAI;UACAL,MAAM,GAAGN,oBAAoB,CAACzB,KAAK,CAAC,IAAI,EAAE2B,IAAI,CAAC;QACnD,CAAC,SACO;UACJ7F,MAAM,CAACkG,cAAc,GAAGA,cAAc;QAC1C;MACJ,CAAC,MACI;QACDD,MAAM,GAAGN,oBAAoB,CAACzB,KAAK,CAAC,IAAI,EAAE2B,IAAI,CAAC;MACnD;MACA,OAAOI,MAAM;IACjB,CAAC;EACL;EACA;AACJ;AACA;AACA;EACI,SAASnB,kBAAkBA,CAACH,WAAW,EAAE4B,YAAY,EAAE;IACnD,OAAO,YAAY;MACf;MACA;MACA,MAAMC,QAAQ,GAAGvD,WAAW,CAAC9B,IAAI,CAAC,IAAIe,gBAAgB,CAAE,oBAAmByC,WAAY,EAAC,CAAC,CAAC;MAC1F,OAAO6B,QAAQ,CAACC,GAAG,CAACF,YAAY,EAAE,IAAI,EAAEpC,SAAS,CAAC;IACtD,CAAC;EACL;EACA,SAASuC,aAAaA,CAACC,QAAQ,EAAEnF,SAAS,EAAEoF,WAAW,EAAEC,IAAI,EAAE;IAC3D,MAAMC,gBAAgB,GAAG,CAAC,CAAC9D,OAAO,CAACE,MAAM,CAAC,gBAAgB,CAAC,CAAC;IAC5D0D,WAAW,CAACG,iBAAiB;IAC7B,MAAMC,aAAa,GAAGJ,WAAW,CAACI,aAAa;IAC/C,IAAIF,gBAAgB,IAAI1D,mCAAmC,EAAE;MACzD;MACA,MAAM6D,eAAe,GAAGhK,IAAI,CAACA,IAAI,CAACK,UAAU,CAAC,eAAe,CAAC,CAAC;MAC9D,IAAI2J,eAAe,IAAI,OAAOA,eAAe,CAACC,SAAS,KAAK,UAAU,EAAE;QACpEP,QAAQ,GAAGM,eAAe,CAACC,SAAS,CAACP,QAAQ,CAAC;MAClD;IACJ;IACA,IAAIE,IAAI,EAAE;MACN,OAAOG,aAAa,CAACP,GAAG,CAACE,QAAQ,EAAEnF,SAAS,EAAE,CAACqF,IAAI,CAAC,CAAC;IACzD,CAAC,MACI;MACD,OAAOG,aAAa,CAACP,GAAG,CAACE,QAAQ,EAAEnF,SAAS,CAAC;IACjD;EACJ;EACA;AACJ;AACA;AACA;AACA;EACI,SAASwD,cAAcA,CAAC2B,QAAQ,EAAE;IAC9B;IACA;IACA;IACA,OAAQA,QAAQ,KAAKA,QAAQ,CAAC1K,MAAM,GAAG,UAAU4K,IAAI,EAAE;MACnD,OAAOH,aAAa,CAACC,QAAQ,EAAE,IAAI,EAAE,IAAI,CAACC,WAAW,EAAEC,IAAI,CAAC;IAChE,CAAC,GAAG,YAAY;MACZ,OAAOH,aAAa,CAACC,QAAQ,EAAE,IAAI,EAAE,IAAI,CAACC,WAAW,CAAC;IAC1D,CAAC,CAAC;EACN;EACA,MAAMO,WAAW,GAAGnE,OAAO,CAACmE,WAAW;EACvCnE,OAAO,CAACmE,WAAW,GAAI,UAAUC,MAAM,EAAE;IACrC5E,SAAS,CAAC6E,eAAe,EAAED,MAAM,CAAC;IAClC,SAASC,eAAeA,CAACC,KAAK,EAAE;MAC5B,IAAIA,KAAK,CAACC,UAAU,EAAE;QAClBD,KAAK,CAACC,UAAU,GAAG,CAACC,EAAE,IAAI,MAAM;UAC5B;UACA,IAAI,CAACR,aAAa,GAAG,IAAI;UACzB,IAAI,CAACD,iBAAiB,GAAG,IAAI;UAC7B9D,WAAW,CAACwE,iBAAiB,CAAC,oBAAoB,EAAED,EAAE,CAAC;QAC3D,CAAC,EAAEF,KAAK,CAACC,UAAU,CAAC;MACxB;MACA,MAAMG,gBAAgB,GAAGpF,MAAM,CAACrF,IAAI,CAACK,UAAU,CAAC,YAAY,CAAC,CAAC;MAC9D,MAAMqK,kBAAkB,GAAGrF,MAAM,CAACrF,IAAI,CAACK,UAAU,CAAC,cAAc,CAAC,CAAC;MAClE,IAAIoK,gBAAgB,EAAE;QAClB;QACAJ,KAAK,CAACvC,OAAO,GAAG;UACZ6C,UAAU,EAAEF,gBAAgB,GAAGA,gBAAgB,GAAGpF,MAAM,CAACsF,UAAU;UACnEC,YAAY,EAAEF,kBAAkB,GAAGA,kBAAkB,GAAGrF,MAAM,CAACuF;QACnE,CAAC;MACL;MACA;MACA;MACA,IAAI7E,OAAO,CAAC8E,WAAW,EAAE;QACrB,IAAI,CAACR,KAAK,CAACS,WAAW,EAAE;UACpBT,KAAK,CAACS,WAAW,GAAG,IAAI/E,OAAO,CAAC8E,WAAW,CAAC,CAAC;QACjD;QACAR,KAAK,CAACS,WAAW,CAACnB,WAAW,GAAG,IAAI;MACxC,CAAC,MACI;QACD,IAAI,CAACU,KAAK,CAACS,WAAW,EAAE;UACpBT,KAAK,CAACS,WAAW,GAAG,CAAC,CAAC;QAC1B;QACAT,KAAK,CAACS,WAAW,CAACnB,WAAW,GAAG,IAAI;MACxC;MACA;MACA,MAAMoB,WAAW,GAAGV,KAAK,CAACU,WAAW;MACrCV,KAAK,CAACU,WAAW,GAAG,UAAU/M,KAAK,EAAE;QACjC,IAAIA,KAAK,IACLA,KAAK,CAACgN,OAAO,KACT,wGAAwG,EAAE;UAC9G;UACA;UACA,MAAMC,aAAa,GAAG,IAAI,IAAI,IAAI,CAACnB,iBAAiB;UACpD,IAAImB,aAAa,EAAE;YACf,MAAMlH,gBAAgB,GAAGkH,aAAa,CAACvH,2BAA2B,CAAC,CAAC;YACpE,IAAI;cACA;cACA1F,KAAK,CAACgN,OAAO,IAAIjH,gBAAgB;YACrC,CAAC,CACD,OAAOxF,GAAG,EAAE,CACZ;UACJ;QACJ;QACA,IAAIwM,WAAW,EAAE;UACbA,WAAW,CAACnD,IAAI,CAAC,IAAI,EAAE5J,KAAK,CAAC;QACjC;MACJ,CAAC;MACDmM,MAAM,CAACvC,IAAI,CAAC,IAAI,EAAEyC,KAAK,CAAC;IAC5B;IACAD,eAAe,CAACxE,SAAS,CAACsF,OAAO,GAAG,YAAY;MAC5C,IAAIC,IAAI,GAAGnL,IAAI,CAACgC,OAAO;MACvB,IAAIoJ,oBAAoB,GAAG,KAAK;MAChC,OAAOD,IAAI,EAAE;QACT,IAAIA,IAAI,KAAKnF,WAAW,EAAE;UACtBoF,oBAAoB,GAAG,IAAI;UAC3B;QACJ;QACAD,IAAI,GAAGA,IAAI,CAACE,MAAM;MACtB;MACA,IAAI,CAACD,oBAAoB,EACrB,MAAM,IAAI/M,KAAK,CAAC,mBAAmB,GAAG2B,IAAI,CAACgC,OAAO,CAAC/B,IAAI,CAAC;MAC5D;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,IAAI,CAAC6J,iBAAiB,GAAG,IAAIhI,aAAa,CAAC,CAAC;MAC5C,IAAI,CAACiI,aAAa,GAAG/D,WAAW,CAAC9B,IAAI,CAAC,IAAI,CAAC4F,iBAAiB,CAAC;MAC7D,IAAI,CAAC9J,IAAI,CAACW,WAAW,EAAE;QACnB;QACA;QACA;QACA;QACA;QACAX,IAAI,CAACgC,OAAO,CAACwI,iBAAiB,CAAC,6BAA6B,EAAE,MAAMN,WAAW,CAACtE,SAAS,CAACsF,OAAO,CAACtD,IAAI,CAAC,IAAI,CAAC,CAAC;MACjH,CAAC,MACI;QACDuC,MAAM,CAACvE,SAAS,CAACsF,OAAO,CAACtD,IAAI,CAAC,IAAI,CAAC;MACvC;IACJ,CAAC;IACD,OAAOwC,eAAe;EAC1B,CAAC,CAAEF,WAAW,CAAC;AACnB,CAAC,CAAC;AAEFlK,IAAI,CAACoF,YAAY,CAAC,MAAM,EAAE,CAACkG,OAAO,EAAEtL,IAAI,EAAEsF,GAAG,KAAK;EAC9C,IAAI,OAAOQ,IAAI,KAAK,WAAW,IAAIA,IAAI,CAAC,gBAAgB,CAAC,EAAE;IACvD;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA9F,IAAI,CAACsF,GAAG,CAACW,MAAM,CAAC,iCAAiC,CAAC,CAAC,GAAG,IAAI;EAC1DH,IAAI,CAAC,gBAAgB,CAAC,GAAG,IAAI;EAC7B,MAAMhE,aAAa,GAAG9B,IAAI,CAAC,eAAe,CAAC;EAC3C,MAAMiF,gBAAgB,GAAGjF,IAAI,CAAC,kBAAkB,CAAC;EACjD,IAAI,CAAC8B,aAAa,EAAE;IAChB,MAAM,IAAIzD,KAAK,CAAC,uBAAuB,CAAC;EAC5C;EACA,MAAMkN,QAAQ,GAAGvL,IAAI,CAACgC,OAAO;EAC7B,MAAMuH,QAAQ,GAAGgC,QAAQ,CAACrH,IAAI,CAAC,IAAIe,gBAAgB,CAAC,eAAe,CAAC,CAAC;EACrE,MAAMgG,aAAa,GAAG,IAAInJ,aAAa,CAAC,CAAC;EACzC,MAAM0J,SAAS,GAAGD,QAAQ,CAACrH,IAAI,CAAC+G,aAAa,CAAC;EAC9C,SAASQ,yBAAyBA,CAACC,cAAc,EAAE;IAC/C,OAAO,UAAU,GAAGC,SAAS,EAAE;MAC3B,MAAMC,kBAAkB,GAAGF,cAAc,CAACzE,KAAK,CAAC,IAAI,EAAE0E,SAAS,CAAC;MAChE,OAAO,UAAU,GAAG/C,IAAI,EAAE;QACtBA,IAAI,CAAC,CAAC,CAAC,GAAGf,kBAAkB,CAACe,IAAI,CAAC,CAAC,CAAC,CAAC;QACrC,OAAOgD,kBAAkB,CAAC3E,KAAK,CAAC,IAAI,EAAE2B,IAAI,CAAC;MAC/C,CAAC;IACL,CAAC;EACL;EACA,SAASiD,qBAAqBA,CAACH,cAAc,EAAE;IAC3C,OAAO,UAAU,GAAGC,SAAS,EAAE;MAC3B,OAAO,UAAU,GAAG/C,IAAI,EAAE;QACtBA,IAAI,CAAC,CAAC,CAAC,GAAGb,cAAc,CAACa,IAAI,CAAC,CAAC,CAAC,CAAC;QACjC,OAAO8C,cAAc,CAACzE,KAAK,CAAC,IAAI,EAAE0E,SAAS,CAAC,CAAC1E,KAAK,CAAC,IAAI,EAAE2B,IAAI,CAAC;MAClE,CAAC;IACL,CAAC;EACL;EACA;AACJ;AACA;AACA;EACI,SAASf,kBAAkBA,CAACyB,YAAY,EAAE;IACtC,OAAO,UAAU,GAAGV,IAAI,EAAE;MACtB,OAAOW,QAAQ,CAACC,GAAG,CAACF,YAAY,EAAE,IAAI,EAAEV,IAAI,CAAC;IACjD,CAAC;EACL;EACA;AACJ;AACA;AACA;AACA;EACI,SAASb,cAAcA,CAAC2B,QAAQ,EAAEoC,UAAU,GAAG,KAAK,EAAE;IAClD,IAAI,OAAOpC,QAAQ,KAAK,UAAU,EAAE;MAChC,OAAOA,QAAQ;IACnB;IACA,MAAMqC,WAAW,GAAG,SAAAA,CAAA,EAAY;MAC5B,IAAI/L,IAAI,CAACsF,GAAG,CAACW,MAAM,CAAC,qBAAqB,CAAC,CAAC,KAAK,IAAI,IAAIyD,QAAQ,IAC5D,CAACA,QAAQ,CAACsC,WAAW,EAAE;QACvB;QACA,MAAMhC,eAAe,GAAGhK,IAAI,CAACA,IAAI,CAACK,UAAU,CAAC,eAAe,CAAC,CAAC;QAC9D,IAAI2J,eAAe,IAAI,OAAOA,eAAe,CAACC,SAAS,KAAK,UAAU,EAAE;UACpEP,QAAQ,GAAGM,eAAe,CAACC,SAAS,CAACP,QAAQ,CAAC;QAClD;MACJ;MACAuB,aAAa,CAACa,UAAU,GAAGA,UAAU;MACrC,OAAON,SAAS,CAAChC,GAAG,CAACE,QAAQ,EAAE,IAAI,EAAExC,SAAS,CAAC;IACnD,CAAC;IACD;IACA;IACAnE,MAAM,CAACkG,cAAc,CAAC8C,WAAW,EAAE,QAAQ,EAAE;MAAE3C,YAAY,EAAE,IAAI;MAAE6C,QAAQ,EAAE,IAAI;MAAE5C,UAAU,EAAE;IAAM,CAAC,CAAC;IACvG0C,WAAW,CAAC/M,MAAM,GAAG0K,QAAQ,CAAC1K,MAAM;IACpC,OAAO+M,WAAW;EACtB;EACA,CAAC,UAAU,EAAE,WAAW,EAAE,WAAW,CAAC,CAAClJ,OAAO,CAAC2E,UAAU,IAAI;IACzD,IAAIkE,cAAc,GAAGJ,OAAO,CAAC9D,UAAU,CAAC;IACxC,IAAI8D,OAAO,CAACtL,IAAI,CAACK,UAAU,CAACmH,UAAU,CAAC,CAAC,EAAE;MACtC;IACJ;IACA8D,OAAO,CAACtL,IAAI,CAACK,UAAU,CAACmH,UAAU,CAAC,CAAC,GAAGkE,cAAc;IACrDJ,OAAO,CAAC9D,UAAU,CAAC,GAAG,UAAU,GAAGoB,IAAI,EAAE;MACrCA,IAAI,CAAC,CAAC,CAAC,GAAGf,kBAAkB,CAACe,IAAI,CAAC,CAAC,CAAC,CAAC;MACrC,OAAO8C,cAAc,CAACzE,KAAK,CAAC,IAAI,EAAE2B,IAAI,CAAC;IAC3C,CAAC;IACD0C,OAAO,CAAC9D,UAAU,CAAC,CAAC0E,IAAI,GAAGT,yBAAyB,CAACC,cAAc,CAACQ,IAAI,CAAC;EAC7E,CAAC,CAAC;EACFZ,OAAO,CAACa,QAAQ,CAACC,IAAI,GAAGd,OAAO,CAACe,SAAS;EACzCf,OAAO,CAACa,QAAQ,CAACG,IAAI,GAAGhB,OAAO,CAACiB,SAAS;EACzC,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC1J,OAAO,CAAC2E,UAAU,IAAI;IACxD,IAAIkE,cAAc,GAAGJ,OAAO,CAAC9D,UAAU,CAAC;IACxC,IAAI8D,OAAO,CAACtL,IAAI,CAACK,UAAU,CAACmH,UAAU,CAAC,CAAC,EAAE;MACtC;IACJ;IACA8D,OAAO,CAACtL,IAAI,CAACK,UAAU,CAACmH,UAAU,CAAC,CAAC,GAAGkE,cAAc;IACrDJ,OAAO,CAAC9D,UAAU,CAAC,GAAG,UAAU,GAAGoB,IAAI,EAAE;MACrCA,IAAI,CAAC,CAAC,CAAC,GAAGb,cAAc,CAACa,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC;MACvC,OAAO8C,cAAc,CAACzE,KAAK,CAAC,IAAI,EAAE2B,IAAI,CAAC;IAC3C,CAAC;IACD0C,OAAO,CAAC9D,UAAU,CAAC,CAAC0E,IAAI,GAAGL,qBAAqB,CAACH,cAAc,CAACQ,IAAI,CAAC;IACrEZ,OAAO,CAAC9D,UAAU,CAAC,CAACgF,IAAI,GAAGd,cAAc,CAACc,IAAI;EAClD,CAAC,CAAC;EACFlB,OAAO,CAACmB,EAAE,CAACL,IAAI,GAAGd,OAAO,CAACoB,GAAG;EAC7BpB,OAAO,CAACmB,EAAE,CAACH,IAAI,GAAGhB,OAAO,CAACqB,GAAG;EAC7BrB,OAAO,CAACsB,IAAI,CAACR,IAAI,GAAGd,OAAO,CAACoB,GAAG;EAC/BpB,OAAO,CAACsB,IAAI,CAACN,IAAI,GAAGhB,OAAO,CAACqB,GAAG;EAC/B,CAAC,YAAY,EAAE,WAAW,EAAE,WAAW,EAAE,UAAU,CAAC,CAAC9J,OAAO,CAAC2E,UAAU,IAAI;IACvE,IAAIkE,cAAc,GAAGJ,OAAO,CAAC9D,UAAU,CAAC;IACxC,IAAI8D,OAAO,CAACtL,IAAI,CAACK,UAAU,CAACmH,UAAU,CAAC,CAAC,EAAE;MACtC;IACJ;IACA8D,OAAO,CAACtL,IAAI,CAACK,UAAU,CAACmH,UAAU,CAAC,CAAC,GAAGkE,cAAc;IACrDJ,OAAO,CAAC9D,UAAU,CAAC,GAAG,UAAU,GAAGoB,IAAI,EAAE;MACrCA,IAAI,CAAC,CAAC,CAAC,GAAGb,cAAc,CAACa,IAAI,CAAC,CAAC,CAAC,CAAC;MACjC,OAAO8C,cAAc,CAACzE,KAAK,CAAC,IAAI,EAAE2B,IAAI,CAAC;IAC3C,CAAC;EACL,CAAC,CAAC;EACF5I,IAAI,CAAC6M,eAAe,GAAG,SAASA,eAAeA,CAACC,KAAK,EAAEC,QAAQ,GAAG,KAAK,EAAE;IACrE;IACA,SAASC,mBAAmBA,CAAA,EAAG;MAC3B,MAAM5E,iBAAiB,GAAGpI,IAAI,CAACgC,OAAO,CAACD,GAAG,CAAC,uBAAuB,CAAC;MACnE,OAAO,CAAC,CAACqG,iBAAiB;IAC9B;IACA;IACA;IACA,SAAS6E,YAAYA,CAAA,EAAG;MACpB,MAAMhC,aAAa,GAAGjL,IAAI,CAACgC,OAAO,CAACD,GAAG,CAAC,eAAe,CAAC;MACvD,OAAOkJ,aAAa,IAAIA,aAAa,CAACa,UAAU;IACpD;IACA,IAAIgB,KAAK,CAACxH,GAAG,CAACW,MAAM,CAAC,YAAY,CAAC,CAAC,EAAE;MACjC;IACJ;IACA6G,KAAK,CAACxH,GAAG,CAACW,MAAM,CAAC,YAAY,CAAC,CAAC,GAAG,IAAI;IACtC;IACAX,GAAG,CAAC4H,WAAW,CAACJ,KAAK,EAAE,kBAAkB,EAAE1I,QAAQ,IAAI;MACnD,OAAO,UAAU+I,IAAI,EAAEvE,IAAI,EAAE;QACzB,IAAIoE,mBAAmB,CAAC,CAAC,EAAE;UACvB,OAAO,IAAI;QACf,CAAC,MACI;UACD,OAAO5I,QAAQ,CAAC6C,KAAK,CAACkG,IAAI,EAAEvE,IAAI,CAAC;QACrC;MACJ,CAAC;IACL,CAAC,CAAC;IACF;IACAtD,GAAG,CAAC4H,WAAW,CAACJ,KAAK,EAAE,eAAe,EAAE1I,QAAQ,IAAI;MAChD,OAAO,UAAU+I,IAAI,EAAEvE,IAAI,EAAE;QACzB5I,IAAI,CAACsF,GAAG,CAACW,MAAM,CAAC,qBAAqB,CAAC,CAAC,GAAG,IAAI;QAC9C,IAAI8G,QAAQ,IAAIE,YAAY,CAAC,CAAC,EAAE;UAC5B,OAAO7I,QAAQ,CAAC6C,KAAK,CAACkG,IAAI,EAAEvE,IAAI,CAAC;QACrC;QACA,OAAOuE,IAAI;MACf,CAAC;IACL,CAAC,CAAC;IACF;IACA7H,GAAG,CAAC4H,WAAW,CAACJ,KAAK,EAAE,eAAe,EAAE1I,QAAQ,IAAI;MAChD,OAAO,UAAU+I,IAAI,EAAEvE,IAAI,EAAE;QACzB5I,IAAI,CAACsF,GAAG,CAACW,MAAM,CAAC,qBAAqB,CAAC,CAAC,GAAG,KAAK;QAC/C,IAAI8G,QAAQ,IAAIE,YAAY,CAAC,CAAC,EAAE;UAC5B,OAAO7I,QAAQ,CAAC6C,KAAK,CAACkG,IAAI,EAAEvE,IAAI,CAAC;QACrC;QACA,OAAOuE,IAAI;MACf,CAAC;IACL,CAAC,CAAC;IACF;IACA7H,GAAG,CAAC4H,WAAW,CAACJ,KAAK,EAAE,eAAe,EAAE1I,QAAQ,IAAI;MAChD,OAAO,UAAU+I,IAAI,EAAEvE,IAAI,EAAE;QACzB,MAAMR,iBAAiB,GAAGpI,IAAI,CAACgC,OAAO,CAACD,GAAG,CAAC,uBAAuB,CAAC;QACnE,IAAIqG,iBAAiB,IAAI4E,mBAAmB,CAAC,CAAC,EAAE;UAC5C5E,iBAAiB,CAACI,qBAAqB,CAACI,IAAI,CAAC,CAAC,CAAC,CAAC;QACpD,CAAC,MACI;UACD,OAAOxE,QAAQ,CAAC6C,KAAK,CAACkG,IAAI,EAAEvE,IAAI,CAAC;QACrC;MACJ,CAAC;IACL,CAAC,CAAC;IACF;IACAtD,GAAG,CAAC4H,WAAW,CAACJ,KAAK,EAAE,mBAAmB,EAAE1I,QAAQ,IAAI;MACpD,OAAO,UAAU+I,IAAI,EAAEvE,IAAI,EAAE;QACzB,MAAMR,iBAAiB,GAAGpI,IAAI,CAACgC,OAAO,CAACD,GAAG,CAAC,uBAAuB,CAAC;QACnE,IAAIqG,iBAAiB,IAAI4E,mBAAmB,CAAC,CAAC,EAAE;UAC5C,OAAO5E,iBAAiB,CAACgF,iBAAiB,CAAC,CAAC;QAChD,CAAC,MACI;UACD,OAAOhJ,QAAQ,CAAC6C,KAAK,CAACkG,IAAI,EAAEvE,IAAI,CAAC;QACrC;MACJ,CAAC;IACL,CAAC,CAAC;IACF;IACAtD,GAAG,CAAC4H,WAAW,CAACJ,KAAK,EAAE,aAAa,EAAE1I,QAAQ,IAAI;MAC9C,OAAO,UAAU+I,IAAI,EAAEvE,IAAI,EAAE;QACzB,MAAMR,iBAAiB,GAAGpI,IAAI,CAACgC,OAAO,CAACD,GAAG,CAAC,uBAAuB,CAAC;QACnE,IAAIqG,iBAAiB,EAAE;UACnBA,iBAAiB,CAACiF,eAAe,CAAC,CAAC;QACvC,CAAC,MACI;UACD,OAAOjJ,QAAQ,CAAC6C,KAAK,CAACkG,IAAI,EAAEvE,IAAI,CAAC;QACrC;MACJ,CAAC;IACL,CAAC,CAAC;IACF;IACAtD,GAAG,CAAC4H,WAAW,CAACJ,KAAK,EAAE,cAAc,EAAE1I,QAAQ,IAAI;MAC/C,OAAO,UAAU+I,IAAI,EAAEvE,IAAI,EAAE;QACzB,MAAMR,iBAAiB,GAAGpI,IAAI,CAACgC,OAAO,CAACD,GAAG,CAAC,uBAAuB,CAAC;QACnE,IAAIqG,iBAAiB,EAAE;UACnBA,iBAAiB,CAACkF,KAAK,CAAC,GAAG,EAAE,IAAI,CAAC;QACtC,CAAC,MACI;UACD,OAAOlJ,QAAQ,CAAC6C,KAAK,CAACkG,IAAI,EAAEvE,IAAI,CAAC;QACrC;MACJ,CAAC;IACL,CAAC,CAAC;IACF;IACAtD,GAAG,CAAC4H,WAAW,CAACJ,KAAK,EAAE,qBAAqB,EAAE1I,QAAQ,IAAI;MACtD,OAAO,UAAU+I,IAAI,EAAEvE,IAAI,EAAE;QACzB,MAAMR,iBAAiB,GAAGpI,IAAI,CAACgC,OAAO,CAACD,GAAG,CAAC,uBAAuB,CAAC;QACnE,IAAIqG,iBAAiB,EAAE;UACnBA,iBAAiB,CAACD,IAAI,CAACS,IAAI,CAAC,CAAC,CAAC,CAAC;QACnC,CAAC,MACI;UACD,OAAOxE,QAAQ,CAAC6C,KAAK,CAACkG,IAAI,EAAEvE,IAAI,CAAC;QACrC;MACJ,CAAC;IACL,CAAC,CAAC;IACF;IACAtD,GAAG,CAAC4H,WAAW,CAACJ,KAAK,EAAE,sBAAsB,EAAE1I,QAAQ,IAAI;MACvD,OAAO,UAAU+I,IAAI,EAAEvE,IAAI,EAAE;QACzB,MAAMR,iBAAiB,GAAGpI,IAAI,CAACgC,OAAO,CAACD,GAAG,CAAC,uBAAuB,CAAC;QACnE,IAAIqG,iBAAiB,EAAE;UACnBA,iBAAiB,CAACmF,sBAAsB,CAAC,CAAC;QAC9C,CAAC,MACI;UACD,OAAOnJ,QAAQ,CAAC6C,KAAK,CAACkG,IAAI,EAAEvE,IAAI,CAAC;QACrC;MACJ,CAAC;IACL,CAAC,CAAC;IACF;IACAtD,GAAG,CAAC4H,WAAW,CAACJ,KAAK,EAAE,0BAA0B,EAAE1I,QAAQ,IAAI;MAC3D,OAAO,UAAU+I,IAAI,EAAEvE,IAAI,EAAE;QACzB,MAAMR,iBAAiB,GAAGpI,IAAI,CAACgC,OAAO,CAACD,GAAG,CAAC,uBAAuB,CAAC;QACnE,IAAIqG,iBAAiB,EAAE;UACnBA,iBAAiB,CAACoF,UAAU,CAAC5E,IAAI,CAAC,CAAC,CAAC,CAAC;QACzC,CAAC,MACI;UACD,OAAOxE,QAAQ,CAAC6C,KAAK,CAACkG,IAAI,EAAEvE,IAAI,CAAC;QACrC;MACJ,CAAC;IACL,CAAC,CAAC;IACF;IACAtD,GAAG,CAAC4H,WAAW,CAACJ,KAAK,EAAE,gBAAgB,EAAE1I,QAAQ,IAAI;MACjD,OAAO,UAAU+I,IAAI,EAAEvE,IAAI,EAAE;QACzB,MAAMR,iBAAiB,GAAGpI,IAAI,CAACgC,OAAO,CAACD,GAAG,CAAC,uBAAuB,CAAC;QACnE,IAAIqG,iBAAiB,EAAE;UACnBA,iBAAiB,CAACqF,eAAe,CAAC,CAAC;QACvC,CAAC,MACI;UACD,OAAOrJ,QAAQ,CAAC6C,KAAK,CAACkG,IAAI,EAAEvE,IAAI,CAAC;QACrC;MACJ,CAAC;IACL,CAAC,CAAC;IACF;IACAtD,GAAG,CAAC4H,WAAW,CAACJ,KAAK,EAAE,eAAe,EAAE1I,QAAQ,IAAI;MAChD,OAAO,UAAU+I,IAAI,EAAEvE,IAAI,EAAE;QACzB,MAAMR,iBAAiB,GAAGpI,IAAI,CAACgC,OAAO,CAACD,GAAG,CAAC,uBAAuB,CAAC;QACnE,IAAIqG,iBAAiB,EAAE;UACnB,OAAOA,iBAAiB,CAACsF,aAAa,CAAC,CAAC;QAC5C,CAAC,MACI;UACD,OAAOtJ,QAAQ,CAAC6C,KAAK,CAACkG,IAAI,EAAEvE,IAAI,CAAC;QACrC;MACJ,CAAC;IACL,CAAC,CAAC;EACN,CAAC;AACL,CAAC,CAAC;AAEF5I,IAAI,CAACoF,YAAY,CAAC,OAAO,EAAE,CAACC,MAAM,EAAErF,IAAI,KAAK;EACzC,MAAM2N,KAAK,GAAGtI,MAAM,CAACsI,KAAK;EAC1B,IAAI,OAAOA,KAAK,KAAK,WAAW,EAAE;IAC9B;IACA;IACA;EACJ;EACA,IAAI,OAAO3N,IAAI,KAAK,WAAW,EAAE;IAC7B,MAAM,IAAI3B,KAAK,CAAC,iBAAiB,CAAC;EACtC;EACA,MAAMyD,aAAa,GAAG9B,IAAI,CAAC,eAAe,CAAC;EAC3C,MAAMiF,gBAAgB,GAAGjF,IAAI,CAAC,kBAAkB,CAAC;EACjD,IAAI,CAAC8B,aAAa,EAAE;IAChB,MAAM,IAAIzD,KAAK,CAAC,uBAAuB,CAAC;EAC5C;EACA,IAAIsP,KAAK,CAAC,gBAAgB,CAAC,EAAE;IACzB,MAAM,IAAItP,KAAK,CAAC,+CAA+C,CAAC;EACpE;EACAsP,KAAK,CAAC,gBAAgB,CAAC,GAAG,IAAI;EAC9B,MAAMpC,QAAQ,GAAGvL,IAAI,CAACgC,OAAO;EAC7B,MAAMuH,QAAQ,GAAGgC,QAAQ,CAACrH,IAAI,CAAC,IAAIe,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;EACtE,IAAI2I,QAAQ,GAAG,IAAI;EACnB,MAAMC,SAAS,GAAGtC,QAAQ,CAACrH,IAAI,CAAC,IAAIpC,aAAa,CAAC,CAAC,CAAC;EACpD,MAAMgM,aAAa,GAAG;IAClBC,KAAK,EAAE1I,MAAM,CAAC0I,KAAK;IACnBC,SAAS,EAAE3I,MAAM,CAAC2I,SAAS;IAC3BC,MAAM,EAAE5I,MAAM,CAAC4I,MAAM;IACrBC,UAAU,EAAE7I,MAAM,CAAC6I,UAAU;IAC7B/B,QAAQ,EAAE9G,MAAM,CAAC8G,QAAQ;IACzBM,EAAE,EAAEpH,MAAM,CAACoH;EACf,CAAC;EACD,SAAS0B,eAAeA,CAACvF,IAAI,EAAEwF,QAAQ,EAAEC,SAAS,EAAE;IAChD,KAAK,IAAItP,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG6J,IAAI,CAAC5J,MAAM,EAAED,CAAC,EAAE,EAAE;MAClC,IAAIuP,GAAG,GAAG1F,IAAI,CAAC7J,CAAC,CAAC;MACjB,IAAI,OAAOuP,GAAG,KAAK,UAAU,EAAE;QAC3B;QACA;QACA;QACA;QACA;QACA1F,IAAI,CAAC7J,CAAC,CAAC,GAAIuP,GAAG,CAACtP,MAAM,KAAK,CAAC,GAAIoP,QAAQ,CAACE,GAAG,CAAC,GAAGD,SAAS,CAACC,GAAG,CAAC;QAC7D;QACA;QACA1F,IAAI,CAAC7J,CAAC,CAAC,CAACwP,QAAQ,GAAG,YAAY;UAC3B,OAAOD,GAAG,CAACC,QAAQ,CAAC,CAAC;QACzB,CAAC;MACL;IACJ;IACA,OAAO3F,IAAI;EACf;EACA,SAASf,kBAAkBA,CAACe,IAAI,EAAE;IAC9B,MAAMwF,QAAQ,GAAG,SAAAA,CAAU7D,EAAE,EAAE;MAC3B,OAAO,YAAY;QACf,OAAOhB,QAAQ,CAACC,GAAG,CAACe,EAAE,EAAE,IAAI,EAAErD,SAAS,CAAC;MAC5C,CAAC;IACL,CAAC;IACD,OAAOiH,eAAe,CAACvF,IAAI,EAAEwF,QAAQ,CAAC;EAC1C;EACA,SAASrG,cAAcA,CAACa,IAAI,EAAE;IAC1B,MAAMyF,SAAS,GAAG,SAAAA,CAAU9D,EAAE,EAAE;MAC5B,OAAO,UAAUX,IAAI,EAAE;QACnB,OAAOgE,QAAQ,CAACpE,GAAG,CAACe,EAAE,EAAE,IAAI,EAAE,CAACX,IAAI,CAAC,CAAC;MACzC,CAAC;IACL,CAAC;IACD,MAAMwE,QAAQ,GAAG,SAAAA,CAAU7D,EAAE,EAAE;MAC3B,OAAO,YAAY;QACf,OAAOqD,QAAQ,CAACpE,GAAG,CAACe,EAAE,EAAE,IAAI,CAAC;MACjC,CAAC;IACL,CAAC;IACD,OAAO4D,eAAe,CAACvF,IAAI,EAAEwF,QAAQ,EAAEC,SAAS,CAAC;EACrD;EACA,SAASG,eAAeA,CAAC5F,IAAI,EAAE;IAC3B,MAAMyF,SAAS,GAAG,SAAAA,CAAU9D,EAAE,EAAE;MAC5B,OAAO,UAAUX,IAAI,EAAE;QACnB,OAAOiE,SAAS,CAACrE,GAAG,CAACe,EAAE,EAAE,IAAI,EAAE,CAACX,IAAI,CAAC,CAAC;MAC1C,CAAC;IACL,CAAC;IACD,MAAMwE,QAAQ,GAAG,SAAAA,CAAU7D,EAAE,EAAE;MAC3B,OAAO,YAAY;QACf,OAAOsD,SAAS,CAACrE,GAAG,CAACe,EAAE,EAAE,IAAI,CAAC;MAClC,CAAC;IACL,CAAC;IACD,OAAO4D,eAAe,CAACvF,IAAI,EAAEwF,QAAQ,EAAEC,SAAS,CAAC;EACrD;EACAhJ,MAAM,CAAC8G,QAAQ,GAAG9G,MAAM,CAACoJ,KAAK,GAAG,YAAY;IACzC,OAAOX,aAAa,CAAC3B,QAAQ,CAAClF,KAAK,CAAC,IAAI,EAAEY,kBAAkB,CAACX,SAAS,CAAC,CAAC;EAC5E,CAAC;EACD7B,MAAM,CAACkH,SAAS,GAAGlH,MAAM,CAACoJ,KAAK,CAACnC,IAAI,GAAGjH,MAAM,CAAC8G,QAAQ,CAACG,IAAI,GAAG,YAAY;IACtE,OAAOwB,aAAa,CAAC3B,QAAQ,CAACG,IAAI,CAACrF,KAAK,CAAC,IAAI,EAAEY,kBAAkB,CAACX,SAAS,CAAC,CAAC;EACjF,CAAC;EACD7B,MAAM,CAAC8G,QAAQ,CAACC,IAAI,GAAG/G,MAAM,CAACoJ,KAAK,CAACrC,IAAI,GAAG,YAAY;IACnD,OAAO0B,aAAa,CAAC3B,QAAQ,CAACC,IAAI,CAACnF,KAAK,CAAC,IAAI,EAAEY,kBAAkB,CAACX,SAAS,CAAC,CAAC;EACjF,CAAC;EACD7B,MAAM,CAACoH,EAAE,GAAGpH,MAAM,CAACqJ,OAAO,GAAGrJ,MAAM,CAACuH,IAAI,GAAG,YAAY;IACnD,OAAOkB,aAAa,CAACrB,EAAE,CAACxF,KAAK,CAAC,IAAI,EAAEc,cAAc,CAACb,SAAS,CAAC,CAAC;EAClE,CAAC;EACD7B,MAAM,CAACsH,GAAG,GAAGtH,MAAM,CAACsJ,QAAQ,GAAGtJ,MAAM,CAACoH,EAAE,CAACH,IAAI,GAAG,YAAY;IACxD,OAAOwB,aAAa,CAACrB,EAAE,CAACH,IAAI,CAACrF,KAAK,CAAC,IAAI,EAAEc,cAAc,CAACb,SAAS,CAAC,CAAC;EACvE,CAAC;EACD7B,MAAM,CAACoH,EAAE,CAACL,IAAI,GAAG/G,MAAM,CAACuH,IAAI,CAACR,IAAI,GAAG,YAAY;IAC5C,OAAO0B,aAAa,CAACrB,EAAE,CAACL,IAAI,CAACnF,KAAK,CAAC,IAAI,EAAEc,cAAc,CAACb,SAAS,CAAC,CAAC;EACvE,CAAC;EACD7B,MAAM,CAAC0I,KAAK,GAAG1I,MAAM,CAACuJ,aAAa,GAAG,YAAY;IAC9C,OAAOd,aAAa,CAACC,KAAK,CAAC9G,KAAK,CAAC,IAAI,EAAEuH,eAAe,CAACtH,SAAS,CAAC,CAAC;EACtE,CAAC;EACD7B,MAAM,CAAC2I,SAAS,GAAG3I,MAAM,CAACwJ,QAAQ,GAAG,YAAY;IAC7C,OAAOf,aAAa,CAACE,SAAS,CAAC/G,KAAK,CAAC,IAAI,EAAEc,cAAc,CAACb,SAAS,CAAC,CAAC;EACzE,CAAC;EACD7B,MAAM,CAAC4I,MAAM,GAAG5I,MAAM,CAACyJ,UAAU,GAAG,YAAY;IAC5C,OAAOhB,aAAa,CAACG,MAAM,CAAChH,KAAK,CAAC,IAAI,EAAEuH,eAAe,CAACtH,SAAS,CAAC,CAAC;EACvE,CAAC;EACD7B,MAAM,CAAC6I,UAAU,GAAG7I,MAAM,CAAC0J,KAAK,GAAG,YAAY;IAC3C,OAAOjB,aAAa,CAACI,UAAU,CAACjH,KAAK,CAAC,IAAI,EAAEc,cAAc,CAACb,SAAS,CAAC,CAAC;EAC1E,CAAC;EACD,CAAC,CAAC8H,eAAe,EAAEC,WAAW,KAAK;IAC/BtB,KAAK,CAACuB,MAAM,CAACtJ,SAAS,CAACuJ,OAAO,GAAG,UAAU5E,EAAE,EAAE;MAC3CvK,IAAI,CAACgC,OAAO,CAACwI,iBAAiB,CAAC,iBAAiB,EAAE,MAAM;QACpDwE,eAAe,CAACpH,IAAI,CAAC,IAAI,EAAE2C,EAAE,CAAC;MAClC,CAAC,CAAC;IACN,CAAC;IACDoD,KAAK,CAACuB,MAAM,CAACtJ,SAAS,CAAC4D,GAAG,GAAG,UAAUe,EAAE,EAAE;MACvC,IAAI,CAAC3D,EAAE,CAAC,MAAM,EAAGwI,CAAC,IAAK;QACnBxB,QAAQ,GAAGrC,QAAQ,CAACrH,IAAI,CAAC,IAAIpC,aAAa,CAAC,CAAC,CAAC;MACjD,CAAC,CAAC;MACF,IAAI,CAAC8E,EAAE,CAAC,MAAM,EAAE,CAACgG,IAAI,EAAErO,GAAG,KAAK;QAC3B,MAAM0M,aAAa,GAAG2C,QAAQ,IAAIA,QAAQ,CAAC7L,GAAG,CAAC,eAAe,CAAC;QAC/D,IAAIkJ,aAAa,IAAI1M,GAAG,EAAE;UACtB,IAAI;YACA;YACAA,GAAG,CAACyM,OAAO,IAAIC,aAAa,CAACvH,2BAA2B,CAAC,CAAC;UAC9D,CAAC,CACD,OAAO1F,KAAK,EAAE,CACd;QACJ;MACJ,CAAC,CAAC;MACF,OAAOiR,WAAW,CAACrH,IAAI,CAAC,IAAI,EAAE2C,EAAE,CAAC;IACrC,CAAC;EACL,CAAC,EAAEoD,KAAK,CAACuB,MAAM,CAACtJ,SAAS,CAACuJ,OAAO,EAAExB,KAAK,CAACuB,MAAM,CAACtJ,SAAS,CAAC4D,GAAG,CAAC;AAClE,CAAC,CAAC;AAEF,CAAC,UAAU6F,OAAO,EAAE;EAChB,MAAMC,iBAAiB,CAAC;IACpB;MAAS,IAAI,CAACC,sBAAsB,GAAGvP,IAAI,CAACK,UAAU,CAAC,kBAAkB,CAAC;IAAE;IAC5EtC,WAAWA,CAACyR,cAAc,EAAEC,YAAY,EAAEvK,UAAU,EAAE;MAClD,IAAI,CAACsK,cAAc,GAAGA,cAAc;MACpC,IAAI,CAACC,YAAY,GAAGA,YAAY;MAChC,IAAI,CAACC,kBAAkB,GAAG,KAAK;MAC/B,IAAI,CAACC,kBAAkB,GAAG,KAAK;MAC/B,IAAI,CAACC,eAAe,GAAG,KAAK;MAC5B,IAAI,CAACC,OAAO,GAAG,KAAK;MACpB,IAAI,CAACC,oBAAoB,GAAG,IAAI;MAChC,IAAI,CAACC,aAAa,GAAG,IAAI;MACzB,IAAI,CAAC5K,OAAO,GAAGnF,IAAI,CAACgC,OAAO;MAC3B,IAAI,CAACgO,6BAA6B,GAAG,CAAC;MACtC,IAAI,CAACC,mCAAmC,GAAG,KAAK;MAChD,IAAI,CAAChQ,IAAI,GAAG,oBAAoB,GAAGiF,UAAU;MAC7C,IAAI,CAAC7C,UAAU,GAAG;QAAE,mBAAmB,EAAE;MAAK,CAAC;MAC/C,IAAI,CAAC4N,mCAAmC,GACpCZ,OAAO,CAACrP,IAAI,CAACK,UAAU,CAAC,qCAAqC,CAAC,CAAC,KAAK,IAAI;IAChF;IACA6P,iCAAiCA,CAAA,EAAG;MAChC,OAAO,IAAI,CAACF,6BAA6B,GAAG,CAAC;IACjD;IACAG,qBAAqBA,CAAA,EAAG;MACpB;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,IAAI,IAAI,CAACL,oBAAoB,KAAK,IAAI,EAAE;QACpClF,YAAY,CAAC,IAAI,CAACkF,oBAAoB,CAAC;QACvC,IAAI,CAACA,oBAAoB,GAAG,IAAI;MACpC;MACA,IAAI,EAAE,IAAI,CAACJ,kBAAkB,IAAI,IAAI,CAACC,kBAAkB,IACnD,IAAI,CAACM,mCAAmC,IAAI,IAAI,CAACC,iCAAiC,CAAC,CAAE,CAAC,EAAE;QACzF;QACA;QACA,IAAI,CAAC/K,OAAO,CAACqE,GAAG,CAAC,MAAM;UACnB,IAAI,CAACsG,oBAAoB,GAAGnF,UAAU,CAAC,MAAM;YACzC,IAAI,CAAC,IAAI,CAACiF,eAAe,IAAI,EAAE,IAAI,CAACF,kBAAkB,IAAI,IAAI,CAACC,kBAAkB,CAAC,EAAE;cAChF,IAAI,CAACH,cAAc,CAAC,CAAC;YACzB;UACJ,CAAC,EAAE,CAAC,CAAC;QACT,CAAC,CAAC;MACN;IACJ;IACAY,mBAAmBA,CAAA,EAAG;MAClB,IAAI,CAAC,IAAI,CAACH,mCAAmC,EAAE;QAC3C;MACJ;MACA,MAAMG,mBAAmB,GAAGC,OAAO,CAACrQ,IAAI,CAACK,UAAU,CAAC,qBAAqB,CAAC,CAAC;MAC3E,IAAI+P,mBAAmB,EAAE;QACrBA,mBAAmB,CAAC,CAAC;MACzB;IACJ;IACAE,qBAAqBA,CAAA,EAAG;MACpB,IAAI,CAAC,IAAI,CAACL,mCAAmC,EAAE;QAC3C;MACJ;MACA,MAAMK,qBAAqB,GAAGD,OAAO,CAACrQ,IAAI,CAACK,UAAU,CAAC,uBAAuB,CAAC,CAAC;MAC/E,IAAIiQ,qBAAqB,EAAE;QACvBA,qBAAqB,CAAC,CAAC;MAC3B;IACJ;IACAhQ,cAAcA,CAAC8D,QAAQ,EAAEpC,OAAO,EAAE8C,MAAM,EAAEpE,IAAI,EAAE;MAC5C,IAAIA,IAAI,CAACI,IAAI,KAAK,WAAW,EAAE;QAC3B,IAAI,CAAC+O,OAAO,GAAG,KAAK;MACxB;MACA,IAAInP,IAAI,CAACI,IAAI,KAAK,WAAW,IAAIJ,IAAI,CAACE,IAAI,IAAIF,IAAI,CAACE,IAAI,YAAYyP,OAAO,EAAE;QACxE;QACA,IAAI3P,IAAI,CAACE,IAAI,CAAC0O,iBAAiB,CAACC,sBAAsB,CAAC,KAAK,IAAI,EAAE;UAC9D;UACA,IAAI,CAACS,6BAA6B,EAAE;QACxC;MACJ;MACA,OAAO5L,QAAQ,CAACrD,YAAY,CAAC+D,MAAM,EAAEpE,IAAI,CAAC;IAC9C;IACAgE,YAAYA,CAACN,QAAQ,EAAEpC,OAAO,EAAE8C,MAAM,EAAEpE,IAAI,EAAE6D,SAAS,EAAEC,SAAS,EAAE;MAChE,IAAI9D,IAAI,CAACI,IAAI,KAAK,WAAW,EAAE;QAC3B,IAAI,CAAC+O,OAAO,GAAG,KAAK;MACxB;MACA,OAAOzL,QAAQ,CAACO,UAAU,CAACG,MAAM,EAAEpE,IAAI,EAAE6D,SAAS,EAAEC,SAAS,CAAC;IAClE;IACAI,YAAYA,CAACR,QAAQ,EAAEpC,OAAO,EAAE8C,MAAM,EAAEpE,IAAI,EAAE;MAC1C,IAAIA,IAAI,CAACI,IAAI,KAAK,WAAW,EAAE;QAC3B,IAAI,CAAC+O,OAAO,GAAG,KAAK;MACxB;MACA,OAAOzL,QAAQ,CAACS,UAAU,CAACC,MAAM,EAAEpE,IAAI,CAAC;IAC5C;IACA;IACA;IACA;IACA;IACA;IACA4D,QAAQA,CAAC/D,kBAAkB,EAAEC,WAAW,EAAEC,UAAU,EAAE2D,QAAQ,EAAEG,SAAS,EAAEC,SAAS,EAAEV,MAAM,EAAE;MAC1F,IAAI,CAAC,IAAI,CAACiM,aAAa,EAAE;QACrB,IAAI,CAACA,aAAa,GAAG3L,QAAQ;MACjC;MACA,IAAI;QACA,IAAI,CAACyL,OAAO,GAAG,IAAI;QACnB,OAAOtP,kBAAkB,CAACkE,MAAM,CAAChE,UAAU,EAAE2D,QAAQ,EAAEG,SAAS,EAAEC,SAAS,EAAEV,MAAM,CAAC;MACxF,CAAC,SACO;QACJ;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,IAAI,IAAI,CAAC+L,OAAO,IAAI,IAAI,CAACE,aAAa,KAAK3L,QAAQ,EAAE;UACjD,IAAI,CAAC+L,qBAAqB,CAAC,CAAC;QAChC;MACJ;IACJ;IACAnP,aAAaA,CAACT,kBAAkB,EAAEC,WAAW,EAAEC,UAAU,EAAEzC,KAAK,EAAE;MAC9D;MACA,MAAMgJ,MAAM,GAAGzG,kBAAkB,CAACY,WAAW,CAACV,UAAU,EAAEzC,KAAK,CAAC;MAChE,IAAIgJ,MAAM,EAAE;QACR,IAAI,CAACyI,YAAY,CAACzR,KAAK,CAAC;QACxB,IAAI,CAAC4R,eAAe,GAAG,IAAI;MAC/B;MACA,OAAO,KAAK;IAChB;IACArM,SAASA,CAACa,QAAQ,EAAEpC,OAAO,EAAE8C,MAAM,EAAEC,YAAY,EAAE;MAC/CX,QAAQ,CAACY,OAAO,CAACF,MAAM,EAAEC,YAAY,CAAC;MACtC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,IAAI/C,OAAO,KAAK8C,MAAM,EAAE;QACpB;MACJ;MACA,IAAIC,YAAY,CAACwL,MAAM,IAAI,WAAW,EAAE;QACpC,IAAI,CAACb,kBAAkB,GAAG3K,YAAY,CAAC5B,SAAS;QAChD,IAAI,CAACgN,qBAAqB,CAAC,CAAC;MAChC,CAAC,MACI,IAAIpL,YAAY,CAACwL,MAAM,IAAI,WAAW,EAAE;QACzC,IAAI,CAACZ,kBAAkB,GAAG5K,YAAY,CAAC7B,SAAS;QAChD,IAAI,CAACiN,qBAAqB,CAAC,CAAC;MAChC;IACJ;EACJ;EACA;EACA;EACAnQ,IAAI,CAAC,mBAAmB,CAAC,GAAGsP,iBAAiB;AACjD,CAAC,EAAE,OAAOkB,MAAM,KAAK,WAAW,IAAIA,MAAM,IAAI,OAAOrD,IAAI,KAAK,WAAW,IAAIA,IAAI,IAAI9H,MAAM,CAAC;AAC5FrF,IAAI,CAACoF,YAAY,CAAC,WAAW,EAAE,CAACC,MAAM,EAAErF,IAAI,EAAEsF,GAAG,KAAK;EAClD;AACJ;AACA;AACA;EACItF,IAAI,CAACsF,GAAG,CAACW,MAAM,CAAC,WAAW,CAAC,CAAC,GAAG,SAASoI,SAASA,CAAC9D,EAAE,EAAE;IACnD;IACA;IACA,IAAIlF,MAAM,CAACU,OAAO,EAAE;MAChB;MACA,OAAO,UAAU6D,IAAI,EAAE;QACnB,IAAI,CAACA,IAAI,EAAE;UACP;UACA;UACAA,IAAI,GAAG,SAAAA,CAAA,EAAY,CAAE,CAAC;UACtBA,IAAI,CAAC6G,IAAI,GAAG,UAAUrB,CAAC,EAAE;YACrB,MAAMA,CAAC;UACX,CAAC;QACL;QACA3F,aAAa,CAACc,EAAE,EAAE,IAAI,EAAEX,IAAI,EAAGrL,GAAG,IAAK;UACnC,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;YACzB,OAAOqL,IAAI,CAAC6G,IAAI,CAAC,IAAIpS,KAAK,CAACE,GAAG,CAAC,CAAC;UACpC,CAAC,MACI;YACDqL,IAAI,CAAC6G,IAAI,CAAClS,GAAG,CAAC;UAClB;QACJ,CAAC,CAAC;MACN,CAAC;IACL;IACA;IACA;IACA;IACA;IACA,OAAO,YAAY;MACf,OAAO,IAAI8R,OAAO,CAAC,CAACb,cAAc,EAAEC,YAAY,KAAK;QACjDhG,aAAa,CAACc,EAAE,EAAE,IAAI,EAAEiF,cAAc,EAAEC,YAAY,CAAC;MACzD,CAAC,CAAC;IACN,CAAC;EACL,CAAC;EACD,SAAShG,aAAaA,CAACc,EAAE,EAAEe,OAAO,EAAEkE,cAAc,EAAEC,YAAY,EAAE;IAC9D,MAAMjP,WAAW,GAAGR,IAAI,CAACgC,OAAO;IAChC,MAAMsN,iBAAiB,GAAGtP,IAAI,CAAC,mBAAmB,CAAC;IACnD,IAAIsP,iBAAiB,KAAKlP,SAAS,EAAE;MACjC,MAAM,IAAI/B,KAAK,CAAC,kFAAkF,GAC9F,4EAA4E,CAAC;IACrF;IACA,MAAMyD,aAAa,GAAG9B,IAAI,CAAC,eAAe,CAAC;IAC3C,IAAI,CAAC8B,aAAa,EAAE;MAChB,MAAM,IAAIzD,KAAK,CAAC,8EAA8E,GAC1F,uEAAuE,CAAC;IAChF;IACA,MAAM4M,aAAa,GAAGnJ,aAAa,CAACC,GAAG,CAAC,CAAC;IACzCD,aAAa,CAACI,aAAa,CAAC,CAAC;IAC7B;IACA;IACA,MAAMsJ,SAAS,GAAGxL,IAAI,CAACgC,OAAO,CAAC0O,WAAW,CAAC,eAAe,CAAC;IAC3D,MAAMC,gBAAgB,GAAG1F,aAAa,CAAC7H,WAAW,CAAC,CAAC;IACpDoI,SAAS,CAACH,MAAM,CAAC7B,GAAG,CAAC,MAAM;MACvB,MAAMoH,YAAY,GAAG,IAAItB,iBAAiB,CAAC,MAAM;QAC7C;QACA,IAAIrE,aAAa,CAAC7H,WAAW,CAAC,CAAC,IAAIwN,YAAY,EAAE;UAC7C;UACA;UACA;UACA3F,aAAa,CAACvI,WAAW,CAACiO,gBAAgB,CAAC;QAC/C;QACAC,YAAY,CAACN,qBAAqB,CAAC,CAAC;QACpC9P,WAAW,CAACgJ,GAAG,CAAC,MAAM;UAClBgG,cAAc,CAAC,CAAC;QACpB,CAAC,CAAC;MACN,CAAC,EAAGxR,KAAK,IAAK;QACV;QACA,IAAIiN,aAAa,CAAC7H,WAAW,CAAC,CAAC,IAAIwN,YAAY,EAAE;UAC7C;UACA3F,aAAa,CAACvI,WAAW,CAACiO,gBAAgB,CAAC;QAC/C;QACAC,YAAY,CAACN,qBAAqB,CAAC,CAAC;QACpC9P,WAAW,CAACgJ,GAAG,CAAC,MAAM;UAClBiG,YAAY,CAACzR,KAAK,CAAC;QACvB,CAAC,CAAC;MACN,CAAC,EAAE,MAAM,CAAC;MACViN,aAAa,CAACvI,WAAW,CAACkO,YAAY,CAAC;MACvCA,YAAY,CAACR,mBAAmB,CAAC,CAAC;IACtC,CAAC,CAAC;IACF,OAAOpQ,IAAI,CAACgC,OAAO,CAAC6O,UAAU,CAACtG,EAAE,EAAEe,OAAO,CAAC;EAC/C;AACJ,CAAC,CAAC;AAEF,CAAC,UAAUjG,MAAM,EAAE;EACf,MAAMyL,YAAY,GAAGzL,MAAM,CAAClH,IAAI;EAChC;EACA;EACA;EACA;EACA,SAAS4S,QAAQA,CAAA,EAAG;IAChB,IAAI7J,SAAS,CAAClI,MAAM,KAAK,CAAC,EAAE;MACxB,MAAMwG,CAAC,GAAG,IAAIsL,YAAY,CAAC,CAAC;MAC5BtL,CAAC,CAACwL,OAAO,CAACD,QAAQ,CAACE,GAAG,CAAC,CAAC,CAAC;MACzB,OAAOzL,CAAC;IACZ,CAAC,MACI;MACD,MAAMoD,IAAI,GAAGC,KAAK,CAACjD,SAAS,CAACkD,KAAK,CAAClB,IAAI,CAACV,SAAS,CAAC;MAClD,OAAO,IAAI4J,YAAY,CAAC,GAAGlI,IAAI,CAAC;IACpC;EACJ;EACAmI,QAAQ,CAACE,GAAG,GAAG,YAAY;IACvB,MAAMC,qBAAqB,GAAGlR,IAAI,CAACgC,OAAO,CAACD,GAAG,CAAC,uBAAuB,CAAC;IACvE,IAAImP,qBAAqB,EAAE;MACvB,OAAOA,qBAAqB,CAACC,iBAAiB,CAAC,CAAC;IACpD;IACA,OAAOL,YAAY,CAACG,GAAG,CAAChK,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;EAClD,CAAC;EACD6J,QAAQ,CAACK,GAAG,GAAGN,YAAY,CAACM,GAAG;EAC/BL,QAAQ,CAACM,KAAK,GAAGP,YAAY,CAACO,KAAK;EACnC;EACA,MAAMC,MAAM,GAAG;IACX3G,UAAU,EAAEtF,MAAM,CAACsF,UAAU;IAC7B4G,WAAW,EAAElM,MAAM,CAACkM,WAAW;IAC/B3G,YAAY,EAAEvF,MAAM,CAACuF,YAAY;IACjC4G,aAAa,EAAEnM,MAAM,CAACmM;EAC1B,CAAC;EACD,MAAMC,SAAS,CAAC;IACZ;IACA;MAAS,IAAI,CAACC,MAAM,GAAG,CAAC;IAAE;IAC1B3T,WAAWA,CAAA,EAAG;MACV;MACA,IAAI,CAAC4T,eAAe,GAAG,EAAE;MACzB;MACA,IAAI,CAACC,gBAAgB,GAAG,CAAC;MACzB;MACA,IAAI,CAACC,0BAA0B,GAAGf,YAAY,CAACG,GAAG,CAAC,CAAC;MACpD;MACA,IAAI,CAACa,kCAAkC,GAAG,EAAE;IAChD;IACAC,kBAAkBA,CAAA,EAAG;MACjB,OAAO,IAAI,CAACH,gBAAgB;IAChC;IACAT,iBAAiBA,CAAA,EAAG;MAChB,OAAO,IAAI,CAACU,0BAA0B,GAAG,IAAI,CAACD,gBAAgB;IAClE;IACApJ,qBAAqBA,CAACwJ,kBAAkB,EAAE;MACtC,IAAI,CAACH,0BAA0B,GAAGG,kBAAkB;IACxD;IACA5E,iBAAiBA,CAAA,EAAG;MAChB,OAAO0D,YAAY,CAACG,GAAG,CAAC,CAAC;IAC7B;IACAgB,gBAAgBA,CAACC,EAAE,EAAEC,KAAK,EAAEC,OAAO,EAAE;MACjCA,OAAO,GAAG;QACN,GAAG;UACCxJ,IAAI,EAAE,EAAE;UACRyJ,UAAU,EAAE,KAAK;UACjBC,uBAAuB,EAAE,KAAK;UAC9BC,EAAE,EAAE,CAAC,CAAC;UACNC,iBAAiB,EAAE;QACvB,CAAC;QACD,GAAGJ;MACP,CAAC;MACD,IAAIK,SAAS,GAAGL,OAAO,CAACG,EAAE,GAAG,CAAC,GAAGd,SAAS,CAACC,MAAM,EAAE,GAAGU,OAAO,CAACG,EAAE;MAChE,IAAIG,OAAO,GAAG,IAAI,CAACd,gBAAgB,GAAGO,KAAK;MAC3C;MACA,IAAIQ,QAAQ,GAAG;QACXD,OAAO,EAAEA,OAAO;QAChBH,EAAE,EAAEE,SAAS;QACbG,IAAI,EAAEV,EAAE;QACRtJ,IAAI,EAAEwJ,OAAO,CAACxJ,IAAI;QAClBuJ,KAAK,EAAEA,KAAK;QACZE,UAAU,EAAED,OAAO,CAACC,UAAU;QAC9BC,uBAAuB,EAAEF,OAAO,CAACE;MACrC,CAAC;MACD,IAAIF,OAAO,CAACI,iBAAiB,EAAE;QAC3B,IAAI,CAACV,kCAAkC,CAAC3S,IAAI,CAACwT,QAAQ,CAAC;MAC1D;MACA,IAAI5T,CAAC,GAAG,CAAC;MACT,OAAOA,CAAC,GAAG,IAAI,CAAC4S,eAAe,CAAC3S,MAAM,EAAED,CAAC,EAAE,EAAE;QACzC,IAAI8T,YAAY,GAAG,IAAI,CAAClB,eAAe,CAAC5S,CAAC,CAAC;QAC1C,IAAI4T,QAAQ,CAACD,OAAO,GAAGG,YAAY,CAACH,OAAO,EAAE;UACzC;QACJ;MACJ;MACA,IAAI,CAACf,eAAe,CAAClO,MAAM,CAAC1E,CAAC,EAAE,CAAC,EAAE4T,QAAQ,CAAC;MAC3C,OAAOF,SAAS;IACpB;IACAK,6BAA6BA,CAACP,EAAE,EAAE;MAC9B,KAAK,IAAIxT,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAAC4S,eAAe,CAAC3S,MAAM,EAAED,CAAC,EAAE,EAAE;QAClD,IAAI,IAAI,CAAC4S,eAAe,CAAC5S,CAAC,CAAC,CAACwT,EAAE,IAAIA,EAAE,EAAE;UAClC,IAAI,CAACZ,eAAe,CAAClO,MAAM,CAAC1E,CAAC,EAAE,CAAC,CAAC;UACjC;QACJ;MACJ;IACJ;IACAgU,SAASA,CAAA,EAAG;MACR,IAAI,CAACpB,eAAe,GAAG,EAAE;IAC7B;IACAjE,aAAaA,CAAA,EAAG;MACZ,OAAO,IAAI,CAACiE,eAAe,CAAC3S,MAAM;IACtC;IACAwO,UAAUA,CAACwF,IAAI,GAAG,CAAC,EAAEC,MAAM,EAAEC,WAAW,EAAE;MACtC,IAAI,IAAI,CAACvB,eAAe,CAAC3S,MAAM,GAAGgU,IAAI,EAAE;QACpC;MACJ;MACA;MACA;MACA,MAAMG,SAAS,GAAG,IAAI,CAACvB,gBAAgB;MACvC,MAAMwB,UAAU,GAAG,IAAI,CAACzB,eAAe,CAACqB,IAAI,GAAG,CAAC,CAAC;MACjD,IAAI,CAAC7K,IAAI,CAACiL,UAAU,CAACV,OAAO,GAAGS,SAAS,EAAEF,MAAM,EAAEC,WAAW,CAAC;IAClE;IACA/K,IAAIA,CAACkL,MAAM,GAAG,CAAC,EAAEJ,MAAM,EAAEC,WAAW,EAAE;MAClC,IAAII,SAAS,GAAG,IAAI,CAAC1B,gBAAgB,GAAGyB,MAAM;MAC9C,IAAIE,eAAe,GAAG,CAAC;MACvBL,WAAW,GAAGnQ,MAAM,CAACyQ,MAAM,CAAC;QAAEC,iCAAiC,EAAE;MAAK,CAAC,EAAEP,WAAW,CAAC;MACrF;MACA;MACA;MACA,MAAMQ,cAAc,GAAGR,WAAW,CAACO,iCAAiC,GAChE,IAAI,CAAC9B,eAAe,GACpB,IAAI,CAACA,eAAe,CAAC7I,KAAK,CAAC,CAAC;MAChC,IAAI4K,cAAc,CAAC1U,MAAM,KAAK,CAAC,IAAIiU,MAAM,EAAE;QACvCA,MAAM,CAACI,MAAM,CAAC;QACd;MACJ;MACA,OAAOK,cAAc,CAAC1U,MAAM,GAAG,CAAC,EAAE;QAC9B;QACA,IAAI,CAAC8S,kCAAkC,GAAG,EAAE;QAC5C,IAAI9P,OAAO,GAAG0R,cAAc,CAAC,CAAC,CAAC;QAC/B,IAAIJ,SAAS,GAAGtR,OAAO,CAAC0Q,OAAO,EAAE;UAC7B;UACA;QACJ,CAAC,MACI;UACD;UACA,IAAI1Q,OAAO,GAAG0R,cAAc,CAACC,KAAK,CAAC,CAAC;UACpC,IAAI,CAACT,WAAW,CAACO,iCAAiC,EAAE;YAChD,MAAMG,GAAG,GAAG,IAAI,CAACjC,eAAe,CAAChQ,OAAO,CAACK,OAAO,CAAC;YACjD,IAAI4R,GAAG,IAAI,CAAC,EAAE;cACV,IAAI,CAACjC,eAAe,CAAClO,MAAM,CAACmQ,GAAG,EAAE,CAAC,CAAC;YACvC;UACJ;UACAL,eAAe,GAAG,IAAI,CAAC3B,gBAAgB;UACvC,IAAI,CAACA,gBAAgB,GAAG5P,OAAO,CAAC0Q,OAAO;UACvC,IAAIO,MAAM,EAAE;YACRA,MAAM,CAAC,IAAI,CAACrB,gBAAgB,GAAG2B,eAAe,CAAC;UACnD;UACA,IAAIM,MAAM,GAAG7R,OAAO,CAAC4Q,IAAI,CAAC3L,KAAK,CAAC5B,MAAM,EAAErD,OAAO,CAACsQ,uBAAuB,GAAG,CAAC,IAAI,CAACV,gBAAgB,CAAC,GAAG5P,OAAO,CAAC4G,IAAI,CAAC;UACjH,IAAI,CAACiL,MAAM,EAAE;YACT;YACA;UACJ;UACA;UACA;UACA,IAAI,CAACX,WAAW,CAACO,iCAAiC,EAAE;YAChD,IAAI,CAAC3B,kCAAkC,CAACjP,OAAO,CAAC8P,QAAQ,IAAI;cACxD,IAAI5T,CAAC,GAAG,CAAC;cACT,OAAOA,CAAC,GAAG2U,cAAc,CAAC1U,MAAM,EAAED,CAAC,EAAE,EAAE;gBACnC,MAAM8T,YAAY,GAAGa,cAAc,CAAC3U,CAAC,CAAC;gBACtC,IAAI4T,QAAQ,CAACD,OAAO,GAAGG,YAAY,CAACH,OAAO,EAAE;kBACzC;gBACJ;cACJ;cACAgB,cAAc,CAACjQ,MAAM,CAAC1E,CAAC,EAAE,CAAC,EAAE4T,QAAQ,CAAC;YACzC,CAAC,CAAC;UACN;QACJ;MACJ;MACAY,eAAe,GAAG,IAAI,CAAC3B,gBAAgB;MACvC,IAAI,CAACA,gBAAgB,GAAG0B,SAAS;MACjC,IAAIL,MAAM,EAAE;QACRA,MAAM,CAAC,IAAI,CAACrB,gBAAgB,GAAG2B,eAAe,CAAC;MACnD;IACJ;IACAhG,sBAAsBA,CAAC0F,MAAM,EAAE;MAC3B,IAAI,IAAI,CAACtB,eAAe,CAAC3S,MAAM,KAAK,CAAC,EAAE;QACnC,OAAO,CAAC;MACZ;MACA;MACA;MACA,MAAMmU,SAAS,GAAG,IAAI,CAACvB,gBAAgB;MACvC,MAAMkC,QAAQ,GAAG,IAAI,CAACnC,eAAe,CAAC,IAAI,CAACA,eAAe,CAAC3S,MAAM,GAAG,CAAC,CAAC;MACtE,IAAI,CAACmJ,IAAI,CAAC2L,QAAQ,CAACpB,OAAO,GAAGS,SAAS,EAAEF,MAAM,EAAE;QAAEQ,iCAAiC,EAAE;MAAM,CAAC,CAAC;MAC7F,OAAO,IAAI,CAAC7B,gBAAgB,GAAGuB,SAAS;IAC5C;IACA7F,KAAKA,CAACyG,KAAK,GAAG,EAAE,EAAEC,aAAa,GAAG,KAAK,EAAEf,MAAM,EAAE;MAC7C,IAAIe,aAAa,EAAE;QACf,OAAO,IAAI,CAACA,aAAa,CAACf,MAAM,CAAC;MACrC,CAAC,MACI;QACD,OAAO,IAAI,CAACgB,gBAAgB,CAACF,KAAK,EAAEd,MAAM,CAAC;MAC/C;IACJ;IACAe,aAAaA,CAACf,MAAM,EAAE;MAClB,IAAI,IAAI,CAACtB,eAAe,CAAC3S,MAAM,KAAK,CAAC,EAAE;QACnC,OAAO,CAAC;MACZ;MACA;MACA;MACA,MAAMmU,SAAS,GAAG,IAAI,CAACvB,gBAAgB;MACvC,MAAMkC,QAAQ,GAAG,IAAI,CAACnC,eAAe,CAAC,IAAI,CAACA,eAAe,CAAC3S,MAAM,GAAG,CAAC,CAAC;MACtE,IAAI,CAACmJ,IAAI,CAAC2L,QAAQ,CAACpB,OAAO,GAAGS,SAAS,EAAEF,MAAM,CAAC;MAC/C,OAAO,IAAI,CAACrB,gBAAgB,GAAGuB,SAAS;IAC5C;IACAc,gBAAgBA,CAACF,KAAK,EAAEd,MAAM,EAAE;MAC5B,MAAME,SAAS,GAAG,IAAI,CAACvB,gBAAgB;MACvC,IAAI2B,eAAe,GAAG,CAAC;MACvB,IAAIjS,KAAK,GAAG,CAAC;MACb,OAAO,IAAI,CAACqQ,eAAe,CAAC3S,MAAM,GAAG,CAAC,EAAE;QACpCsC,KAAK,EAAE;QACP,IAAIA,KAAK,GAAGyS,KAAK,EAAE;UACf,MAAM,IAAI1V,KAAK,CAAC,2CAA2C,GAAG0V,KAAK,GAC/D,+CAA+C,CAAC;QACxD;QACA;QACA;QACA,IAAI,IAAI,CAACpC,eAAe,CAACuC,MAAM,CAACxT,IAAI,IAAI,CAACA,IAAI,CAAC2R,UAAU,IAAI,CAAC3R,IAAI,CAAC4R,uBAAuB,CAAC,CACrFtT,MAAM,KAAK,CAAC,EAAE;UACf;QACJ;QACA,MAAMgD,OAAO,GAAG,IAAI,CAAC2P,eAAe,CAACgC,KAAK,CAAC,CAAC;QAC5CJ,eAAe,GAAG,IAAI,CAAC3B,gBAAgB;QACvC,IAAI,CAACA,gBAAgB,GAAG5P,OAAO,CAAC0Q,OAAO;QACvC,IAAIO,MAAM,EAAE;UACR;UACAA,MAAM,CAAC,IAAI,CAACrB,gBAAgB,GAAG2B,eAAe,CAAC;QACnD;QACA,MAAMM,MAAM,GAAG7R,OAAO,CAAC4Q,IAAI,CAAC3L,KAAK,CAAC5B,MAAM,EAAErD,OAAO,CAAC4G,IAAI,CAAC;QACvD,IAAI,CAACiL,MAAM,EAAE;UACT;UACA;QACJ;MACJ;MACA,OAAO,IAAI,CAACjC,gBAAgB,GAAGuB,SAAS;IAC5C;EACJ;EACA,MAAM1K,qBAAqB,CAAC;IACxB,OAAO0L,YAAYA,CAAA,EAAG;MAClB,IAAInU,IAAI,CAACgC,OAAO,CAACD,GAAG,CAAC,uBAAuB,CAAC,IAAI,IAAI,EAAE;QACnD,MAAM,IAAI1D,KAAK,CAAC,wEAAwE,CAAC;MAC7F;IACJ;IACAN,WAAWA,CAACmH,UAAU,EAAEkP,iCAAiC,GAAG,KAAK,EAAEC,gBAAgB,EAAE;MACjF,IAAI,CAACD,iCAAiC,GAAGA,iCAAiC;MAC1E,IAAI,CAACC,gBAAgB,GAAGA,gBAAgB;MACxC,IAAI,CAACC,UAAU,GAAG,IAAI7C,SAAS,CAAC,CAAC;MACjC,IAAI,CAAC8C,WAAW,GAAG,EAAE;MACrB,IAAI,CAACC,UAAU,GAAG,IAAI;MACtB,IAAI,CAACC,sBAAsB,GAAGpE,OAAO,CAACrQ,IAAI,CAACK,UAAU,CAAC,uBAAuB,CAAC,CAAC;MAC/E,IAAI,CAACqU,qBAAqB,GAAG,EAAE;MAC/B,IAAI,CAACC,aAAa,GAAG,EAAE;MACvB,IAAI,CAACC,eAAe,GAAG,KAAK;MAC5B,IAAI,CAACvS,UAAU,GAAG;QAAE,uBAAuB,EAAE;MAAK,CAAC;MACnD,IAAI,CAACpC,IAAI,GAAG,wBAAwB,GAAGiF,UAAU;MACjD;MACA;MACA,IAAI,CAAC,IAAI,CAACmP,gBAAgB,EAAE;QACxB,IAAI,CAACA,gBAAgB,GAAGhP,MAAM,CAACrF,IAAI,CAACK,UAAU,CAAC,wBAAwB,CAAC,CAAC;MAC7E;IACJ;IACAwU,WAAWA,CAACtK,EAAE,EAAEuK,UAAU,EAAE;MACxB,OAAO,CAAC,GAAGlM,IAAI,KAAK;QAChB2B,EAAE,CAACtD,KAAK,CAAC5B,MAAM,EAAEuD,IAAI,CAAC;QACtB,IAAI,IAAI,CAAC4L,UAAU,KAAK,IAAI,EAAE;UAAE;UAC5B,IAAIM,UAAU,CAACC,SAAS,IAAI,IAAI,EAAE;YAC9BD,UAAU,CAACC,SAAS,CAAC9N,KAAK,CAAC5B,MAAM,CAAC;UACtC;UACA;UACA,IAAI,CAACgI,eAAe,CAAC,CAAC;QAC1B,CAAC,MACI;UAAE;UACH,IAAIyH,UAAU,CAACE,OAAO,IAAI,IAAI,EAAE;YAC5BF,UAAU,CAACE,OAAO,CAAC/N,KAAK,CAAC5B,MAAM,CAAC;UACpC;QACJ;QACA;QACA,OAAO,IAAI,CAACmP,UAAU,KAAK,IAAI;MACnC,CAAC;IACL;IACA,OAAOS,YAAYA,CAAC3D,MAAM,EAAEiB,EAAE,EAAE;MAC5B,IAAI2C,KAAK,GAAG5D,MAAM,CAAC3P,OAAO,CAAC4Q,EAAE,CAAC;MAC9B,IAAI2C,KAAK,GAAG,CAAC,CAAC,EAAE;QACZ5D,MAAM,CAAC7N,MAAM,CAACyR,KAAK,EAAE,CAAC,CAAC;MAC3B;IACJ;IACAC,aAAaA,CAAC5C,EAAE,EAAE;MACd,OAAO,MAAM;QACT9J,qBAAqB,CAACwM,YAAY,CAAC,IAAI,CAACN,aAAa,EAAEpC,EAAE,CAAC;MAC9D,CAAC;IACL;IACA6C,qBAAqBA,CAAC7K,EAAE,EAAE8K,QAAQ,EAAEzM,IAAI,EAAE2J,EAAE,EAAE;MAC1C,OAAO,MAAM;QACT;QACA,IAAI,IAAI,CAACmC,qBAAqB,CAAC/S,OAAO,CAAC4Q,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE;UAC/C,IAAI,CAAC+B,UAAU,CAACrC,gBAAgB,CAAC1H,EAAE,EAAE8K,QAAQ,EAAE;YAAEzM,IAAI;YAAEyJ,UAAU,EAAE,IAAI;YAAEE,EAAE;YAAEC,iBAAiB,EAAE;UAAK,CAAC,CAAC;QAC3G;MACJ,CAAC;IACL;IACA8C,qBAAqBA,CAAC/C,EAAE,EAAE;MACtB,OAAO,MAAM;QACT9J,qBAAqB,CAACwM,YAAY,CAAC,IAAI,CAACP,qBAAqB,EAAEnC,EAAE,CAAC;MACtE,CAAC;IACL;IACAgD,WAAWA,CAAChL,EAAE,EAAE4H,KAAK,EAAEvJ,IAAI,EAAE4M,OAAO,GAAG,IAAI,EAAE;MACzC,IAAIC,aAAa,GAAG,IAAI,CAACN,aAAa,CAAC1D,SAAS,CAACC,MAAM,CAAC;MACxD;MACA,IAAIQ,EAAE,GAAG,IAAI,CAAC2C,WAAW,CAACtK,EAAE,EAAE;QAAEwK,SAAS,EAAEU,aAAa;QAAET,OAAO,EAAES;MAAc,CAAC,CAAC;MACnF,IAAIlD,EAAE,GAAG,IAAI,CAAC+B,UAAU,CAACrC,gBAAgB,CAACC,EAAE,EAAEC,KAAK,EAAE;QAAEvJ,IAAI;QAAE0J,uBAAuB,EAAE,CAACkD;MAAQ,CAAC,CAAC;MACjG,IAAIA,OAAO,EAAE;QACT,IAAI,CAACb,aAAa,CAACxV,IAAI,CAACoT,EAAE,CAAC;MAC/B;MACA,OAAOA,EAAE;IACb;IACAmD,aAAaA,CAACnD,EAAE,EAAE;MACd9J,qBAAqB,CAACwM,YAAY,CAAC,IAAI,CAACN,aAAa,EAAEpC,EAAE,CAAC;MAC1D,IAAI,CAAC+B,UAAU,CAACxB,6BAA6B,CAACP,EAAE,CAAC;IACrD;IACAoD,YAAYA,CAACpL,EAAE,EAAE8K,QAAQ,EAAEzM,IAAI,EAAE;MAC7B,IAAI2J,EAAE,GAAGd,SAAS,CAACC,MAAM;MACzB,IAAIoD,UAAU,GAAG;QAAEC,SAAS,EAAE,IAAI;QAAEC,OAAO,EAAE,IAAI,CAACM,qBAAqB,CAAC/C,EAAE;MAAE,CAAC;MAC7E,IAAIL,EAAE,GAAG,IAAI,CAAC2C,WAAW,CAACtK,EAAE,EAAEuK,UAAU,CAAC;MACzC;MACAA,UAAU,CAACC,SAAS,GAAG,IAAI,CAACK,qBAAqB,CAAClD,EAAE,EAAEmD,QAAQ,EAAEzM,IAAI,EAAE2J,EAAE,CAAC;MACzE;MACA,IAAI,CAAC+B,UAAU,CAACrC,gBAAgB,CAACC,EAAE,EAAEmD,QAAQ,EAAE;QAAEzM,IAAI;QAAEyJ,UAAU,EAAE;MAAK,CAAC,CAAC;MAC1E,IAAI,CAACqC,qBAAqB,CAACvV,IAAI,CAACoT,EAAE,CAAC;MACnC,OAAOA,EAAE;IACb;IACAqD,cAAcA,CAACrD,EAAE,EAAE;MACf9J,qBAAqB,CAACwM,YAAY,CAAC,IAAI,CAACP,qBAAqB,EAAEnC,EAAE,CAAC;MAClE,IAAI,CAAC+B,UAAU,CAACxB,6BAA6B,CAACP,EAAE,CAAC;IACrD;IACAsD,uBAAuBA,CAAA,EAAG;MACtB,IAAI7X,KAAK,GAAG,IAAI,CAACwW,UAAU,IAAI,IAAI,CAACC,sBAAsB,CAAC,CAAC,CAAC;MAC7D,IAAI,CAACA,sBAAsB,CAACzV,MAAM,GAAG,CAAC;MACtC,IAAI,CAACwV,UAAU,GAAG,IAAI;MACtB,MAAMxW,KAAK;IACf;IACA+T,kBAAkBA,CAAA,EAAG;MACjB,OAAO,IAAI,CAACuC,UAAU,CAACvC,kBAAkB,CAAC,CAAC;IAC/C;IACAZ,iBAAiBA,CAAA,EAAG;MAChB,OAAO,IAAI,CAACmD,UAAU,CAACnD,iBAAiB,CAAC,CAAC;IAC9C;IACA3I,qBAAqBA,CAACsN,QAAQ,EAAE;MAC5B,IAAI,CAACxB,UAAU,CAAC9L,qBAAqB,CAACsN,QAAQ,CAAC;IACnD;IACA1I,iBAAiBA,CAAA,EAAG;MAChB,OAAO,IAAI,CAACkH,UAAU,CAAClH,iBAAiB,CAAC,CAAC;IAC9C;IACA,OAAO2I,SAASA,CAAA,EAAG;MACf,IAAI,CAAC,CAAC1Q,MAAM,CAACrF,IAAI,CAACK,UAAU,CAAC,qBAAqB,CAAC,CAAC,EAAE;QAClD;QACA;QACA;QACA;QACA;QACA;MACJ;MACA,IAAIgF,MAAM,CAAC,MAAM,CAAC,KAAK0L,QAAQ,EAAE;QAC7B;QACA;MACJ;MACA1L,MAAM,CAAC,MAAM,CAAC,GAAG0L,QAAQ;MACzBA,QAAQ,CAACnL,SAAS,GAAGkL,YAAY,CAAClL,SAAS;MAC3C;MACA;MACA;MACA6C,qBAAqB,CAACuN,eAAe,CAAC,CAAC;IAC3C;IACA,OAAOC,SAASA,CAAA,EAAG;MACf,IAAI5Q,MAAM,CAAC,MAAM,CAAC,KAAK0L,QAAQ,EAAE;QAC7B1L,MAAM,CAAC,MAAM,CAAC,GAAGyL,YAAY;MACjC;IACJ;IACA,OAAOkF,eAAeA,CAAA,EAAG;MACrB,IAAI3Q,MAAM,CAACsF,UAAU,KAAK2G,MAAM,CAAC3G,UAAU,EAAE;QACzCtF,MAAM,CAACsF,UAAU,GAAG2G,MAAM,CAAC3G,UAAU;QACrCtF,MAAM,CAACuF,YAAY,GAAG0G,MAAM,CAAC1G,YAAY;MAC7C;MACA,IAAIvF,MAAM,CAACkM,WAAW,KAAKD,MAAM,CAACC,WAAW,EAAE;QAC3ClM,MAAM,CAACkM,WAAW,GAAGD,MAAM,CAACC,WAAW;QACvClM,MAAM,CAACmM,aAAa,GAAGF,MAAM,CAACE,aAAa;MAC/C;IACJ;IACA0E,aAAaA,CAAA,EAAG;MACZ,IAAI,CAACtB,eAAe,GAAG,IAAI;MAC3BnM,qBAAqB,CAACsN,SAAS,CAAC,CAAC;IACrC;IACAI,eAAeA,CAAA,EAAG;MACd,IAAI,CAACvB,eAAe,GAAG,KAAK;MAC5BnM,qBAAqB,CAACwN,SAAS,CAAC,CAAC;IACrC;IACAzI,UAAUA,CAAC4I,KAAK,GAAG,CAAC,EAAEnD,MAAM,EAAEC,WAAW,GAAG;MAAEO,iCAAiC,EAAE;IAAK,CAAC,EAAE;MACrF,IAAI2C,KAAK,IAAI,CAAC,EAAE;QACZ;MACJ;MACA3N,qBAAqB,CAAC0L,YAAY,CAAC,CAAC;MACpC,IAAI,CAAC9G,eAAe,CAAC,CAAC;MACtB,IAAI,CAACiH,UAAU,CAAC9G,UAAU,CAAC4I,KAAK,EAAEnD,MAAM,EAAEC,WAAW,CAAC;MACtD,IAAI,IAAI,CAACsB,UAAU,KAAK,IAAI,EAAE;QAC1B,IAAI,CAACqB,uBAAuB,CAAC,CAAC;MAClC;IACJ;IACA1N,IAAIA,CAACkL,MAAM,GAAG,CAAC,EAAEJ,MAAM,EAAEC,WAAW,GAAG;MAAEO,iCAAiC,EAAE;IAAK,CAAC,EAAE;MAChFhL,qBAAqB,CAAC0L,YAAY,CAAC,CAAC;MACpC,IAAI,CAAC9G,eAAe,CAAC,CAAC;MACtB,IAAI,CAACiH,UAAU,CAACnM,IAAI,CAACkL,MAAM,EAAEJ,MAAM,EAAEC,WAAW,CAAC;MACjD,IAAI,IAAI,CAACsB,UAAU,KAAK,IAAI,EAAE;QAC1B,IAAI,CAACqB,uBAAuB,CAAC,CAAC;MAClC;IACJ;IACAxI,eAAeA,CAAA,EAAG;MACd5E,qBAAqB,CAAC0L,YAAY,CAAC,CAAC;MACpC,MAAMkC,WAAW,GAAGA,CAAA,KAAM;QACtB,IAAI,IAAI,CAAC7B,UAAU,KAAK,IAAI,IAAI,IAAI,CAACC,sBAAsB,CAACzV,MAAM,EAAE;UAChE;UACA,IAAI,CAAC6W,uBAAuB,CAAC,CAAC;QAClC;MACJ,CAAC;MACD,OAAO,IAAI,CAACtB,WAAW,CAACvV,MAAM,GAAG,CAAC,EAAE;QAChC,IAAIsX,SAAS,GAAG,IAAI,CAAC/B,WAAW,CAACZ,KAAK,CAAC,CAAC;QACxC2C,SAAS,CAAC1D,IAAI,CAAC3L,KAAK,CAACqP,SAAS,CAACxR,MAAM,EAAEwR,SAAS,CAAC1N,IAAI,CAAC;MAC1D;MACAyN,WAAW,CAAC,CAAC;IACjB;IACA/I,KAAKA,CAACyG,KAAK,EAAEC,aAAa,EAAEf,MAAM,EAAE;MAChCxK,qBAAqB,CAAC0L,YAAY,CAAC,CAAC;MACpC,IAAI,CAAC9G,eAAe,CAAC,CAAC;MACtB,MAAMkJ,OAAO,GAAG,IAAI,CAACjC,UAAU,CAAChH,KAAK,CAACyG,KAAK,EAAEC,aAAa,EAAEf,MAAM,CAAC;MACnE,IAAI,IAAI,CAACuB,UAAU,KAAK,IAAI,EAAE;QAC1B,IAAI,CAACqB,uBAAuB,CAAC,CAAC;MAClC;MACA,OAAOU,OAAO;IAClB;IACAhJ,sBAAsBA,CAAC0F,MAAM,EAAE;MAC3BxK,qBAAqB,CAAC0L,YAAY,CAAC,CAAC;MACpC,IAAI,CAAC9G,eAAe,CAAC,CAAC;MACtB,MAAMkJ,OAAO,GAAG,IAAI,CAACjC,UAAU,CAAC/G,sBAAsB,CAAC0F,MAAM,CAAC;MAC9D,IAAI,IAAI,CAACuB,UAAU,KAAK,IAAI,EAAE;QAC1B,IAAI,CAACqB,uBAAuB,CAAC,CAAC;MAClC;MACA,OAAOU,OAAO;IAClB;IACA9I,eAAeA,CAAA,EAAG;MACdhF,qBAAqB,CAAC0L,YAAY,CAAC,CAAC;MACpC,IAAI,CAACG,UAAU,CAACvB,SAAS,CAAC,CAAC;MAC3B,IAAI,CAAC2B,qBAAqB,GAAG,EAAE;MAC/B,IAAI,CAACC,aAAa,GAAG,EAAE;IAC3B;IACAjH,aAAaA,CAAA,EAAG;MACZ,OAAO,IAAI,CAAC4G,UAAU,CAAC5G,aAAa,CAAC,CAAC,GAAG,IAAI,CAAC6G,WAAW,CAACvV,MAAM;IACpE;IACAsB,cAAcA,CAAC8D,QAAQ,EAAEpC,OAAO,EAAE8C,MAAM,EAAEpE,IAAI,EAAE;MAC5C,QAAQA,IAAI,CAACI,IAAI;QACb,KAAK,WAAW;UACZ,IAAI8H,IAAI,GAAGlI,IAAI,CAACE,IAAI,IAAIF,IAAI,CAACE,IAAI,CAACgI,IAAI;UACtC;UACA;UACA;UACA,IAAI4N,cAAc;UAClB,IAAI5N,IAAI,EAAE;YACN,IAAI6N,aAAa,GAAG/V,IAAI,CAACE,IAAI,CAAC8V,KAAK;YACnC,IAAI,OAAO9N,IAAI,CAAC5J,MAAM,KAAK,QAAQ,IAAI4J,IAAI,CAAC5J,MAAM,GAAGyX,aAAa,GAAG,CAAC,EAAE;cACpED,cAAc,GAAG3N,KAAK,CAACjD,SAAS,CAACkD,KAAK,CAAClB,IAAI,CAACgB,IAAI,EAAE6N,aAAa,GAAG,CAAC,CAAC;YACxE;UACJ;UACA,IAAI,CAAClC,WAAW,CAACpV,IAAI,CAAC;YAClByT,IAAI,EAAElS,IAAI,CAAC+D,MAAM;YACjBmE,IAAI,EAAE4N,cAAc;YACpB1R,MAAM,EAAEpE,IAAI,CAACE,IAAI,IAAIF,IAAI,CAACE,IAAI,CAACkE;UACnC,CAAC,CAAC;UACF;QACJ,KAAK,WAAW;UACZ,QAAQpE,IAAI,CAACoD,MAAM;YACf,KAAK,YAAY;cACbpD,IAAI,CAACE,IAAI,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC2U,WAAW,CAAC7U,IAAI,CAAC+D,MAAM,EAAE/D,IAAI,CAACE,IAAI,CAAC,OAAO,CAAC,EAAEiI,KAAK,CAACjD,SAAS,CAACkD,KAAK,CAAClB,IAAI,CAAClH,IAAI,CAACE,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;cAC3H;YACJ,KAAK,cAAc;cACfF,IAAI,CAACE,IAAI,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC2U,WAAW,CAAC7U,IAAI,CAAC+D,MAAM,EAAE,CAAC,EAAEoE,KAAK,CAACjD,SAAS,CAACkD,KAAK,CAAClB,IAAI,CAAClH,IAAI,CAACE,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;cAC1G;YACJ,KAAK,aAAa;cACdF,IAAI,CAACE,IAAI,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC+U,YAAY,CAACjV,IAAI,CAAC+D,MAAM,EAAE/D,IAAI,CAACE,IAAI,CAAC,OAAO,CAAC,EAAEiI,KAAK,CAACjD,SAAS,CAACkD,KAAK,CAAClB,IAAI,CAAClH,IAAI,CAACE,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;cAC5H;YACJ,KAAK,qBAAqB;cACtB,MAAM,IAAIvC,KAAK,CAAC,+DAA+D,GAC3EqC,IAAI,CAACE,IAAI,CAAC,KAAK,CAAC,CAAC;YACzB,KAAK,uBAAuB;YAC5B,KAAK,6BAA6B;YAClC,KAAK,0BAA0B;cAC3B;cACA;cACAF,IAAI,CAACE,IAAI,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC2U,WAAW,CAAC7U,IAAI,CAAC+D,MAAM,EAAE,EAAE,EAAE/D,IAAI,CAACE,IAAI,CAAC,MAAM,CAAC,EAAE,IAAI,CAACwT,iCAAiC,CAAC;cACpH;YACJ;cACI;cACA;cACA,MAAMuC,eAAe,GAAG,IAAI,CAACC,mBAAmB,CAAClW,IAAI,CAAC;cACtD,IAAIiW,eAAe,EAAE;gBACjB,MAAM/N,IAAI,GAAGlI,IAAI,CAACE,IAAI,IAAIF,IAAI,CAACE,IAAI,CAAC,MAAM,CAAC;gBAC3C,MAAMuR,KAAK,GAAGvJ,IAAI,IAAIA,IAAI,CAAC5J,MAAM,GAAG,CAAC,GAAG4J,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC;gBACnD,IAAIiO,YAAY,GAAGF,eAAe,CAACE,YAAY,GAAGF,eAAe,CAACE,YAAY,GAAGjO,IAAI;gBACrF,IAAI,CAAC,CAAC+N,eAAe,CAACtE,UAAU,EAAE;kBAC9B;kBACA3R,IAAI,CAACE,IAAI,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC+U,YAAY,CAACjV,IAAI,CAAC+D,MAAM,EAAE0N,KAAK,EAAE0E,YAAY,CAAC;kBAC3EnW,IAAI,CAACE,IAAI,CAACyR,UAAU,GAAG,IAAI;gBAC/B,CAAC,MACI;kBACD;kBACA3R,IAAI,CAACE,IAAI,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC2U,WAAW,CAAC7U,IAAI,CAAC+D,MAAM,EAAE0N,KAAK,EAAE0E,YAAY,CAAC;gBAC9E;gBACA;cACJ;cACA,MAAM,IAAIxY,KAAK,CAAC,kDAAkD,GAAGqC,IAAI,CAACoD,MAAM,CAAC;UACzF;UACA;QACJ,KAAK,WAAW;UACZpD,IAAI,GAAG0D,QAAQ,CAACrD,YAAY,CAAC+D,MAAM,EAAEpE,IAAI,CAAC;UAC1C;MACR;MACA,OAAOA,IAAI;IACf;IACAkE,YAAYA,CAACR,QAAQ,EAAEpC,OAAO,EAAE8C,MAAM,EAAEpE,IAAI,EAAE;MAC1C,QAAQA,IAAI,CAACoD,MAAM;QACf,KAAK,YAAY;QACjB,KAAK,uBAAuB;QAC5B,KAAK,6BAA6B;QAClC,KAAK,0BAA0B;UAC3B,OAAO,IAAI,CAAC4R,aAAa,CAAChV,IAAI,CAACE,IAAI,CAAC,UAAU,CAAC,CAAC;QACpD,KAAK,aAAa;UACd,OAAO,IAAI,CAACgV,cAAc,CAAClV,IAAI,CAACE,IAAI,CAAC,UAAU,CAAC,CAAC;QACrD;UACI;UACA;UACA,MAAM+V,eAAe,GAAG,IAAI,CAACC,mBAAmB,CAAClW,IAAI,CAAC;UACtD,IAAIiW,eAAe,EAAE;YACjB,MAAMG,QAAQ,GAAGpW,IAAI,CAACE,IAAI,CAAC,UAAU,CAAC;YACtC,OAAO+V,eAAe,CAACtE,UAAU,GAAG,IAAI,CAACuD,cAAc,CAACkB,QAAQ,CAAC,GAC7D,IAAI,CAACpB,aAAa,CAACoB,QAAQ,CAAC;UACpC;UACA,OAAO1S,QAAQ,CAACS,UAAU,CAACC,MAAM,EAAEpE,IAAI,CAAC;MAChD;IACJ;IACA4D,QAAQA,CAACF,QAAQ,EAAEpC,OAAO,EAAE8C,MAAM,EAAEiS,QAAQ,EAAExS,SAAS,EAAEC,SAAS,EAAEV,MAAM,EAAE;MACxE,IAAI;QACA2E,qBAAqB,CAACsN,SAAS,CAAC,CAAC;QACjC,OAAO3R,QAAQ,CAACK,MAAM,CAACK,MAAM,EAAEiS,QAAQ,EAAExS,SAAS,EAAEC,SAAS,EAAEV,MAAM,CAAC;MAC1E,CAAC,SACO;QACJ,IAAI,CAAC,IAAI,CAAC8Q,eAAe,EAAE;UACvBnM,qBAAqB,CAACwN,SAAS,CAAC,CAAC;QACrC;MACJ;IACJ;IACAW,mBAAmBA,CAAClW,IAAI,EAAE;MACtB,IAAI,CAAC,IAAI,CAAC2T,gBAAgB,EAAE;QACxB,OAAO,IAAI;MACf;MACA,KAAK,IAAItV,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACsV,gBAAgB,CAACrV,MAAM,EAAED,CAAC,EAAE,EAAE;QACnD,MAAM4X,eAAe,GAAG,IAAI,CAACtC,gBAAgB,CAACtV,CAAC,CAAC;QAChD,IAAI4X,eAAe,CAAC7S,MAAM,KAAKpD,IAAI,CAACoD,MAAM,EAAE;UACxC,OAAO6S,eAAe;QAC1B;MACJ;MACA,OAAO,IAAI;IACf;IACA3V,aAAaA,CAACT,kBAAkB,EAAEC,WAAW,EAAEC,UAAU,EAAEzC,KAAK,EAAE;MAC9D,IAAI,CAACwW,UAAU,GAAGxW,KAAK;MACvB,OAAO,KAAK,CAAC,CAAC;IAClB;EACJ;EACA;EACA;EACAgC,IAAI,CAAC,uBAAuB,CAAC,GAAGyI,qBAAqB;AACzD,CAAC,EAAE,OAAO+H,MAAM,KAAK,QAAQ,IAAIA,MAAM,IAAI,OAAOrD,IAAI,KAAK,QAAQ,IAAIA,IAAI,IAAI9H,MAAM,CAAC;AACtFrF,IAAI,CAACoF,YAAY,CAAC,WAAW,EAAE,CAACC,MAAM,EAAErF,IAAI,EAAEsF,GAAG,KAAK;EAClD,MAAMmD,qBAAqB,GAAGzI,IAAI,IAAIA,IAAI,CAAC,uBAAuB,CAAC;EACnE,SAASgX,gBAAgBA,CAAA,EAAG;IACxB,OAAOhX,IAAI,IAAIA,IAAI,CAAC,eAAe,CAAC;EACxC;EACA,IAAIiX,sBAAsB,GAAG,IAAI;EACjC;AACJ;AACA;AACA;AACA;AACA;EACI,SAASC,kBAAkBA,CAAA,EAAG;IAC1B,IAAID,sBAAsB,EAAE;MACxBA,sBAAsB,CAACd,eAAe,CAAC,CAAC;IAC5C;IACAc,sBAAsB,GAAG,IAAI;IAC7B;IACAD,gBAAgB,CAAC,CAAC,IAAIA,gBAAgB,CAAC,CAAC,CAAC9U,aAAa,CAAC,CAAC,CAACmB,aAAa,CAAC,CAAC;EAC5E;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,SAAS4G,SAASA,CAACM,EAAE,EAAE;IACnB;IACA,MAAM4M,WAAW,GAAG,SAAAA,CAAU,GAAGvO,IAAI,EAAE;MACnC,MAAM9G,aAAa,GAAGkV,gBAAgB,CAAC,CAAC;MACxC,IAAI,CAAClV,aAAa,EAAE;QAChB,MAAM,IAAIzD,KAAK,CAAC,8EAA8E,GAC1F,uEAAuE,CAAC;MAChF;MACA,MAAM4M,aAAa,GAAGnJ,aAAa,CAACI,aAAa,CAAC,CAAC;MACnD,IAAIlC,IAAI,CAACgC,OAAO,CAACD,GAAG,CAAC,uBAAuB,CAAC,EAAE;QAC3C,MAAM,IAAI1D,KAAK,CAAC,qCAAqC,CAAC;MAC1D;MACA,IAAI;QACA;QACA,IAAI,CAAC4Y,sBAAsB,EAAE;UACzB,IAAIhM,aAAa,CAAC7H,WAAW,CAAC,CAAC,YAAYqF,qBAAqB,EAAE;YAC9D,MAAM,IAAIpK,KAAK,CAAC,qCAAqC,CAAC;UAC1D;UACA4Y,sBAAsB,GAAG,IAAIxO,qBAAqB,CAAC,CAAC;QACxD;QACA,IAAI2O,GAAG;QACP,MAAMC,iBAAiB,GAAGpM,aAAa,CAAC7H,WAAW,CAAC,CAAC;QACrD6H,aAAa,CAACvI,WAAW,CAACuU,sBAAsB,CAAC;QACjDA,sBAAsB,CAACf,aAAa,CAAC,CAAC;QACtC,IAAI;UACAkB,GAAG,GAAG7M,EAAE,CAACtD,KAAK,CAAC,IAAI,EAAE2B,IAAI,CAAC;UAC1ByE,eAAe,CAAC,CAAC;QACrB,CAAC,SACO;UACJpC,aAAa,CAACvI,WAAW,CAAC2U,iBAAiB,CAAC;QAChD;QACA,IAAIJ,sBAAsB,CAACvC,qBAAqB,CAAC1V,MAAM,GAAG,CAAC,EAAE;UACzD,MAAM,IAAIX,KAAK,CAAE,GAAE4Y,sBAAsB,CAACvC,qBAAqB,CAAC1V,MAAO,GAAE,GACpE,uCAAsC,CAAC;QAChD;QACA,IAAIiY,sBAAsB,CAACtC,aAAa,CAAC3V,MAAM,GAAG,CAAC,EAAE;UACjD,MAAM,IAAIX,KAAK,CAAE,GAAE4Y,sBAAsB,CAACtC,aAAa,CAAC3V,MAAO,+BAA8B,CAAC;QAClG;QACA,OAAOoY,GAAG;MACd,CAAC,SACO;QACJF,kBAAkB,CAAC,CAAC;MACxB;IACJ,CAAC;IACDC,WAAW,CAACnL,WAAW,GAAG,IAAI;IAC9B,OAAOmL,WAAW;EACtB;EACA,SAASG,qBAAqBA,CAAA,EAAG;IAC7B,IAAIL,sBAAsB,IAAI,IAAI,EAAE;MAChCA,sBAAsB,GAAGjX,IAAI,CAACgC,OAAO,CAACD,GAAG,CAAC,uBAAuB,CAAC;MAClE,IAAIkV,sBAAsB,IAAI,IAAI,EAAE;QAChC,MAAM,IAAI5Y,KAAK,CAAC,wEAAwE,CAAC;MAC7F;IACJ;IACA,OAAO4Y,sBAAsB;EACjC;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,SAAS9O,IAAIA,CAACkL,MAAM,GAAG,CAAC,EAAEkE,mBAAmB,GAAG,KAAK,EAAE;IACnDD,qBAAqB,CAAC,CAAC,CAACnP,IAAI,CAACkL,MAAM,EAAE,IAAI,EAAEkE,mBAAmB,CAAC;EACnE;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,SAASjK,KAAKA,CAACkK,QAAQ,EAAE;IACrB,OAAOF,qBAAqB,CAAC,CAAC,CAAChK,KAAK,CAACkK,QAAQ,CAAC;EAClD;EACA;AACJ;AACA;AACA;AACA;EACI,SAASC,oBAAoBA,CAAA,EAAG;IAC5B,MAAMxT,QAAQ,GAAGqT,qBAAqB,CAAC,CAAC;IACxCrT,QAAQ,CAACyQ,qBAAqB;IAC9BzQ,QAAQ,CAACyQ,qBAAqB,CAAC1V,MAAM,GAAG,CAAC;EAC7C;EACA;AACJ;AACA;AACA;AACA;EACI,SAASqO,eAAeA,CAAA,EAAG;IACvBiK,qBAAqB,CAAC,CAAC,CAACjK,eAAe,CAAC,CAAC;EAC7C;EACArN,IAAI,CAACsF,GAAG,CAACW,MAAM,CAAC,eAAe,CAAC,CAAC,GAC7B;IAAEiR,kBAAkB;IAAE7J,eAAe;IAAEoK,oBAAoB;IAAEtP,IAAI;IAAEmF,KAAK;IAAErD;EAAU,CAAC;AAC7F,CAAC,EAAE,IAAI,CAAC;;AAER;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAjK,IAAI,CAACoF,YAAY,CAAC,gBAAgB,EAAE,CAACC,MAAM,EAAErF,IAAI,EAAEsF,GAAG,KAAK;EACvD,MAAMoS,WAAW,GAAGpS,GAAG,CAACW,MAAM,CAAC,OAAO,CAAC;EACvC,MAAM0R,UAAU,GAAG,IAAI;EACvB,MAAMpI,sBAAsB,GAAGjK,GAAG,CAACW,MAAM,CAAC,kBAAkB,CAAC;EAC7D;EACA;EACA;EACA;EACA;EACA;EACA;EACAoK,OAAO,CAAC/K,GAAG,CAACW,MAAM,CAAC,qBAAqB,CAAC,CAAC,GAAG,SAASmK,mBAAmBA,CAAA,EAAG;IACxE,IAAIwH,OAAO,GAAGvH,OAAO,CAACrQ,IAAI,CAACK,UAAU,CAAC,iBAAiB,CAAC,CAAC;IACzD,IAAIuX,OAAO,EAAE;MACT;IACJ;IACAA,OAAO,GAAGvH,OAAO,CAACrQ,IAAI,CAACK,UAAU,CAAC,iBAAiB,CAAC,CAAC,GAAGgQ,OAAO,CAACzK,SAAS,CAACiS,IAAI;IAC9ExH,OAAO,CAACzK,SAAS,CAACiS,IAAI,GAAG,YAAY;MACjC,MAAMC,OAAO,GAAGF,OAAO,CAAC3Q,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;MAC9C,IAAI,IAAI,CAACwQ,WAAW,CAAC,KAAKC,UAAU,EAAE;QAClC;QACA,MAAMI,iBAAiB,GAAG/X,IAAI,CAACgC,OAAO,CAACD,GAAG,CAAC,mBAAmB,CAAC;QAC/D,IAAIgW,iBAAiB,EAAE;UACnBA,iBAAiB,CAAC/H,6BAA6B,EAAE;UACjD8H,OAAO,CAACvI,sBAAsB,CAAC,GAAG,IAAI;QAC1C;MACJ;MACA,OAAOuI,OAAO;IAClB,CAAC;EACL,CAAC;EACDzH,OAAO,CAAC/K,GAAG,CAACW,MAAM,CAAC,uBAAuB,CAAC,CAAC,GAAG,SAAS+R,qBAAqBA,CAAA,EAAG;IAC5E;IACA,MAAMJ,OAAO,GAAGvH,OAAO,CAACrQ,IAAI,CAACK,UAAU,CAAC,iBAAiB,CAAC,CAAC;IAC3D,IAAIuX,OAAO,EAAE;MACTvH,OAAO,CAACzK,SAAS,CAACiS,IAAI,GAAGD,OAAO;MAChCvH,OAAO,CAACrQ,IAAI,CAACK,UAAU,CAAC,iBAAiB,CAAC,CAAC,GAAGD,SAAS;IAC3D;EACJ,CAAC;AACL,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}