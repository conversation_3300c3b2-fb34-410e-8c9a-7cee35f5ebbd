{"ast": null, "code": "import { TestBed } from '@angular/core/testing';\nimport { RouterTestingModule } from '@angular/router/testing';\nimport { AppComponent } from './app.component';\ndescribe('AppComponent', () => {\n  beforeEach(() => TestBed.configureTestingModule({\n    imports: [RouterTestingModule],\n    declarations: [AppComponent]\n  }));\n  it('should create the app', () => {\n    const fixture = TestBed.createComponent(AppComponent);\n    const app = fixture.componentInstance;\n    expect(app).toBeTruthy();\n  });\n  it(`should have as title 'front'`, () => {\n    const fixture = TestBed.createComponent(AppComponent);\n    const app = fixture.componentInstance;\n    expect(app.title).toEqual('front');\n  });\n  it('should render router outlet', () => {\n    const fixture = TestBed.createComponent(AppComponent);\n    fixture.detectChanges();\n    const compiled = fixture.nativeElement;\n    expect(compiled.querySelector('router-outlet')).toBeTruthy();\n  });\n});", "map": {"version": 3, "names": ["TestBed", "RouterTestingModule", "AppComponent", "describe", "beforeEach", "configureTestingModule", "imports", "declarations", "it", "fixture", "createComponent", "app", "componentInstance", "expect", "toBeTruthy", "title", "toEqual", "detectChanges", "compiled", "nativeElement", "querySelector"], "sources": ["C:\\Users\\<USER>\\Desktop\\angular\\front\\src\\app\\app.component.spec.ts"], "sourcesContent": ["import { TestBed } from '@angular/core/testing';\nimport { RouterTestingModule } from '@angular/router/testing';\nimport { AppComponent } from './app.component';\n\ndescribe('AppComponent', () => {\n  beforeEach(() => TestBed.configureTestingModule({\n    imports: [RouterTestingModule],\n    declarations: [AppComponent]\n  }));\n\n  it('should create the app', () => {\n    const fixture = TestBed.createComponent(AppComponent);\n    const app = fixture.componentInstance;\n    expect(app).toBeTruthy();\n  });\n\n  it(`should have as title 'front'`, () => {\n    const fixture = TestBed.createComponent(AppComponent);\n    const app = fixture.componentInstance;\n    expect(app.title).toEqual('front');\n  });\n\n  it('should render router outlet', () => {\n    const fixture = TestBed.createComponent(AppComponent);\n    fixture.detectChanges();\n    const compiled = fixture.nativeElement as HTMLElement;\n    expect(compiled.querySelector('router-outlet')).toBeTruthy();\n  });\n});\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,uBAAuB;AAC/C,SAASC,mBAAmB,QAAQ,yBAAyB;AAC7D,SAASC,YAAY,QAAQ,iBAAiB;AAE9CC,QAAQ,CAAC,cAAc,EAAE,MAAK;EAC5BC,UAAU,CAAC,MAAMJ,OAAO,CAACK,sBAAsB,CAAC;IAC9CC,OAAO,EAAE,CAACL,mBAAmB,CAAC;IAC9BM,YAAY,EAAE,CAACL,YAAY;GAC5B,CAAC,CAAC;EAEHM,EAAE,CAAC,uBAAuB,EAAE,MAAK;IAC/B,MAAMC,OAAO,GAAGT,OAAO,CAACU,eAAe,CAACR,YAAY,CAAC;IACrD,MAAMS,GAAG,GAAGF,OAAO,CAACG,iBAAiB;IACrCC,MAAM,CAACF,GAAG,CAAC,CAACG,UAAU,EAAE;EAC1B,CAAC,CAAC;EAEFN,EAAE,CAAC,8BAA8B,EAAE,MAAK;IACtC,MAAMC,OAAO,GAAGT,OAAO,CAACU,eAAe,CAACR,YAAY,CAAC;IACrD,MAAMS,GAAG,GAAGF,OAAO,CAACG,iBAAiB;IACrCC,MAAM,CAACF,GAAG,CAACI,KAAK,CAAC,CAACC,OAAO,CAAC,OAAO,CAAC;EACpC,CAAC,CAAC;EAEFR,EAAE,CAAC,6BAA6B,EAAE,MAAK;IACrC,MAAMC,OAAO,GAAGT,OAAO,CAACU,eAAe,CAACR,YAAY,CAAC;IACrDO,OAAO,CAACQ,aAAa,EAAE;IACvB,MAAMC,QAAQ,GAAGT,OAAO,CAACU,aAA4B;IACrDN,MAAM,CAACK,QAAQ,CAACE,aAAa,CAAC,eAAe,CAAC,CAAC,CAACN,UAAU,EAAE;EAC9D,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}