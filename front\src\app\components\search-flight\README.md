# Composant SearchFlight - Recherche de Vols

## Description

Composant de recherche de vols moderne et responsive qui reproduit fidèlement le design de l'interface "Search and Book Flights". Ce composant permet aux utilisateurs de rechercher des vols avec différents types de voyage (aller simple, aller-retour, multi-destinations) et s'intègre parfaitement avec votre backend Spring Boot.

## Fonctionnalités

### 🎨 Design Fidèle à l'Image
- **Header avec icône** : Section "Search and Book Flights" avec sous-titre
- **Sidebar "Latest Searches"** : Zone pour les recherches récentes
- **Onglets de type de voyage** : One way, Round Trip, Multi-City/Stop-Overs
- **Interface intuitive** : Champs de saisie avec icônes et boutons interactifs

### ✈️ Types de Recherche
- **One Way** : Vol aller simple
- **Round Trip** : Vol aller-retour avec validation de date de retour
- **Multi-City** : Vols multi-destinations

### 📋 Champs de Recherche
- **Aéroports** : Départ et arrivée avec bouton d'échange
- **Dates** : Sélecteur de dates avec validation
- **Passagers** : Adultes, enfants, bébés avec sélecteurs
- **Classe de voyage** : Economy, Premium Economy, Business, First Class
- **Compagnie préférée** : Sélection optionnelle
- **Options avancées** : Tarifs remboursables, bagages, calendrier

### 🔧 Fonctionnalités Techniques
- **Validation en temps réel** : Formulaires réactifs avec messages d'erreur
- **Intégration backend** : Communication avec votre API Spring Boot
- **Gestion d'erreurs** : Messages d'erreur contextuels
- **Loading states** : Indicateurs de chargement pendant la recherche
- **Responsive design** : Adaptation mobile/tablette/desktop

## Structure des Fichiers

```
components/search-flight/
├── search-flight.component.ts      # Logique du composant
├── search-flight.component.html    # Template HTML
├── search-flight.component.css     # Styles CSS
├── search-flight.component.spec.ts # Tests unitaires
└── README.md                      # Documentation
```

## Intégration Backend

### Endpoints Utilisés
Le composant communique avec votre backend via ces endpoints :

```typescript
// One Way
POST /api/flights/search/oneway

// Round Trip  
POST /api/flights/search/roundtrip

// Multi-City
POST /api/flights/search/multicity
```

### Modèles de Données
Le composant utilise les interfaces TypeScript qui correspondent exactement à vos modèles Java :

- `OneWayRequest` → `com.paximum.demo.models.OneWayRequest`
- `RoundTripRequest` → `com.paximum.demo.models.RoundTripRequest`
- `MulticityRequest` → `com.paximum.demo.models.MulticityRequest`

### Authentification
- Utilise le token JWT stocké par `AuthService`
- Headers d'autorisation automatiques via l'intercepteur HTTP
- Gestion des erreurs d'authentification

## Utilisation

### Navigation depuis le Dashboard
```typescript
// Dans dashboard.component.ts
navigateToFlightSearch(): void {
  this.router.navigate(['/search-flights']);
}
```

### Route Protégée
```typescript
// Dans app-routing.module.ts
{
  path: 'search-flights',
  component: SearchFlightComponent,
  canActivate: [AuthGuard]
}
```

### Utilisation Directe
```html
<app-search-flight></app-search-flight>
```

## Configuration

### Variables d'Environnement
```typescript
// environment.ts
export const environment = {
  production: false,
  apiUrl: 'http://localhost:8080',
  authEndpoint: '/auth/login'
};
```

### Service FlightService
Le service gère automatiquement :
- Construction des requêtes selon le type de voyage
- Mapping des données du formulaire vers les modèles backend
- Gestion des erreurs HTTP
- Headers d'authentification

## Validation des Formulaires

### Champs Requis
- **Aéroport de départ** : Minimum 3 caractères
- **Aéroport d'arrivée** : Minimum 3 caractères
- **Date de départ** : Date valide
- **Date de retour** : Requise pour aller-retour
- **Passagers** : Minimum 1 adulte

### Validation Conditionnelle
- Date de retour obligatoire uniquement pour les vols aller-retour
- Validation des dates (retour après départ)
- Limites sur le nombre de passagers

## Responsive Design

### Desktop (>1024px)
- Layout en grille : formulaire principal + sidebar
- Tous les champs visibles sur une ligne
- Interface complète

### Tablet (768-1024px)
- Sidebar en bas du formulaire
- Champs regroupés par ligne
- Navigation adaptée

### Mobile (<768px)
- Layout vertical complet
- Champs empilés
- Boutons et sélecteurs optimisés pour le tactile

## Personnalisation

### Couleurs
```css
:root {
  --primary-blue: #4a90e2;
  --secondary-blue: #7bb3f0;
  --success-color: #48bb78;
  --error-color: #e53e3e;
}
```

### Compagnies Aériennes
```typescript
// Dans search-flight.component.ts
preferredAirlines = [
  'Turkish Airlines',
  'Emirates',
  'Qatar Airways',
  // Ajoutez vos compagnies
];
```

## Tests

### Exécution des Tests
```bash
ng test --include="**/search-flight.component.spec.ts"
```

### Couverture des Tests
- ✅ Initialisation du composant
- ✅ Validation des formulaires
- ✅ Échange d'aéroports
- ✅ Calcul des passagers
- ✅ Soumission des recherches
- ✅ Gestion des erreurs
- ✅ Types de voyage

## Évolutions Futures

### 🚀 Améliorations Prévues
- [ ] Autocomplétion des aéroports
- [ ] Historique des recherches
- [ ] Filtres avancés
- [ ] Sauvegarde des préférences
- [ ] Mode hors ligne
- [ ] Notifications push

### 📊 Analytics
- [ ] Tracking des recherches populaires
- [ ] Temps de réponse des API
- [ ] Taux de conversion

## Dépendances

- Angular 16+
- Angular Reactive Forms
- Angular HTTP Client
- RxJS
- Votre backend Spring Boot

## Support Navigateurs

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

---

**🎯 Le composant SearchFlight offre une expérience utilisateur moderne et intuitive pour la recherche de vols, parfaitement intégré avec votre architecture backend Spring Boot !**
