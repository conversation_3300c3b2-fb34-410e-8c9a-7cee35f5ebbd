{"ast": null, "code": "import { combineLatestInit } from '../observable/combineLatest';\nimport { operate } from '../util/lift';\nimport { argsOrArgArray } from '../util/argsOrArgArray';\nimport { mapOneOrManyArgs } from '../util/mapOneOrManyArgs';\nimport { pipe } from '../util/pipe';\nimport { popResultSelector } from '../util/args';\nexport function combineLatest(...args) {\n  const resultSelector = popResultSelector(args);\n  return resultSelector ? pipe(combineLatest(...args), mapOneOrManyArgs(resultSelector)) : operate((source, subscriber) => {\n    combineLatestInit([source, ...argsOrArgArray(args)])(subscriber);\n  });\n}", "map": {"version": 3, "names": ["combineLatestInit", "operate", "argsOrArgArray", "mapOneOrManyArgs", "pipe", "popResultSelector", "combineLatest", "args", "resultSelector", "source", "subscriber"], "sources": ["C:/Users/<USER>/Desktop/angular/front/node_modules/rxjs/dist/esm/internal/operators/combineLatest.js"], "sourcesContent": ["import { combineLatestInit } from '../observable/combineLatest';\nimport { operate } from '../util/lift';\nimport { argsOrArgArray } from '../util/argsOrArgArray';\nimport { mapOneOrManyArgs } from '../util/mapOneOrManyArgs';\nimport { pipe } from '../util/pipe';\nimport { popResultSelector } from '../util/args';\nexport function combineLatest(...args) {\n    const resultSelector = popResultSelector(args);\n    return resultSelector\n        ? pipe(combineLatest(...args), mapOneOrManyArgs(resultSelector))\n        : operate((source, subscriber) => {\n            combineLatestInit([source, ...argsOrArgArray(args)])(subscriber);\n        });\n}\n"], "mappings": "AAAA,SAASA,iBAAiB,QAAQ,6BAA6B;AAC/D,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,cAAc,QAAQ,wBAAwB;AACvD,SAASC,gBAAgB,QAAQ,0BAA0B;AAC3D,SAASC,IAAI,QAAQ,cAAc;AACnC,SAASC,iBAAiB,QAAQ,cAAc;AAChD,OAAO,SAASC,aAAaA,CAAC,GAAGC,IAAI,EAAE;EACnC,MAAMC,cAAc,GAAGH,iBAAiB,CAACE,IAAI,CAAC;EAC9C,OAAOC,cAAc,GACfJ,IAAI,CAACE,aAAa,CAAC,GAAGC,IAAI,CAAC,EAAEJ,gBAAgB,CAACK,cAAc,CAAC,CAAC,GAC9DP,OAAO,CAAC,CAACQ,MAAM,EAAEC,UAAU,KAAK;IAC9BV,iBAAiB,CAAC,CAACS,MAAM,EAAE,GAAGP,cAAc,CAACK,IAAI,CAAC,CAAC,CAAC,CAACG,UAAU,CAAC;EACpE,CAAC,CAAC;AACV", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}