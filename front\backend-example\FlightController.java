package com.paximum.demo.controllers;

import org.springframework.web.bind.annotation.*;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;

import com.paximum.demo.models.OneWayRequest;
import com.paximum.demo.models.OneWayResponse;
import com.paximum.demo.models.RoundTripRequest;
import com.paximum.demo.models.RoundTripResponse;
import com.paximum.demo.models.MulticityRequest;
import com.paximum.demo.models.MulticityResponse;
import com.paximum.demo.services.ProductService;

import reactor.core.publisher.Mono;

/**
 * Contrôleur REST pour la recherche de vols
 * Utilise le ProductService existant pour communiquer avec l'API Paximum
 */
@RestController
@RequestMapping("/api/flights")
@CrossOrigin(origins = "http://localhost:4200")
public class FlightController {
    
    private final ProductService productService;

    public FlightController(ProductService productService) {
        this.productService = productService;
    }

    /**
     * Recherche de vols aller simple
     * 
     * @param oneWayRequest La requête de recherche
     * @param authentication L'authentification de l'utilisateur
     * @return Mono<ResponseEntity<OneWayResponse>>
     */
    @PostMapping("/search/oneway")
    public Mono<ResponseEntity<OneWayResponse>> searchOneWayFlights(
            @RequestBody OneWayRequest oneWayRequest,
            Authentication authentication) {
        
        // Récupérer le token depuis l'authentification
        String token = extractTokenFromAuthentication(authentication);
        
        return productService.searchOneWayFlights(oneWayRequest, token)
            .map(response -> ResponseEntity.ok(response))
            .onErrorReturn(ResponseEntity.internalServerError().build());
    }

    /**
     * Recherche de vols aller-retour
     * 
     * @param roundTripRequest La requête de recherche
     * @param authentication L'authentification de l'utilisateur
     * @return Mono<ResponseEntity<RoundTripResponse>>
     */
    @PostMapping("/search/roundtrip")
    public Mono<ResponseEntity<RoundTripResponse>> searchRoundTripFlights(
            @RequestBody RoundTripRequest roundTripRequest,
            Authentication authentication) {
        
        String token = extractTokenFromAuthentication(authentication);
        
        return productService.searchRoundTripFlights(roundTripRequest, token)
            .map(response -> ResponseEntity.ok(response))
            .onErrorReturn(ResponseEntity.internalServerError().build());
    }

    /**
     * Recherche de vols multi-destinations
     * 
     * @param multicityRequest La requête de recherche
     * @param authentication L'authentification de l'utilisateur
     * @return Mono<ResponseEntity<MulticityResponse>>
     */
    @PostMapping("/search/multicity")
    public Mono<ResponseEntity<MulticityResponse>> searchMulticityFlights(
            @RequestBody MulticityRequest multicityRequest,
            Authentication authentication) {
        
        String token = extractTokenFromAuthentication(authentication);
        
        return productService.searchMulticityFlights(multicityRequest, token)
            .map(response -> ResponseEntity.ok(response))
            .onErrorReturn(ResponseEntity.internalServerError().build());
    }

    /**
     * Extrait le token JWT de l'objet Authentication
     * 
     * @param authentication L'objet d'authentification
     * @return Le token JWT
     */
    private String extractTokenFromAuthentication(Authentication authentication) {
        // Cette méthode dépend de votre implémentation de sécurité
        // Exemple si vous stockez le token dans les détails de l'authentification
        if (authentication != null && authentication.getDetails() instanceof String) {
            return (String) authentication.getDetails();
        }
        
        // Ou si vous utilisez un JWT token dans le principal
        if (authentication != null && authentication.getPrincipal() instanceof String) {
            return (String) authentication.getPrincipal();
        }
        
        // Fallback - vous devrez adapter selon votre implémentation
        throw new RuntimeException("Token non trouvé dans l'authentification");
    }

    /**
     * Endpoint de test pour vérifier la connectivité
     */
    @GetMapping("/health")
    public ResponseEntity<String> healthCheck() {
        return ResponseEntity.ok("Flight service is running");
    }
}

/*
 * NOTES D'IMPLÉMENTATION :
 * 
 * 1. Ce contrôleur utilise votre ProductService existant
 * 2. Il faut adapter la méthode extractTokenFromAuthentication selon votre implémentation de sécurité
 * 3. Vous pouvez ajouter une validation des données d'entrée
 * 4. Considérez l'ajout de logs pour le debugging
 * 
 * EXEMPLE D'UTILISATION AVEC VOTRE SÉCURITÉ :
 * 
 * Si vous utilisez JWT avec Spring Security, vous pourriez avoir quelque chose comme :
 * 
 * @Autowired
 * private JwtTokenProvider jwtTokenProvider;
 * 
 * private String extractTokenFromAuthentication(Authentication authentication) {
 *     return jwtTokenProvider.getTokenFromAuthentication(authentication);
 * }
 * 
 * OU si vous stockez le token dans le SecurityContext :
 * 
 * private String extractTokenFromAuthentication(Authentication authentication) {
 *     return SecurityContextHolder.getContext().getAuthentication().getCredentials().toString();
 * }
 */
