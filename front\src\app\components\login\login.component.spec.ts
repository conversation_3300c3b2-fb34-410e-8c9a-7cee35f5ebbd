import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ReactiveFormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { of, throwError } from 'rxjs';

import { LoginComponent } from './login.component';
import { AuthService } from '../../services/auth.service';
import { AuthResponse } from '../../models/auth-response.interface';

describe('LoginComponent', () => {
  let component: LoginComponent;
  let fixture: ComponentFixture<LoginComponent>;
  let authService: jasmine.SpyObj<AuthService>;
  let router: jasmine.SpyObj<Router>;

  const mockAuthResponse: AuthResponse = {
    header: {
      requestId: '123',
      success: true,
      messages: []
    },
    body: {
      token: 'mock-token',
      expiresOn: '2024-12-31T23:59:59Z',
      tokenId: 1,
      userInfo: {
        code: 'USER001',
        name: 'Test User',
        mainAgency: { code: 'AG001', name: 'Test Agency', registerCode: 'REG001' },
        agency: { code: 'AG001', name: 'Test Agency', registerCode: 'REG001' },
        office: { code: 'OFF001', name: 'Test Office' },
        operator: { code: 'OP001', name: 'Test Operator', thumbnail: 'thumb.jpg' },
        market: { code: 'MK001', name: 'Test Market', favicon: 'favicon.ico' }
      }
    }
  };

  beforeEach(async () => {
    const authServiceSpy = jasmine.createSpyObj('AuthService', ['authenticate', 'isAuthenticated']);
    const routerSpy = jasmine.createSpyObj('Router', ['navigate']);

    await TestBed.configureTestingModule({
      declarations: [LoginComponent],
      imports: [ReactiveFormsModule, HttpClientTestingModule],
      providers: [
        { provide: AuthService, useValue: authServiceSpy },
        { provide: Router, useValue: routerSpy }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(LoginComponent);
    component = fixture.componentInstance;
    authService = TestBed.inject(AuthService) as jasmine.SpyObj<AuthService>;
    router = TestBed.inject(Router) as jasmine.SpyObj<Router>;
  });

  beforeEach(() => {
    authService.isAuthenticated.and.returnValue(false);
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize form with empty values', () => {
    expect(component.loginForm.get('agency')?.value).toBe('');
    expect(component.loginForm.get('user')?.value).toBe('');
    expect(component.loginForm.get('password')?.value).toBe('');
  });

  it('should validate required fields', () => {
    const agencyControl = component.loginForm.get('agency');
    const userControl = component.loginForm.get('user');
    const passwordControl = component.loginForm.get('password');

    expect(agencyControl?.hasError('required')).toBeTruthy();
    expect(userControl?.hasError('required')).toBeTruthy();
    expect(passwordControl?.hasError('required')).toBeTruthy();
  });

  it('should validate minimum length', () => {
    component.loginForm.patchValue({
      agency: 'A',
      user: 'U',
      password: '123'
    });

    const agencyControl = component.loginForm.get('agency');
    const userControl = component.loginForm.get('user');
    const passwordControl = component.loginForm.get('password');

    expect(agencyControl?.hasError('minlength')).toBeTruthy();
    expect(userControl?.hasError('minlength')).toBeTruthy();
    expect(passwordControl?.hasError('minlength')).toBeTruthy();
  });

  it('should toggle password visibility', () => {
    expect(component.showPassword).toBeFalsy();
    component.togglePasswordVisibility();
    expect(component.showPassword).toBeTruthy();
    component.togglePasswordVisibility();
    expect(component.showPassword).toBeFalsy();
  });

  it('should submit form with valid data and correct property names', () => {
    authService.authenticate.and.returnValue(of(mockAuthResponse));
    
    component.loginForm.patchValue({
      agency: 'TEST_AGENCY',
      user: 'TEST_USER',
      password: 'password123'
    });

    component.onSubmit();

    expect(authService.authenticate).toHaveBeenCalledWith({
      Agency: 'TEST_AGENCY',
      User: 'TEST_USER',
      Password: 'password123'
    });
    expect(router.navigate).toHaveBeenCalledWith(['/dashboard']);
  });

  it('should handle authentication error', () => {
    const errorMessage = 'Authentication failed';
    authService.authenticate.and.returnValue(throwError(() => new Error(errorMessage)));
    
    component.loginForm.patchValue({
      agency: 'TEST_AGENCY',
      user: 'TEST_USER',
      password: 'password123'
    });

    component.onSubmit();

    expect(component.errorMessage).toBe(errorMessage);
    expect(component.isLoading).toBeFalsy();
  });

  it('should handle API error response', () => {
    const errorResponse: AuthResponse = {
      header: {
        requestId: '123',
        success: false,
        messages: [{ id: 1, code: 'AUTH_ERROR', messageType: 1, message: 'Invalid credentials' }]
      },
      body: {
        token: '',
        expiresOn: '',
        tokenId: 0,
        userInfo: {} as any
      }
    };

    authService.authenticate.and.returnValue(of(errorResponse));
    
    component.loginForm.patchValue({
      agency: 'TEST_AGENCY',
      user: 'TEST_USER',
      password: 'wrongpassword'
    });

    component.onSubmit();

    expect(component.errorMessage).toBe('Invalid credentials');
    expect(component.isLoading).toBeFalsy();
  });

  it('should redirect if already authenticated', () => {
    authService.isAuthenticated.and.returnValue(true);
    
    component.ngOnInit();
    
    expect(router.navigate).toHaveBeenCalledWith(['/dashboard']);
  });

  it('should not submit invalid form', () => {
    component.onSubmit();
    
    expect(authService.authenticate).not.toHaveBeenCalled();
    expect(component.loginForm.get('agency')?.touched).toBeTruthy();
    expect(component.loginForm.get('user')?.touched).toBeTruthy();
    expect(component.loginForm.get('password')?.touched).toBeTruthy();
  });

  it('should clear error message', () => {
    component.errorMessage = 'Test error';
    component.clearError();
    expect(component.errorMessage).toBe('');
  });

  it('should trim whitespace from input values', () => {
    authService.authenticate.and.returnValue(of(mockAuthResponse));
    
    component.loginForm.patchValue({
      agency: '  TEST_AGENCY  ',
      user: '  TEST_USER  ',
      password: 'password123'
    });

    component.onSubmit();

    expect(authService.authenticate).toHaveBeenCalledWith({
      Agency: 'TEST_AGENCY',
      User: 'TEST_USER',
      Password: 'password123'
    });
  });
});
