{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../services/auth.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/common\";\nfunction LoginComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 31);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 32);\n    i0.ɵɵelement(2, \"path\", 33);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 34);\n    i0.ɵɵlistener(\"click\", function LoginComponent_div_11_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r8 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r8.clearError());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(6, \"svg\", 35);\n    i0.ɵɵelement(7, \"path\", 36);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r0.errorMessage);\n  }\n}\nfunction LoginComponent_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 37);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getErrorMessage(\"agency\"), \" \");\n  }\n}\nfunction LoginComponent_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 37);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.getErrorMessage(\"user\"), \" \");\n  }\n}\nfunction LoginComponent__svg_svg_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(0, \"svg\", 38);\n    i0.ɵɵelement(1, \"path\", 39)(2, \"path\", 40);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent__svg_svg_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(0, \"svg\", 38);\n    i0.ɵɵelement(1, \"path\", 41);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_div_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 37);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r5.getErrorMessage(\"password\"), \" \");\n  }\n}\nfunction LoginComponent_span_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 42);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 43);\n    i0.ɵɵelement(2, \"path\", 44);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" Se connecter \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_span_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 45);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 46);\n    i0.ɵɵelement(2, \"circle\", 47)(3, \"path\", 48);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" Connexion en cours... \");\n    i0.ɵɵelementEnd();\n  }\n}\nexport class LoginComponent {\n  constructor(formBuilder, authService, router) {\n    this.formBuilder = formBuilder;\n    this.authService = authService;\n    this.router = router;\n    this.isLoading = false;\n    this.errorMessage = '';\n    this.showPassword = false;\n  }\n  ngOnInit() {\n    this.initializeForm();\n    // Rediriger si déjà connecté\n    if (this.authService.isAuthenticated()) {\n      this.router.navigate(['/dashboard']); // Ajustez selon votre route de destination\n    }\n  }\n  /**\n   * Initialise le formulaire de connexion avec les validations\n   */\n  initializeForm() {\n    this.loginForm = this.formBuilder.group({\n      agency: ['', [Validators.required, Validators.minLength(2)]],\n      user: ['', [Validators.required, Validators.minLength(2)]],\n      password: ['', [Validators.required, Validators.minLength(6)]]\n    });\n  }\n  /**\n   * Getter pour accéder facilement aux contrôles du formulaire\n   */\n  get formControls() {\n    return this.loginForm.controls;\n  }\n  /**\n   * Bascule la visibilité du mot de passe\n   */\n  togglePasswordVisibility() {\n    this.showPassword = !this.showPassword;\n  }\n  /**\n   * Soumet le formulaire de connexion\n   */\n  onSubmit() {\n    if (this.loginForm.valid && !this.isLoading) {\n      this.isLoading = true;\n      this.errorMessage = '';\n      // Créer l'objet de requête avec les noms de propriétés exacts du backend\n      const authRequest = {\n        Agency: this.loginForm.value.agency.trim(),\n        User: this.loginForm.value.user.trim(),\n        Password: this.loginForm.value.password\n      };\n      this.authService.authenticate(authRequest).subscribe({\n        next: response => {\n          this.isLoading = false;\n          if (response.header.success) {\n            // Connexion réussie\n            console.log('Connexion réussie:', response.body.userInfo);\n            // Rediriger vers le dashboard ou la page d'accueil\n            this.router.navigate(['/dashboard']); // Ajustez selon votre route\n          } else {\n            // Erreur retournée par l'API\n            this.handleApiError(response);\n          }\n        },\n        error: error => {\n          this.isLoading = false;\n          this.errorMessage = error.message || 'Une erreur est survenue lors de la connexion';\n          console.error('Erreur de connexion:', error);\n        }\n      });\n    } else {\n      // Marquer tous les champs comme touchés pour afficher les erreurs\n      this.markFormGroupTouched();\n    }\n  }\n  /**\n   * Gère les erreurs retournées par l'API\n   */\n  handleApiError(response) {\n    if (response.header.messages && response.header.messages.length > 0) {\n      // Utiliser le premier message d'erreur de l'API\n      this.errorMessage = response.header.messages[0].message;\n    } else {\n      this.errorMessage = 'Échec de l\\'authentification';\n    }\n  }\n  /**\n   * Marque tous les champs du formulaire comme touchés\n   */\n  markFormGroupTouched() {\n    Object.keys(this.loginForm.controls).forEach(key => {\n      const control = this.loginForm.get(key);\n      control?.markAsTouched();\n    });\n  }\n  /**\n   * Vérifie si un champ a une erreur et a été touché\n   */\n  hasError(fieldName, errorType) {\n    const field = this.loginForm.get(fieldName);\n    return !!(field?.hasError(errorType) && field?.touched);\n  }\n  /**\n   * Récupère le message d'erreur pour un champ\n   */\n  getErrorMessage(fieldName) {\n    const field = this.loginForm.get(fieldName);\n    if (field?.hasError('required')) {\n      return `${this.getFieldDisplayName(fieldName)} est requis`;\n    }\n    if (field?.hasError('minlength')) {\n      const requiredLength = field.errors?.['minlength']?.requiredLength;\n      return `${this.getFieldDisplayName(fieldName)} doit contenir au moins ${requiredLength} caractères`;\n    }\n    return '';\n  }\n  /**\n   * Retourne le nom d'affichage pour un champ\n   */\n  getFieldDisplayName(fieldName) {\n    const displayNames = {\n      agency: 'Le code agence',\n      user: 'Le code utilisateur',\n      password: 'Le mot de passe'\n    };\n    return displayNames[fieldName] || fieldName;\n  }\n  /**\n   * Nettoie le message d'erreur\n   */\n  clearError() {\n    this.errorMessage = '';\n  }\n  static {\n    this.ɵfac = function LoginComponent_Factory(t) {\n      return new (t || LoginComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: LoginComponent,\n      selectors: [[\"app-login\"]],\n      decls: 47,\n      vars: 20,\n      consts: [[1, \"login-container\"], [1, \"login-card\"], [1, \"login-header\"], [1, \"logo-container\"], [1, \"login-title\"], [1, \"logo-subtitle\"], [1, \"login-subtitle\"], [\"novalidate\", \"\", 1, \"login-form\", 3, \"formGroup\", \"ngSubmit\"], [\"class\", \"error-message\", \"role\", \"alert\", 4, \"ngIf\"], [1, \"form-group\"], [\"for\", \"agency\", 1, \"form-label\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"label-icon\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4\"], [\"type\", \"text\", \"id\", \"agency\", \"formControlName\", \"agency\", \"placeholder\", \"Entrez le code de votre agence\", \"autocomplete\", \"organization\", \"maxlength\", \"50\", 1, \"form-input\"], [\"class\", \"field-error\", 4, \"ngIf\"], [\"for\", \"user\", 1, \"form-label\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\"], [\"type\", \"text\", \"id\", \"user\", \"formControlName\", \"user\", \"placeholder\", \"Entrez votre code utilisateur\", \"autocomplete\", \"username\", \"maxlength\", \"50\", 1, \"form-input\"], [\"for\", \"password\", 1, \"form-label\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z\"], [1, \"password-input-container\"], [\"id\", \"password\", \"formControlName\", \"password\", \"placeholder\", \"Entrez votre mot de passe\", \"autocomplete\", \"current-password\", \"maxlength\", \"100\", 1, \"form-input\", \"password-input\", 3, \"type\"], [\"type\", \"button\", \"tabindex\", \"0\", 1, \"password-toggle\", 3, \"click\"], [\"class\", \"password-icon\", \"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 4, \"ngIf\"], [\"type\", \"submit\", 1, \"login-button\", 3, \"disabled\"], [\"class\", \"button-content\", 4, \"ngIf\"], [\"class\", \"loading-content\", 4, \"ngIf\"], [1, \"login-footer\"], [1, \"footer-text\"], [\"href\", \"mailto:<EMAIL>\", 1, \"footer-link\"], [1, \"version-info\"], [\"role\", \"alert\", 1, \"error-message\"], [\"fill\", \"currentColor\", \"viewBox\", \"0 0 20 20\", \"aria-hidden\", \"true\", 1, \"error-icon\"], [\"fill-rule\", \"evenodd\", \"d\", \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z\", \"clip-rule\", \"evenodd\"], [\"type\", \"button\", \"aria-label\", \"Fermer le message d'erreur\", 1, \"error-close\", 3, \"click\"], [\"fill\", \"currentColor\", \"viewBox\", \"0 0 20 20\"], [\"fill-rule\", \"evenodd\", \"d\", \"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z\", \"clip-rule\", \"evenodd\"], [1, \"field-error\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"password-icon\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21\"], [1, \"button-content\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"button-icon\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1\"], [1, \"loading-content\"], [\"viewBox\", \"0 0 24 24\", 1, \"loading-spinner\"], [\"cx\", \"12\", \"cy\", \"12\", \"r\", \"10\", \"stroke\", \"currentColor\", \"stroke-width\", \"4\", \"fill\", \"none\", \"opacity\", \"0.25\"], [\"fill\", \"currentColor\", \"d\", \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\", \"opacity\", \"0.75\"]],\n      template: function LoginComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"h1\", 4);\n          i0.ɵɵtext(5, \"Paximum\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"div\", 5);\n          i0.ɵɵtext(7, \"Syst\\u00E8me d'authentification\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(8, \"p\", 6);\n          i0.ɵɵtext(9, \"Connectez-vous \\u00E0 votre compte\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(10, \"form\", 7);\n          i0.ɵɵlistener(\"ngSubmit\", function LoginComponent_Template_form_ngSubmit_10_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵtemplate(11, LoginComponent_div_11_Template, 8, 1, \"div\", 8);\n          i0.ɵɵelementStart(12, \"div\", 9)(13, \"label\", 10);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(14, \"svg\", 11);\n          i0.ɵɵelement(15, \"path\", 12);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(16, \" Code Agence \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelement(17, \"input\", 13);\n          i0.ɵɵtemplate(18, LoginComponent_div_18_Template, 2, 1, \"div\", 14);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"div\", 9)(20, \"label\", 15);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(21, \"svg\", 11);\n          i0.ɵɵelement(22, \"path\", 16);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(23, \" Code Utilisateur \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelement(24, \"input\", 17);\n          i0.ɵɵtemplate(25, LoginComponent_div_25_Template, 2, 1, \"div\", 14);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(26, \"div\", 9)(27, \"label\", 18);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(28, \"svg\", 11);\n          i0.ɵɵelement(29, \"path\", 19);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(30, \" Mot de passe \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(31, \"div\", 20);\n          i0.ɵɵelement(32, \"input\", 21);\n          i0.ɵɵelementStart(33, \"button\", 22);\n          i0.ɵɵlistener(\"click\", function LoginComponent_Template_button_click_33_listener() {\n            return ctx.togglePasswordVisibility();\n          });\n          i0.ɵɵtemplate(34, LoginComponent__svg_svg_34_Template, 3, 0, \"svg\", 23);\n          i0.ɵɵtemplate(35, LoginComponent__svg_svg_35_Template, 2, 0, \"svg\", 23);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(36, LoginComponent_div_36_Template, 2, 1, \"div\", 14);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(37, \"button\", 24);\n          i0.ɵɵtemplate(38, LoginComponent_span_38_Template, 4, 0, \"span\", 25);\n          i0.ɵɵtemplate(39, LoginComponent_span_39_Template, 5, 0, \"span\", 26);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(40, \"div\", 27)(41, \"p\", 28);\n          i0.ɵɵtext(42, \" Besoin d'aide ? \");\n          i0.ɵɵelementStart(43, \"a\", 29);\n          i0.ɵɵtext(44, \"Contactez le support\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(45, \"div\", 30);\n          i0.ɵɵtext(46, \" Version 1.0.0 \");\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"formGroup\", ctx.loginForm);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.errorMessage);\n          i0.ɵɵadvance(6);\n          i0.ɵɵclassProp(\"error\", ctx.hasError(\"agency\", \"required\") || ctx.hasError(\"agency\", \"minlength\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.formControls[\"agency\"].touched && ctx.formControls[\"agency\"].errors);\n          i0.ɵɵadvance(6);\n          i0.ɵɵclassProp(\"error\", ctx.hasError(\"user\", \"required\") || ctx.hasError(\"user\", \"minlength\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.formControls[\"user\"].touched && ctx.formControls[\"user\"].errors);\n          i0.ɵɵadvance(7);\n          i0.ɵɵclassProp(\"error\", ctx.hasError(\"password\", \"required\") || ctx.hasError(\"password\", \"minlength\"));\n          i0.ɵɵproperty(\"type\", ctx.showPassword ? \"text\" : \"password\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵattribute(\"aria-label\", ctx.showPassword ? \"Masquer le mot de passe\" : \"Afficher le mot de passe\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.showPassword);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.showPassword);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.formControls[\"password\"].touched && ctx.formControls[\"password\"].errors);\n          i0.ɵɵadvance(1);\n          i0.ɵɵclassProp(\"loading\", ctx.isLoading);\n          i0.ɵɵproperty(\"disabled\", ctx.isLoading || ctx.loginForm.invalid);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n        }\n      },\n      dependencies: [i4.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.MaxLengthValidator, i1.FormGroupDirective, i1.FormControlName],\n      styles: [\"\\n\\n[_ngcontent-%COMP%]:root {\\n  --primary-color: #2563eb;\\n  --primary-hover: #1d4ed8;\\n  --primary-light: #dbeafe;\\n  --secondary-color: #64748b;\\n  --success-color: #059669;\\n  --error-color: #dc2626;\\n  --error-bg: #fef2f2;\\n  --error-border: #fecaca;\\n  --warning-color: #d97706;\\n  --background-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  --card-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);\\n  --border-radius: 0.75rem;\\n  --transition: all 0.2s ease;\\n}\\n\\n\\n\\n.login-container[_ngcontent-%COMP%] {\\n  min-height: 100vh;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  background: var(--background-gradient);\\n  padding: 1rem;\\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;\\n}\\n\\n\\n\\n.login-card[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: var(--border-radius);\\n  box-shadow: var(--card-shadow);\\n  padding: 2.5rem;\\n  width: 100%;\\n  max-width: 420px;\\n  animation: _ngcontent-%COMP%_slideUp 0.6s ease-out;\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.login-card[_ngcontent-%COMP%]::before {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  height: 4px;\\n  background: var(--background-gradient);\\n}\\n\\n@keyframes _ngcontent-%COMP%_slideUp {\\n  from {\\n    opacity: 0;\\n    transform: translateY(30px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n\\n\\n\\n.login-header[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-bottom: 2.5rem;\\n}\\n\\n.logo-container[_ngcontent-%COMP%] {\\n  margin-bottom: 1rem;\\n}\\n\\n.login-title[_ngcontent-%COMP%] {\\n  font-size: 2.25rem;\\n  font-weight: 800;\\n  color: var(--primary-color);\\n  margin: 0 0 0.25rem 0;\\n  letter-spacing: -0.025em;\\n}\\n\\n.logo-subtitle[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: var(--secondary-color);\\n  font-weight: 500;\\n}\\n\\n.login-subtitle[_ngcontent-%COMP%] {\\n  color: var(--secondary-color);\\n  margin: 0;\\n  font-size: 1rem;\\n  font-weight: 400;\\n}\\n\\n\\n\\n.login-form[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 1.75rem;\\n}\\n\\n\\n\\n.form-group[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 0.5rem;\\n}\\n\\n.form-label[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #374151;\\n  font-size: 0.875rem;\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n}\\n\\n.label-icon[_ngcontent-%COMP%] {\\n  width: 1rem;\\n  height: 1rem;\\n  color: var(--secondary-color);\\n}\\n\\n.form-input[_ngcontent-%COMP%] {\\n  padding: 0.875rem 1rem;\\n  border: 2px solid #e5e7eb;\\n  border-radius: 0.5rem;\\n  font-size: 1rem;\\n  transition: var(--transition);\\n  background-color: #f9fafb;\\n  font-weight: 400;\\n}\\n\\n.form-input[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  border-color: var(--primary-color);\\n  box-shadow: 0 0 0 3px var(--primary-light);\\n  background-color: white;\\n}\\n\\n.form-input[_ngcontent-%COMP%]:hover:not(:focus) {\\n  border-color: #d1d5db;\\n}\\n\\n.form-input.error[_ngcontent-%COMP%] {\\n  border-color: var(--error-color);\\n  background-color: var(--error-bg);\\n}\\n\\n.form-input.error[_ngcontent-%COMP%]:focus {\\n  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);\\n}\\n\\n\\n\\n.password-input-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  display: flex;\\n  align-items: center;\\n}\\n\\n.password-input[_ngcontent-%COMP%] {\\n  padding-right: 3.5rem;\\n}\\n\\n.password-toggle[_ngcontent-%COMP%] {\\n  position: absolute;\\n  right: 1rem;\\n  background: none;\\n  border: none;\\n  cursor: pointer;\\n  color: var(--secondary-color);\\n  padding: 0.5rem;\\n  border-radius: 0.375rem;\\n  transition: var(--transition);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n\\n.password-toggle[_ngcontent-%COMP%]:hover {\\n  color: #374151;\\n  background-color: #f3f4f6;\\n}\\n\\n.password-toggle[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  color: var(--primary-color);\\n  background-color: var(--primary-light);\\n}\\n\\n.password-icon[_ngcontent-%COMP%] {\\n  width: 1.25rem;\\n  height: 1.25rem;\\n}\\n\\n\\n\\n.error-message[_ngcontent-%COMP%] {\\n  background-color: var(--error-bg);\\n  border: 1px solid var(--error-border);\\n  color: var(--error-color);\\n  padding: 1rem;\\n  border-radius: 0.5rem;\\n  font-size: 0.875rem;\\n  display: flex;\\n  align-items: flex-start;\\n  gap: 0.75rem;\\n  position: relative;\\n}\\n\\n.error-icon[_ngcontent-%COMP%] {\\n  width: 1.25rem;\\n  height: 1.25rem;\\n  flex-shrink: 0;\\n  margin-top: 0.125rem;\\n}\\n\\n.error-close[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0.75rem;\\n  right: 0.75rem;\\n  background: none;\\n  border: none;\\n  color: var(--error-color);\\n  cursor: pointer;\\n  padding: 0.25rem;\\n  border-radius: 0.25rem;\\n  transition: var(--transition);\\n}\\n\\n.error-close[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(239, 68, 68, 0.1);\\n}\\n\\n.error-close[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  width: 1rem;\\n  height: 1rem;\\n}\\n\\n.field-error[_ngcontent-%COMP%] {\\n  color: var(--error-color);\\n  font-size: 0.75rem;\\n  margin-top: 0.25rem;\\n  font-weight: 500;\\n}\\n\\n\\n\\n.login-button[_ngcontent-%COMP%] {\\n  background: var(--background-gradient);\\n  color: white;\\n  border: none;\\n  padding: 1rem 1.5rem;\\n  border-radius: 0.5rem;\\n  font-size: 1rem;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: var(--transition);\\n  position: relative;\\n  overflow: hidden;\\n  min-height: 3.25rem;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n\\n.login-button[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  transform: translateY(-2px);\\n  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);\\n}\\n\\n.login-button[_ngcontent-%COMP%]:active:not(:disabled) {\\n  transform: translateY(0);\\n}\\n\\n.login-button[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.6;\\n  cursor: not-allowed;\\n  transform: none;\\n}\\n\\n.button-content[_ngcontent-%COMP%], .loading-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 0.5rem;\\n}\\n\\n.button-icon[_ngcontent-%COMP%] {\\n  width: 1.25rem;\\n  height: 1.25rem;\\n}\\n\\n.loading-spinner[_ngcontent-%COMP%] {\\n  width: 1.25rem;\\n  height: 1.25rem;\\n  animation: _ngcontent-%COMP%_spin 1s linear infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_spin {\\n  from {\\n    transform: rotate(0deg);\\n  }\\n  to {\\n    transform: rotate(360deg);\\n  }\\n}\\n\\n\\n\\n.login-footer[_ngcontent-%COMP%] {\\n  margin-top: 2.5rem;\\n  text-align: center;\\n  border-top: 1px solid #e5e7eb;\\n  padding-top: 1.5rem;\\n}\\n\\n.footer-text[_ngcontent-%COMP%] {\\n  color: var(--secondary-color);\\n  font-size: 0.875rem;\\n  margin: 0 0 0.75rem 0;\\n}\\n\\n.footer-link[_ngcontent-%COMP%] {\\n  color: var(--primary-color);\\n  text-decoration: none;\\n  font-weight: 600;\\n  transition: var(--transition);\\n}\\n\\n.footer-link[_ngcontent-%COMP%]:hover {\\n  color: var(--primary-hover);\\n  text-decoration: underline;\\n}\\n\\n.version-info[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  color: #9ca3af;\\n  font-weight: 500;\\n}\\n\\n\\n\\n@media (max-width: 480px) {\\n  .login-container[_ngcontent-%COMP%] {\\n    padding: 0.75rem;\\n  }\\n  \\n  .login-card[_ngcontent-%COMP%] {\\n    padding: 2rem 1.5rem;\\n    border-radius: 0.5rem;\\n  }\\n  \\n  .login-title[_ngcontent-%COMP%] {\\n    font-size: 1.875rem;\\n  }\\n  \\n  .form-input[_ngcontent-%COMP%] {\\n    padding: 0.75rem;\\n    font-size: 0.875rem;\\n  }\\n  \\n  .login-button[_ngcontent-%COMP%] {\\n    padding: 0.875rem 1.25rem;\\n    font-size: 0.875rem;\\n  }\\n}\\n\\n@media (max-width: 360px) {\\n  .login-card[_ngcontent-%COMP%] {\\n    padding: 1.5rem 1rem;\\n  }\\n  \\n  .login-title[_ngcontent-%COMP%] {\\n    font-size: 1.5rem;\\n  }\\n  \\n  .login-form[_ngcontent-%COMP%] {\\n    gap: 1.5rem;\\n  }\\n}\\n\\n\\n\\n@media (prefers-reduced-motion: reduce) {\\n  .login-card[_ngcontent-%COMP%] {\\n    animation: none;\\n  }\\n  \\n  .loading-spinner[_ngcontent-%COMP%] {\\n    animation: none;\\n  }\\n  \\n  .login-button[_ngcontent-%COMP%] {\\n    transition: none;\\n  }\\n  \\n  *[_ngcontent-%COMP%] {\\n    transition: none !important;\\n  }\\n}\\n\\n\\n\\n@media (prefers-color-scheme: dark) {\\n  .login-container[_ngcontent-%COMP%] {\\n    background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);\\n  }\\n  \\n  .login-card[_ngcontent-%COMP%] {\\n    background: #1e293b;\\n    color: #f1f5f9;\\n    border: 1px solid #334155;\\n  }\\n  \\n  .login-title[_ngcontent-%COMP%] {\\n    color: #60a5fa;\\n  }\\n  \\n  .login-subtitle[_ngcontent-%COMP%], .logo-subtitle[_ngcontent-%COMP%] {\\n    color: #cbd5e1;\\n  }\\n  \\n  .form-label[_ngcontent-%COMP%] {\\n    color: #e2e8f0;\\n  }\\n  \\n  .form-input[_ngcontent-%COMP%] {\\n    background-color: #334155;\\n    border-color: #475569;\\n    color: #f1f5f9;\\n  }\\n  \\n  .form-input[_ngcontent-%COMP%]:focus {\\n    background-color: #1e293b;\\n    border-color: #60a5fa;\\n  }\\n  \\n  .footer-text[_ngcontent-%COMP%] {\\n    color: #cbd5e1;\\n  }\\n  \\n  .version-info[_ngcontent-%COMP%] {\\n    color: #64748b;\\n  }\\n}\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "i0", "ɵɵelementStart", "ɵɵnamespaceSVG", "ɵɵelement", "ɵɵelementEnd", "ɵɵnamespaceHTML", "ɵɵtext", "ɵɵlistener", "LoginComponent_div_11_Template_button_click_5_listener", "ɵɵrestoreView", "_r9", "ctx_r8", "ɵɵnextContext", "ɵɵresetView", "clearError", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r0", "errorMessage", "ɵɵtextInterpolate1", "ctx_r1", "getErrorMessage", "ctx_r2", "ctx_r5", "LoginComponent", "constructor", "formBuilder", "authService", "router", "isLoading", "showPassword", "ngOnInit", "initializeForm", "isAuthenticated", "navigate", "loginForm", "group", "agency", "required", "<PERSON><PERSON><PERSON><PERSON>", "user", "password", "formControls", "controls", "togglePasswordVisibility", "onSubmit", "valid", "authRequest", "Agency", "value", "trim", "User", "Password", "authenticate", "subscribe", "next", "response", "header", "success", "console", "log", "body", "userInfo", "handleApiError", "error", "message", "markFormGroupTouched", "messages", "length", "Object", "keys", "for<PERSON>ach", "key", "control", "get", "<PERSON><PERSON><PERSON><PERSON>ched", "<PERSON><PERSON><PERSON><PERSON>", "fieldName", "errorType", "field", "touched", "getFieldDisplayName", "<PERSON><PERSON><PERSON><PERSON>", "errors", "displayNames", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "AuthService", "i3", "Router", "selectors", "decls", "vars", "consts", "template", "LoginComponent_Template", "rf", "ctx", "LoginComponent_Template_form_ngSubmit_10_listener", "ɵɵtemplate", "LoginComponent_div_11_Template", "LoginComponent_div_18_Template", "LoginComponent_div_25_Template", "LoginComponent_Template_button_click_33_listener", "LoginComponent__svg_svg_34_Template", "LoginComponent__svg_svg_35_Template", "LoginComponent_div_36_Template", "LoginComponent_span_38_Template", "LoginComponent_span_39_Template", "ɵɵproperty", "ɵɵclassProp", "ɵɵattribute", "invalid"], "sources": ["C:\\Users\\<USER>\\Desktop\\angular\\front\\src\\app\\components\\login\\login.component.ts", "C:\\Users\\<USER>\\Desktop\\angular\\front\\src\\app\\components\\login\\login.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { Router } from '@angular/router';\n\nimport { AuthService } from '../../services/auth.service';\nimport { AuthRequest } from '../../models/auth-request.interface';\n\n@Component({\n  selector: 'app-login',\n  templateUrl: './login.component.html',\n  styleUrls: ['./login.component.css']\n})\nexport class LoginComponent implements OnInit {\n  loginForm!: FormGroup;\n  isLoading = false;\n  errorMessage = '';\n  showPassword = false;\n\n  constructor(\n    private formBuilder: FormBuilder,\n    private authService: AuthService,\n    private router: Router\n  ) {}\n\n  ngOnInit(): void {\n    this.initializeForm();\n    \n    // Rediriger si déjà connecté\n    if (this.authService.isAuthenticated()) {\n      this.router.navigate(['/dashboard']); // Ajustez selon votre route de destination\n    }\n  }\n\n  /**\n   * Initialise le formulaire de connexion avec les validations\n   */\n  private initializeForm(): void {\n    this.loginForm = this.formBuilder.group({\n      agency: ['', [Validators.required, Validators.minLength(2)]],\n      user: ['', [Validators.required, Validators.minLength(2)]],\n      password: ['', [Validators.required, Validators.minLength(6)]]\n    });\n  }\n\n  /**\n   * Getter pour accéder facilement aux contrôles du formulaire\n   */\n  get formControls() {\n    return this.loginForm.controls;\n  }\n\n  /**\n   * Bascule la visibilité du mot de passe\n   */\n  togglePasswordVisibility(): void {\n    this.showPassword = !this.showPassword;\n  }\n\n  /**\n   * Soumet le formulaire de connexion\n   */\n  onSubmit(): void {\n    if (this.loginForm.valid && !this.isLoading) {\n      this.isLoading = true;\n      this.errorMessage = '';\n\n      // Créer l'objet de requête avec les noms de propriétés exacts du backend\n      const authRequest: AuthRequest = {\n        Agency: this.loginForm.value.agency.trim(),\n        User: this.loginForm.value.user.trim(),\n        Password: this.loginForm.value.password\n      };\n\n      this.authService.authenticate(authRequest).subscribe({\n        next: (response) => {\n          this.isLoading = false;\n          \n          if (response.header.success) {\n            // Connexion réussie\n            console.log('Connexion réussie:', response.body.userInfo);\n            \n            // Rediriger vers le dashboard ou la page d'accueil\n            this.router.navigate(['/dashboard']); // Ajustez selon votre route\n          } else {\n            // Erreur retournée par l'API\n            this.handleApiError(response);\n          }\n        },\n        error: (error) => {\n          this.isLoading = false;\n          this.errorMessage = error.message || 'Une erreur est survenue lors de la connexion';\n          console.error('Erreur de connexion:', error);\n        }\n      });\n    } else {\n      // Marquer tous les champs comme touchés pour afficher les erreurs\n      this.markFormGroupTouched();\n    }\n  }\n\n  /**\n   * Gère les erreurs retournées par l'API\n   */\n  private handleApiError(response: any): void {\n    if (response.header.messages && response.header.messages.length > 0) {\n      // Utiliser le premier message d'erreur de l'API\n      this.errorMessage = response.header.messages[0].message;\n    } else {\n      this.errorMessage = 'Échec de l\\'authentification';\n    }\n  }\n\n  /**\n   * Marque tous les champs du formulaire comme touchés\n   */\n  private markFormGroupTouched(): void {\n    Object.keys(this.loginForm.controls).forEach(key => {\n      const control = this.loginForm.get(key);\n      control?.markAsTouched();\n    });\n  }\n\n  /**\n   * Vérifie si un champ a une erreur et a été touché\n   */\n  hasError(fieldName: string, errorType: string): boolean {\n    const field = this.loginForm.get(fieldName);\n    return !!(field?.hasError(errorType) && field?.touched);\n  }\n\n  /**\n   * Récupère le message d'erreur pour un champ\n   */\n  getErrorMessage(fieldName: string): string {\n    const field = this.loginForm.get(fieldName);\n    \n    if (field?.hasError('required')) {\n      return `${this.getFieldDisplayName(fieldName)} est requis`;\n    }\n    \n    if (field?.hasError('minlength')) {\n      const requiredLength = field.errors?.['minlength']?.requiredLength;\n      return `${this.getFieldDisplayName(fieldName)} doit contenir au moins ${requiredLength} caractères`;\n    }\n    \n    return '';\n  }\n\n  /**\n   * Retourne le nom d'affichage pour un champ\n   */\n  private getFieldDisplayName(fieldName: string): string {\n    const displayNames: { [key: string]: string } = {\n      agency: 'Le code agence',\n      user: 'Le code utilisateur',\n      password: 'Le mot de passe'\n    };\n    \n    return displayNames[fieldName] || fieldName;\n  }\n\n  /**\n   * Nettoie le message d'erreur\n   */\n  clearError(): void {\n    this.errorMessage = '';\n  }\n}\n", "<div class=\"login-container\">\n  <div class=\"login-card\">\n    <!-- Header -->\n    <div class=\"login-header\">\n      <div class=\"logo-container\">\n        <h1 class=\"login-title\">Paximum</h1>\n        <div class=\"logo-subtitle\">Système d'authentification</div>\n      </div>\n      <p class=\"login-subtitle\">Connectez-vous à votre compte</p>\n    </div>\n\n    <!-- Formulaire -->\n    <form [formGroup]=\"loginForm\" (ngSubmit)=\"onSubmit()\" class=\"login-form\" novalidate>\n      \n      <!-- Message d'erreur global -->\n      <div *ngIf=\"errorMessage\" class=\"error-message\" role=\"alert\">\n        <svg class=\"error-icon\" fill=\"currentColor\" viewBox=\"0 0 20 20\" aria-hidden=\"true\">\n          <path fill-rule=\"evenodd\" d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z\" clip-rule=\"evenodd\"></path>\n        </svg>\n        <span>{{ errorMessage }}</span>\n        <button type=\"button\" class=\"error-close\" (click)=\"clearError()\" aria-label=\"Fermer le message d'erreur\">\n          <svg fill=\"currentColor\" viewBox=\"0 0 20 20\">\n            <path fill-rule=\"evenodd\" d=\"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z\" clip-rule=\"evenodd\"></path>\n          </svg>\n        </button>\n      </div>\n\n      <!-- Champ Code Agence -->\n      <div class=\"form-group\">\n        <label for=\"agency\" class=\"form-label\">\n          <svg class=\"label-icon\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4\"></path>\n          </svg>\n          Code Agence\n        </label>\n        <input\n          type=\"text\"\n          id=\"agency\"\n          formControlName=\"agency\"\n          class=\"form-input\"\n          [class.error]=\"hasError('agency', 'required') || hasError('agency', 'minlength')\"\n          placeholder=\"Entrez le code de votre agence\"\n          autocomplete=\"organization\"\n          maxlength=\"50\"\n        >\n        <div *ngIf=\"formControls['agency'].touched && formControls['agency'].errors\" class=\"field-error\">\n          {{ getErrorMessage('agency') }}\n        </div>\n      </div>\n\n      <!-- Champ Code Utilisateur -->\n      <div class=\"form-group\">\n        <label for=\"user\" class=\"form-label\">\n          <svg class=\"label-icon\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\"></path>\n          </svg>\n          Code Utilisateur\n        </label>\n        <input\n          type=\"text\"\n          id=\"user\"\n          formControlName=\"user\"\n          class=\"form-input\"\n          [class.error]=\"hasError('user', 'required') || hasError('user', 'minlength')\"\n          placeholder=\"Entrez votre code utilisateur\"\n          autocomplete=\"username\"\n          maxlength=\"50\"\n        >\n        <div *ngIf=\"formControls['user'].touched && formControls['user'].errors\" class=\"field-error\">\n          {{ getErrorMessage('user') }}\n        </div>\n      </div>\n\n      <!-- Champ Mot de passe -->\n      <div class=\"form-group\">\n        <label for=\"password\" class=\"form-label\">\n          <svg class=\"label-icon\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z\"></path>\n          </svg>\n          Mot de passe\n        </label>\n        <div class=\"password-input-container\">\n          <input\n            [type]=\"showPassword ? 'text' : 'password'\"\n            id=\"password\"\n            formControlName=\"password\"\n            class=\"form-input password-input\"\n            [class.error]=\"hasError('password', 'required') || hasError('password', 'minlength')\"\n            placeholder=\"Entrez votre mot de passe\"\n            autocomplete=\"current-password\"\n            maxlength=\"100\"\n          >\n          <button\n            type=\"button\"\n            class=\"password-toggle\"\n            (click)=\"togglePasswordVisibility()\"\n            [attr.aria-label]=\"showPassword ? 'Masquer le mot de passe' : 'Afficher le mot de passe'\"\n            tabindex=\"0\"\n          >\n            <svg *ngIf=\"!showPassword\" class=\"password-icon\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"></path>\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z\"></path>\n            </svg>\n            <svg *ngIf=\"showPassword\" class=\"password-icon\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21\"></path>\n            </svg>\n          </button>\n        </div>\n        <div *ngIf=\"formControls['password'].touched && formControls['password'].errors\" class=\"field-error\">\n          {{ getErrorMessage('password') }}\n        </div>\n      </div>\n\n      <!-- Bouton de connexion -->\n      <button\n        type=\"submit\"\n        class=\"login-button\"\n        [disabled]=\"isLoading || loginForm.invalid\"\n        [class.loading]=\"isLoading\"\n      >\n        <span *ngIf=\"!isLoading\" class=\"button-content\">\n          <svg class=\"button-icon\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1\"></path>\n          </svg>\n          Se connecter\n        </span>\n        <span *ngIf=\"isLoading\" class=\"loading-content\">\n          <svg class=\"loading-spinner\" viewBox=\"0 0 24 24\">\n            <circle cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" stroke-width=\"4\" fill=\"none\" opacity=\"0.25\"></circle>\n            <path fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\" opacity=\"0.75\"></path>\n          </svg>\n          Connexion en cours...\n        </span>\n      </button>\n    </form>\n\n    <!-- Footer -->\n    <div class=\"login-footer\">\n      <p class=\"footer-text\">\n        Besoin d'aide ? \n        <a href=\"mailto:<EMAIL>\" class=\"footer-link\">Contactez le support</a>\n      </p>\n      <div class=\"version-info\">\n        Version 1.0.0\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": "AACA,SAAiCA,UAAU,QAAQ,gBAAgB;;;;;;;;;ICc7DC,EAAA,CAAAC,cAAA,cAA6D;IAC3DD,EAAA,CAAAE,cAAA,EAAmF;IAAnFF,EAAA,CAAAC,cAAA,cAAmF;IACjFD,EAAA,CAAAG,SAAA,eAA2K;IAC7KH,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAK,eAAA,EAAM;IAANL,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAM,MAAA,GAAkB;IAAAN,EAAA,CAAAI,YAAA,EAAO;IAC/BJ,EAAA,CAAAC,cAAA,iBAAyG;IAA/DD,EAAA,CAAAO,UAAA,mBAAAC,uDAAA;MAAAR,EAAA,CAAAS,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAX,EAAA,CAAAY,aAAA;MAAA,OAASZ,EAAA,CAAAa,WAAA,CAAAF,MAAA,CAAAG,UAAA,EAAY;IAAA,EAAC;IAC9Dd,EAAA,CAAAE,cAAA,EAA6C;IAA7CF,EAAA,CAAAC,cAAA,cAA6C;IAC3CD,EAAA,CAAAG,SAAA,eAA4P;IAC9PH,EAAA,CAAAI,YAAA,EAAM;;;;IAJFJ,EAAA,CAAAe,SAAA,GAAkB;IAAlBf,EAAA,CAAAgB,iBAAA,CAAAC,MAAA,CAAAC,YAAA,CAAkB;;;;;IA0BxBlB,EAAA,CAAAC,cAAA,cAAiG;IAC/FD,EAAA,CAAAM,MAAA,GACF;IAAAN,EAAA,CAAAI,YAAA,EAAM;;;;IADJJ,EAAA,CAAAe,SAAA,GACF;IADEf,EAAA,CAAAmB,kBAAA,MAAAC,MAAA,CAAAC,eAAA,gBACF;;;;;IAqBArB,EAAA,CAAAC,cAAA,cAA6F;IAC3FD,EAAA,CAAAM,MAAA,GACF;IAAAN,EAAA,CAAAI,YAAA,EAAM;;;;IADJJ,EAAA,CAAAe,SAAA,GACF;IADEf,EAAA,CAAAmB,kBAAA,MAAAG,MAAA,CAAAD,eAAA,cACF;;;;;IA6BIrB,EAAA,CAAAE,cAAA,EAAuG;IAAvGF,EAAA,CAAAC,cAAA,cAAuG;IACrGD,EAAA,CAAAG,SAAA,eAAkH;IAEpHH,EAAA,CAAAI,YAAA,EAAM;;;;;IACNJ,EAAA,CAAAE,cAAA,EAAsG;IAAtGF,EAAA,CAAAC,cAAA,cAAsG;IACpGD,EAAA,CAAAG,SAAA,eAAqQ;IACvQH,EAAA,CAAAI,YAAA,EAAM;;;;;IAGVJ,EAAA,CAAAC,cAAA,cAAqG;IACnGD,EAAA,CAAAM,MAAA,GACF;IAAAN,EAAA,CAAAI,YAAA,EAAM;;;;IADJJ,EAAA,CAAAe,SAAA,GACF;IADEf,EAAA,CAAAmB,kBAAA,MAAAI,MAAA,CAAAF,eAAA,kBACF;;;;;IAUArB,EAAA,CAAAC,cAAA,eAAgD;IAC9CD,EAAA,CAAAE,cAAA,EAA+E;IAA/EF,EAAA,CAAAC,cAAA,cAA+E;IAC7ED,EAAA,CAAAG,SAAA,eAA8K;IAChLH,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAM,MAAA,qBACF;IAAAN,EAAA,CAAAI,YAAA,EAAO;;;;;IACPJ,EAAA,CAAAC,cAAA,eAAgD;IAC9CD,EAAA,CAAAE,cAAA,EAAiD;IAAjDF,EAAA,CAAAC,cAAA,cAAiD;IAC/CD,EAAA,CAAAG,SAAA,iBAA0G;IAE5GH,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAM,MAAA,8BACF;IAAAN,EAAA,CAAAI,YAAA,EAAO;;;ADxHf,OAAM,MAAOoB,cAAc;EAMzBC,YACUC,WAAwB,EACxBC,WAAwB,EACxBC,MAAc;IAFd,KAAAF,WAAW,GAAXA,WAAW;IACX,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IAPhB,KAAAC,SAAS,GAAG,KAAK;IACjB,KAAAX,YAAY,GAAG,EAAE;IACjB,KAAAY,YAAY,GAAG,KAAK;EAMjB;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,cAAc,EAAE;IAErB;IACA,IAAI,IAAI,CAACL,WAAW,CAACM,eAAe,EAAE,EAAE;MACtC,IAAI,CAACL,MAAM,CAACM,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;;EAE1C;EAEA;;;EAGQF,cAAcA,CAAA;IACpB,IAAI,CAACG,SAAS,GAAG,IAAI,CAACT,WAAW,CAACU,KAAK,CAAC;MACtCC,MAAM,EAAE,CAAC,EAAE,EAAE,CAACtC,UAAU,CAACuC,QAAQ,EAAEvC,UAAU,CAACwC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5DC,IAAI,EAAE,CAAC,EAAE,EAAE,CAACzC,UAAU,CAACuC,QAAQ,EAAEvC,UAAU,CAACwC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1DE,QAAQ,EAAE,CAAC,EAAE,EAAE,CAAC1C,UAAU,CAACuC,QAAQ,EAAEvC,UAAU,CAACwC,SAAS,CAAC,CAAC,CAAC,CAAC;KAC9D,CAAC;EACJ;EAEA;;;EAGA,IAAIG,YAAYA,CAAA;IACd,OAAO,IAAI,CAACP,SAAS,CAACQ,QAAQ;EAChC;EAEA;;;EAGAC,wBAAwBA,CAAA;IACtB,IAAI,CAACd,YAAY,GAAG,CAAC,IAAI,CAACA,YAAY;EACxC;EAEA;;;EAGAe,QAAQA,CAAA;IACN,IAAI,IAAI,CAACV,SAAS,CAACW,KAAK,IAAI,CAAC,IAAI,CAACjB,SAAS,EAAE;MAC3C,IAAI,CAACA,SAAS,GAAG,IAAI;MACrB,IAAI,CAACX,YAAY,GAAG,EAAE;MAEtB;MACA,MAAM6B,WAAW,GAAgB;QAC/BC,MAAM,EAAE,IAAI,CAACb,SAAS,CAACc,KAAK,CAACZ,MAAM,CAACa,IAAI,EAAE;QAC1CC,IAAI,EAAE,IAAI,CAAChB,SAAS,CAACc,KAAK,CAACT,IAAI,CAACU,IAAI,EAAE;QACtCE,QAAQ,EAAE,IAAI,CAACjB,SAAS,CAACc,KAAK,CAACR;OAChC;MAED,IAAI,CAACd,WAAW,CAAC0B,YAAY,CAACN,WAAW,CAAC,CAACO,SAAS,CAAC;QACnDC,IAAI,EAAGC,QAAQ,IAAI;UACjB,IAAI,CAAC3B,SAAS,GAAG,KAAK;UAEtB,IAAI2B,QAAQ,CAACC,MAAM,CAACC,OAAO,EAAE;YAC3B;YACAC,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEJ,QAAQ,CAACK,IAAI,CAACC,QAAQ,CAAC;YAEzD;YACA,IAAI,CAAClC,MAAM,CAACM,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;WACvC,MAAM;YACL;YACA,IAAI,CAAC6B,cAAc,CAACP,QAAQ,CAAC;;QAEjC,CAAC;QACDQ,KAAK,EAAGA,KAAK,IAAI;UACf,IAAI,CAACnC,SAAS,GAAG,KAAK;UACtB,IAAI,CAACX,YAAY,GAAG8C,KAAK,CAACC,OAAO,IAAI,8CAA8C;UACnFN,OAAO,CAACK,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;QAC9C;OACD,CAAC;KACH,MAAM;MACL;MACA,IAAI,CAACE,oBAAoB,EAAE;;EAE/B;EAEA;;;EAGQH,cAAcA,CAACP,QAAa;IAClC,IAAIA,QAAQ,CAACC,MAAM,CAACU,QAAQ,IAAIX,QAAQ,CAACC,MAAM,CAACU,QAAQ,CAACC,MAAM,GAAG,CAAC,EAAE;MACnE;MACA,IAAI,CAAClD,YAAY,GAAGsC,QAAQ,CAACC,MAAM,CAACU,QAAQ,CAAC,CAAC,CAAC,CAACF,OAAO;KACxD,MAAM;MACL,IAAI,CAAC/C,YAAY,GAAG,8BAA8B;;EAEtD;EAEA;;;EAGQgD,oBAAoBA,CAAA;IAC1BG,MAAM,CAACC,IAAI,CAAC,IAAI,CAACnC,SAAS,CAACQ,QAAQ,CAAC,CAAC4B,OAAO,CAACC,GAAG,IAAG;MACjD,MAAMC,OAAO,GAAG,IAAI,CAACtC,SAAS,CAACuC,GAAG,CAACF,GAAG,CAAC;MACvCC,OAAO,EAAEE,aAAa,EAAE;IAC1B,CAAC,CAAC;EACJ;EAEA;;;EAGAC,QAAQA,CAACC,SAAiB,EAAEC,SAAiB;IAC3C,MAAMC,KAAK,GAAG,IAAI,CAAC5C,SAAS,CAACuC,GAAG,CAACG,SAAS,CAAC;IAC3C,OAAO,CAAC,EAAEE,KAAK,EAAEH,QAAQ,CAACE,SAAS,CAAC,IAAIC,KAAK,EAAEC,OAAO,CAAC;EACzD;EAEA;;;EAGA3D,eAAeA,CAACwD,SAAiB;IAC/B,MAAME,KAAK,GAAG,IAAI,CAAC5C,SAAS,CAACuC,GAAG,CAACG,SAAS,CAAC;IAE3C,IAAIE,KAAK,EAAEH,QAAQ,CAAC,UAAU,CAAC,EAAE;MAC/B,OAAO,GAAG,IAAI,CAACK,mBAAmB,CAACJ,SAAS,CAAC,aAAa;;IAG5D,IAAIE,KAAK,EAAEH,QAAQ,CAAC,WAAW,CAAC,EAAE;MAChC,MAAMM,cAAc,GAAGH,KAAK,CAACI,MAAM,GAAG,WAAW,CAAC,EAAED,cAAc;MAClE,OAAO,GAAG,IAAI,CAACD,mBAAmB,CAACJ,SAAS,CAAC,2BAA2BK,cAAc,aAAa;;IAGrG,OAAO,EAAE;EACX;EAEA;;;EAGQD,mBAAmBA,CAACJ,SAAiB;IAC3C,MAAMO,YAAY,GAA8B;MAC9C/C,MAAM,EAAE,gBAAgB;MACxBG,IAAI,EAAE,qBAAqB;MAC3BC,QAAQ,EAAE;KACX;IAED,OAAO2C,YAAY,CAACP,SAAS,CAAC,IAAIA,SAAS;EAC7C;EAEA;;;EAGA/D,UAAUA,CAAA;IACR,IAAI,CAACI,YAAY,GAAG,EAAE;EACxB;;;uBA1JWM,cAAc,EAAAxB,EAAA,CAAAqF,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAvF,EAAA,CAAAqF,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAAzF,EAAA,CAAAqF,iBAAA,CAAAK,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAdnE,cAAc;MAAAoE,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCZ3BlG,EAAA,CAAAC,cAAA,aAA6B;UAKGD,EAAA,CAAAM,MAAA,cAAO;UAAAN,EAAA,CAAAI,YAAA,EAAK;UACpCJ,EAAA,CAAAC,cAAA,aAA2B;UAAAD,EAAA,CAAAM,MAAA,sCAA0B;UAAAN,EAAA,CAAAI,YAAA,EAAM;UAE7DJ,EAAA,CAAAC,cAAA,WAA0B;UAAAD,EAAA,CAAAM,MAAA,yCAA6B;UAAAN,EAAA,CAAAI,YAAA,EAAI;UAI7DJ,EAAA,CAAAC,cAAA,eAAoF;UAAtDD,EAAA,CAAAO,UAAA,sBAAA6F,kDAAA;YAAA,OAAYD,GAAA,CAAAtD,QAAA,EAAU;UAAA,EAAC;UAGnD7C,EAAA,CAAAqG,UAAA,KAAAC,8BAAA,iBAUM;UAGNtG,EAAA,CAAAC,cAAA,cAAwB;UAEpBD,EAAA,CAAAE,cAAA,EAA8E;UAA9EF,EAAA,CAAAC,cAAA,eAA8E;UAC5ED,EAAA,CAAAG,SAAA,gBAA2N;UAC7NH,EAAA,CAAAI,YAAA,EAAM;UACNJ,EAAA,CAAAM,MAAA,qBACF;UAAAN,EAAA,CAAAI,YAAA,EAAQ;UACRJ,EAAA,CAAAK,eAAA,EASC;UATDL,EAAA,CAAAG,SAAA,iBASC;UACDH,EAAA,CAAAqG,UAAA,KAAAE,8BAAA,kBAEM;UACRvG,EAAA,CAAAI,YAAA,EAAM;UAGNJ,EAAA,CAAAC,cAAA,cAAwB;UAEpBD,EAAA,CAAAE,cAAA,EAA8E;UAA9EF,EAAA,CAAAC,cAAA,eAA8E;UAC5ED,EAAA,CAAAG,SAAA,gBAAqJ;UACvJH,EAAA,CAAAI,YAAA,EAAM;UACNJ,EAAA,CAAAM,MAAA,0BACF;UAAAN,EAAA,CAAAI,YAAA,EAAQ;UACRJ,EAAA,CAAAK,eAAA,EASC;UATDL,EAAA,CAAAG,SAAA,iBASC;UACDH,EAAA,CAAAqG,UAAA,KAAAG,8BAAA,kBAEM;UACRxG,EAAA,CAAAI,YAAA,EAAM;UAGNJ,EAAA,CAAAC,cAAA,cAAwB;UAEpBD,EAAA,CAAAE,cAAA,EAA8E;UAA9EF,EAAA,CAAAC,cAAA,eAA8E;UAC5ED,EAAA,CAAAG,SAAA,gBAAsL;UACxLH,EAAA,CAAAI,YAAA,EAAM;UACNJ,EAAA,CAAAM,MAAA,sBACF;UAAAN,EAAA,CAAAI,YAAA,EAAQ;UACRJ,EAAA,CAAAK,eAAA,EAAsC;UAAtCL,EAAA,CAAAC,cAAA,eAAsC;UACpCD,EAAA,CAAAG,SAAA,iBASC;UACDH,EAAA,CAAAC,cAAA,kBAMC;UAHCD,EAAA,CAAAO,UAAA,mBAAAkG,iDAAA;YAAA,OAASN,GAAA,CAAAvD,wBAAA,EAA0B;UAAA,EAAC;UAIpC5C,EAAA,CAAAqG,UAAA,KAAAK,mCAAA,kBAGM;UACN1G,EAAA,CAAAqG,UAAA,KAAAM,mCAAA,kBAEM;UACR3G,EAAA,CAAAI,YAAA,EAAS;UAEXJ,EAAA,CAAAqG,UAAA,KAAAO,8BAAA,kBAEM;UACR5G,EAAA,CAAAI,YAAA,EAAM;UAGNJ,EAAA,CAAAC,cAAA,kBAKC;UACCD,EAAA,CAAAqG,UAAA,KAAAQ,+BAAA,mBAKO;UACP7G,EAAA,CAAAqG,UAAA,KAAAS,+BAAA,mBAMO;UACT9G,EAAA,CAAAI,YAAA,EAAS;UAIXJ,EAAA,CAAAC,cAAA,eAA0B;UAEtBD,EAAA,CAAAM,MAAA,yBACA;UAAAN,EAAA,CAAAC,cAAA,aAAyD;UAAAD,EAAA,CAAAM,MAAA,4BAAoB;UAAAN,EAAA,CAAAI,YAAA,EAAI;UAEnFJ,EAAA,CAAAC,cAAA,eAA0B;UACxBD,EAAA,CAAAM,MAAA,uBACF;UAAAN,EAAA,CAAAI,YAAA,EAAM;;;UApIFJ,EAAA,CAAAe,SAAA,IAAuB;UAAvBf,EAAA,CAAA+G,UAAA,cAAAZ,GAAA,CAAAhE,SAAA,CAAuB;UAGrBnC,EAAA,CAAAe,SAAA,GAAkB;UAAlBf,EAAA,CAAA+G,UAAA,SAAAZ,GAAA,CAAAjF,YAAA,CAAkB;UAyBpBlB,EAAA,CAAAe,SAAA,GAAiF;UAAjFf,EAAA,CAAAgH,WAAA,UAAAb,GAAA,CAAAvB,QAAA,0BAAAuB,GAAA,CAAAvB,QAAA,wBAAiF;UAK7E5E,EAAA,CAAAe,SAAA,GAAqE;UAArEf,EAAA,CAAA+G,UAAA,SAAAZ,GAAA,CAAAzD,YAAA,WAAAsC,OAAA,IAAAmB,GAAA,CAAAzD,YAAA,WAAAyC,MAAA,CAAqE;UAkBzEnF,EAAA,CAAAe,SAAA,GAA6E;UAA7Ef,EAAA,CAAAgH,WAAA,UAAAb,GAAA,CAAAvB,QAAA,wBAAAuB,GAAA,CAAAvB,QAAA,sBAA6E;UAKzE5E,EAAA,CAAAe,SAAA,GAAiE;UAAjEf,EAAA,CAAA+G,UAAA,SAAAZ,GAAA,CAAAzD,YAAA,SAAAsC,OAAA,IAAAmB,GAAA,CAAAzD,YAAA,SAAAyC,MAAA,CAAiE;UAmBnEnF,EAAA,CAAAe,SAAA,GAAqF;UAArFf,EAAA,CAAAgH,WAAA,UAAAb,GAAA,CAAAvB,QAAA,4BAAAuB,GAAA,CAAAvB,QAAA,0BAAqF;UAJrF5E,EAAA,CAAA+G,UAAA,SAAAZ,GAAA,CAAArE,YAAA,uBAA2C;UAa3C9B,EAAA,CAAAe,SAAA,GAAyF;UAAzFf,EAAA,CAAAiH,WAAA,eAAAd,GAAA,CAAArE,YAAA,0DAAyF;UAGnF9B,EAAA,CAAAe,SAAA,GAAmB;UAAnBf,EAAA,CAAA+G,UAAA,UAAAZ,GAAA,CAAArE,YAAA,CAAmB;UAInB9B,EAAA,CAAAe,SAAA,GAAkB;UAAlBf,EAAA,CAAA+G,UAAA,SAAAZ,GAAA,CAAArE,YAAA,CAAkB;UAKtB9B,EAAA,CAAAe,SAAA,GAAyE;UAAzEf,EAAA,CAAA+G,UAAA,SAAAZ,GAAA,CAAAzD,YAAA,aAAAsC,OAAA,IAAAmB,GAAA,CAAAzD,YAAA,aAAAyC,MAAA,CAAyE;UAU/EnF,EAAA,CAAAe,SAAA,GAA2B;UAA3Bf,EAAA,CAAAgH,WAAA,YAAAb,GAAA,CAAAtE,SAAA,CAA2B;UAD3B7B,EAAA,CAAA+G,UAAA,aAAAZ,GAAA,CAAAtE,SAAA,IAAAsE,GAAA,CAAAhE,SAAA,CAAA+E,OAAA,CAA2C;UAGpClH,EAAA,CAAAe,SAAA,GAAgB;UAAhBf,EAAA,CAAA+G,UAAA,UAAAZ,GAAA,CAAAtE,SAAA,CAAgB;UAMhB7B,EAAA,CAAAe,SAAA,GAAe;UAAff,EAAA,CAAA+G,UAAA,SAAAZ,GAAA,CAAAtE,SAAA,CAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}