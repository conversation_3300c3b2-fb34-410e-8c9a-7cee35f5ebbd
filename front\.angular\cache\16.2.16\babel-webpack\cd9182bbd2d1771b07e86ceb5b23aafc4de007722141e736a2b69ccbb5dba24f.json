{"ast": null, "code": "/**\n * Interfaces pour les requêtes de recherche de vols\n * Correspondant aux modèles du backend Spring Boot\n */\n// Enums et constantes\nexport var PassengerType;\n(function (PassengerType) {\n  PassengerType[PassengerType[\"ADULT\"] = 1] = \"ADULT\";\n  PassengerType[PassengerType[\"CHILD\"] = 2] = \"CHILD\";\n  PassengerType[PassengerType[\"INFANT\"] = 3] = \"INFANT\";\n})(PassengerType || (PassengerType = {}));\nexport var LocationType;\n(function (LocationType) {\n  LocationType[LocationType[\"AIRPORT\"] = 1] = \"AIRPORT\";\n  LocationType[LocationType[\"CITY\"] = 2] = \"CITY\";\n  LocationType[LocationType[\"COUNTRY\"] = 3] = \"COUNTRY\";\n})(LocationType || (LocationType = {}));\nexport var FlightClass;\n(function (FlightClass) {\n  FlightClass[FlightClass[\"ECONOMY\"] = 1] = \"ECONOMY\";\n  FlightClass[FlightClass[\"PREMIUM_ECONOMY\"] = 2] = \"PREMIUM_ECONOMY\";\n  FlightClass[FlightClass[\"BUSINESS\"] = 3] = \"BUSINESS\";\n  FlightClass[FlightClass[\"FIRST\"] = 4] = \"FIRST\";\n})(FlightClass || (FlightClass = {}));\nexport var ProductType;\n(function (ProductType) {\n  ProductType[ProductType[\"FLIGHT\"] = 2] = \"FLIGHT\";\n})(ProductType || (ProductType = {}));", "map": {"version": 3, "names": ["PassengerType", "LocationType", "FlightClass", "ProductType"], "sources": ["C:\\Users\\<USER>\\Desktop\\angular\\front\\src\\app\\models\\flight-search.interface.ts"], "sourcesContent": ["/**\n * Interfaces pour les requêtes de recherche de vols\n * Correspondant aux modèles du backend Spring Boot\n */\n\n// Interfaces communes\nexport interface Location {\n  id: string;\n  type: number;\n  provider?: number;\n}\n\nexport interface Passenger {\n  type: number;\n  count: number;\n}\n\nexport interface CorporateRule {\n  Airline: string;\n  Supplier: string;\n}\n\nexport interface CorporateCode {\n  Code: string;\n  Rule: CorporateRule;\n}\n\nexport interface GetOptionsParameters {\n  flightBaggageGetOption: number;\n}\n\nexport interface AdditionalParameters {\n  getOptionsParameters?: GetOptionsParameters;\n  CorporateCodes?: CorporateCode[];\n}\n\n// Interface pour les requêtes One Way\nexport interface OneWayRequest {\n  ProductType: number;\n  ServiceTypes: string[];\n  CheckIn: string;\n  DepartureLocations: Location[];\n  ArrivalLocations: Location[];\n  Passengers: Passenger[];\n  showOnlyNonStopFlight: boolean;\n  additionalParameters?: AdditionalParameters;\n  acceptPendingProviders: boolean;\n  forceFlightBundlePackage: boolean;\n  disablePackageOfferTotalPrice: boolean;\n  calculateFlightFees: boolean;\n  flightClasses?: number[];\n  Culture: string;\n  Currency: string;\n}\n\n// Interface pour les requêtes Round Trip\nexport interface RoundTripRequest {\n  ProductType: number;\n  ServiceTypes: string[];\n  DepartureLocations: Location[];\n  ArrivalLocations: Location[];\n  CheckIn: string;\n  Night: number;\n  Passengers: Passenger[];\n  acceptPendingProviders: boolean;\n  forceFlightBundlePackage: boolean;\n  disablePackageOfferTotalPrice: boolean;\n  supportedFlightReponseListTypes?: number[];\n  showOnlyNonStopFlight: boolean;\n  additionalParameters?: AdditionalParameters;\n  calculateFlightFees: boolean;\n  Culture: string;\n  Currency: string;\n}\n\n// Interface pour les requêtes Multi-city\nexport interface MulticityRequest {\n  serviceTypes: string[];\n  productType: number;\n  arrivalLocations: Location[];\n  departureLocations: Location[];\n  passengers: Passenger[];\n  checkIns: string[];\n  calculateFlightFees: boolean;\n  acceptPendingProviders: boolean;\n  additionalParameters?: AdditionalParameters;\n  forceFlightBundlePackage: boolean;\n  disablePackageOfferTotalPrice: boolean;\n  showOnlyNonStopFlight: boolean;\n  supportedFlightReponseListTypes?: number[];\n  culture: string;\n  currency: string;\n}\n\n// Interfaces pour les réponses (structure de base)\nexport interface FlightSearchHeader {\n  requestId: string;\n  success: boolean;\n  messages: FlightSearchMessage[];\n}\n\nexport interface FlightSearchMessage {\n  id: number;\n  code: string;\n  messageType: number;\n  message: string;\n}\n\nexport interface FlightSearchResponse {\n  header: FlightSearchHeader;\n  body: any; // À définir selon la structure exacte de votre API\n}\n\n// Types pour les réponses spécifiques\nexport interface OneWayResponse extends FlightSearchResponse {}\nexport interface RoundTripResponse extends FlightSearchResponse {}\nexport interface MulticityResponse extends FlightSearchResponse {}\n\n// Enums et constantes\nexport enum PassengerType {\n  ADULT = 1,\n  CHILD = 2,\n  INFANT = 3\n}\n\nexport enum LocationType {\n  AIRPORT = 1,\n  CITY = 2,\n  COUNTRY = 3\n}\n\nexport enum FlightClass {\n  ECONOMY = 1,\n  PREMIUM_ECONOMY = 2,\n  BUSINESS = 3,\n  FIRST = 4\n}\n\nexport enum ProductType {\n  FLIGHT = 2\n}\n\n// Interface pour le formulaire de recherche\nexport interface FlightSearchForm {\n  tripType: 'oneWay' | 'roundTrip' | 'multiCity';\n  departureLocation: string;\n  arrivalLocation: string;\n  departureDate: string;\n  returnDate?: string;\n  passengers: {\n    adults: number;\n    children: number;\n    infants: number;\n  };\n  flightClass: FlightClass;\n  directFlightsOnly: boolean;\n  preferredAirline?: string;\n}\n"], "mappings": "AAAA;;;;AAsHA;AACA,WAAYA,aAIX;AAJD,WAAYA,aAAa;EACvBA,aAAA,CAAAA,aAAA,wBAAS;EACTA,aAAA,CAAAA,aAAA,wBAAS;EACTA,aAAA,CAAAA,aAAA,0BAAU;AACZ,CAAC,EAJWA,aAAa,KAAbA,aAAa;AAMzB,WAAYC,YAIX;AAJD,WAAYA,YAAY;EACtBA,YAAA,CAAAA,YAAA,4BAAW;EACXA,YAAA,CAAAA,YAAA,sBAAQ;EACRA,YAAA,CAAAA,YAAA,4BAAW;AACb,CAAC,EAJWA,YAAY,KAAZA,YAAY;AAMxB,WAAYC,WAKX;AALD,WAAYA,WAAW;EACrBA,WAAA,CAAAA,WAAA,4BAAW;EACXA,WAAA,CAAAA,WAAA,4CAAmB;EACnBA,WAAA,CAAAA,WAAA,8BAAY;EACZA,WAAA,CAAAA,WAAA,wBAAS;AACX,CAAC,EALWA,WAAW,KAAXA,WAAW;AAOvB,WAAYC,WAEX;AAFD,WAAYA,WAAW;EACrBA,WAAA,CAAAA,WAAA,0BAAU;AACZ,CAAC,EAFWA,WAAW,KAAXA,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}