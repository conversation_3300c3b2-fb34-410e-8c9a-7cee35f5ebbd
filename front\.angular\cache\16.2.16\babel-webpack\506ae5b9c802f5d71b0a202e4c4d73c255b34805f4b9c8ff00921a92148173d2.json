{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../services/auth.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/common\";\nfunction LoginComponent_div_33_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 64);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 65);\n    i0.ɵɵelement(2, \"path\", 66);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 67);\n    i0.ɵɵlistener(\"click\", function LoginComponent_div_33_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r7 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r7.clearError());\n    });\n    i0.ɵɵtext(6, \"\\u00D7\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r0.errorMessage);\n  }\n}\nfunction LoginComponent_div_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 68);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getErrorMessage(\"agency\"), \" \");\n  }\n}\nfunction LoginComponent__svg_svg_51_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(0, \"svg\", 69);\n    i0.ɵɵelement(1, \"path\", 70)(2, \"path\", 71);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent__svg_svg_52_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(0, \"svg\", 69);\n    i0.ɵɵelement(1, \"path\", 72);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_div_53_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 68);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r4.getErrorMessage(\"password\"), \" \");\n  }\n}\nfunction LoginComponent_span_58_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"LOGIN\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_span_59_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 73);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 74);\n    i0.ɵɵelement(2, \"circle\", 75)(3, \"path\", 76);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" Loading... \");\n    i0.ɵɵelementEnd();\n  }\n}\nexport class LoginComponent {\n  constructor(formBuilder, authService, router) {\n    this.formBuilder = formBuilder;\n    this.authService = authService;\n    this.router = router;\n    this.isLoading = false;\n    this.errorMessage = '';\n    this.showPassword = false;\n  }\n  ngOnInit() {\n    this.initializeForm();\n    // Rediriger si déjà connecté\n    if (this.authService.isAuthenticated()) {\n      this.router.navigate(['/dashboard']); // Ajustez selon votre route de destination\n    }\n  }\n  /**\n   * Initialise le formulaire de connexion avec les validations\n   */\n  initializeForm() {\n    this.loginForm = this.formBuilder.group({\n      agency: ['', [Validators.required, Validators.minLength(2)]],\n      user: ['', [Validators.required, Validators.minLength(2)]],\n      password: ['', [Validators.required, Validators.minLength(6)]]\n    });\n  }\n  /**\n   * Getter pour accéder facilement aux contrôles du formulaire\n   */\n  get formControls() {\n    return this.loginForm.controls;\n  }\n  /**\n   * Bascule la visibilité du mot de passe\n   */\n  togglePasswordVisibility() {\n    this.showPassword = !this.showPassword;\n  }\n  /**\n   * Soumet le formulaire de connexion\n   */\n  onSubmit() {\n    if (this.loginForm.valid && !this.isLoading) {\n      this.isLoading = true;\n      this.errorMessage = '';\n      // Créer l'objet de requête avec les noms de propriétés exacts du backend\n      const authRequest = {\n        Agency: this.loginForm.value.agency.trim(),\n        User: this.loginForm.value.user.trim(),\n        Password: this.loginForm.value.password\n      };\n      this.authService.authenticate(authRequest).subscribe({\n        next: response => {\n          this.isLoading = false;\n          if (response.header.success) {\n            // Connexion réussie\n            console.log('Connexion réussie:', response.body.userInfo);\n            // Rediriger vers le dashboard ou la page d'accueil\n            this.router.navigate(['/dashboard']); // Ajustez selon votre route\n          } else {\n            // Erreur retournée par l'API\n            this.handleApiError(response);\n          }\n        },\n        error: error => {\n          this.isLoading = false;\n          this.errorMessage = error.message || 'Une erreur est survenue lors de la connexion';\n          console.error('Erreur de connexion:', error);\n        }\n      });\n    } else {\n      // Marquer tous les champs comme touchés pour afficher les erreurs\n      this.markFormGroupTouched();\n    }\n  }\n  /**\n   * Gère les erreurs retournées par l'API\n   */\n  handleApiError(response) {\n    if (response.header.messages && response.header.messages.length > 0) {\n      // Utiliser le premier message d'erreur de l'API\n      this.errorMessage = response.header.messages[0].message;\n    } else {\n      this.errorMessage = 'Échec de l\\'authentification';\n    }\n  }\n  /**\n   * Marque tous les champs du formulaire comme touchés\n   */\n  markFormGroupTouched() {\n    Object.keys(this.loginForm.controls).forEach(key => {\n      const control = this.loginForm.get(key);\n      control?.markAsTouched();\n    });\n  }\n  /**\n   * Vérifie si un champ a une erreur et a été touché\n   */\n  hasError(fieldName, errorType) {\n    const field = this.loginForm.get(fieldName);\n    return !!(field?.hasError(errorType) && field?.touched);\n  }\n  /**\n   * Récupère le message d'erreur pour un champ\n   */\n  getErrorMessage(fieldName) {\n    const field = this.loginForm.get(fieldName);\n    if (field?.hasError('required')) {\n      return `${this.getFieldDisplayName(fieldName)} est requis`;\n    }\n    if (field?.hasError('minlength')) {\n      const requiredLength = field.errors?.['minlength']?.requiredLength;\n      return `${this.getFieldDisplayName(fieldName)} doit contenir au moins ${requiredLength} caractères`;\n    }\n    return '';\n  }\n  /**\n   * Retourne le nom d'affichage pour un champ\n   */\n  getFieldDisplayName(fieldName) {\n    const displayNames = {\n      agency: 'Le code agence',\n      user: 'Le code utilisateur',\n      password: 'Le mot de passe'\n    };\n    return displayNames[fieldName] || fieldName;\n  }\n  /**\n   * Nettoie le message d'erreur\n   */\n  clearError() {\n    this.errorMessage = '';\n  }\n  static {\n    this.ɵfac = function LoginComponent_Factory(t) {\n      return new (t || LoginComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: LoginComponent,\n      selectors: [[\"app-login\"]],\n      decls: 81,\n      vars: 16,\n      consts: [[1, \"login-container\"], [1, \"login-left\"], [1, \"brand-section\"], [1, \"brand-title\"], [1, \"brand-subtitle\"], [1, \"travel-illustration\"], [\"viewBox\", \"0 0 200 200\", \"fill\", \"none\", 1, \"travel-icon\"], [\"d\", \"M50 100 L150 80 L160 85 L150 90 L50 110 Z\", \"fill\", \"white\", \"opacity\", \"0.9\"], [\"d\", \"M45 105 L55 100 L55 110 Z\", \"fill\", \"white\", \"opacity\", \"0.7\"], [\"cx\", \"40\", \"cy\", \"60\", \"r\", \"15\", \"fill\", \"white\", \"opacity\", \"0.6\"], [\"cx\", \"50\", \"cy\", \"60\", \"r\", \"18\", \"fill\", \"white\", \"opacity\", \"0.6\"], [\"cx\", \"60\", \"cy\", \"60\", \"r\", \"15\", \"fill\", \"white\", \"opacity\", \"0.6\"], [\"d\", \"M0 150 L30 120 L60 140 L90 110 L120 130 L150 100 L180 120 L200 110 L200 200 L0 200 Z\", \"fill\", \"white\", \"opacity\", \"0.3\"], [1, \"monuments\"], [\"viewBox\", \"0 0 300 100\", \"fill\", \"none\", 1, \"monument-icon\"], [\"d\", \"M50 90 L60 20 L70 90 M45 90 L75 90 M55 60 L65 60\", \"stroke\", \"#0ea5e9\", \"stroke-width\", \"2\", \"fill\", \"none\"], [\"d\", \"M120 90 L120 60 Q120 50 130 50 Q140 50 140 60 L140 90 M110 90 L150 90\", \"stroke\", \"#0ea5e9\", \"stroke-width\", \"2\", \"fill\", \"none\"], [\"cx\", \"130\", \"cy\", \"55\", \"r\", \"3\", \"fill\", \"#0ea5e9\"], [\"x\", \"180\", \"y\", \"70\", \"width\", \"15\", \"height\", \"20\", \"fill\", \"#0ea5e9\", \"opacity\", \"0.7\"], [\"x\", \"200\", \"y\", \"60\", \"width\", \"15\", \"height\", \"30\", \"fill\", \"#0ea5e9\", \"opacity\", \"0.8\"], [\"x\", \"220\", \"y\", \"50\", \"width\", \"15\", \"height\", \"40\", \"fill\", \"#0ea5e9\"], [1, \"login-right\"], [1, \"login-form-container\"], [1, \"login-header\"], [1, \"plane-icon\"], [\"viewBox\", \"0 0 24 24\", \"fill\", \"none\", \"stroke\", \"currentColor\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M12 19l9 2-9-18-9 18 9-2zm0 0v-8\"], [1, \"welcome-title\"], [1, \"welcome-subtitle\"], [\"class\", \"error-message\", \"role\", \"alert\", 4, \"ngIf\"], [\"novalidate\", \"\", 1, \"login-form\", 3, \"formGroup\", \"ngSubmit\"], [1, \"form-group\"], [\"for\", \"agency\", 1, \"form-label\"], [1, \"input-container\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"input-icon\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207\"], [\"type\", \"text\", \"id\", \"agency\", \"formControlName\", \"agency\", \"placeholder\", \"Enter your email\", \"autocomplete\", \"email\", 1, \"form-input\"], [\"class\", \"field-error\", 4, \"ngIf\"], [\"for\", \"password\", 1, \"form-label\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z\"], [\"id\", \"password\", \"formControlName\", \"password\", \"placeholder\", \"\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\", \"autocomplete\", \"current-password\", 1, \"form-input\", 3, \"type\"], [\"type\", \"button\", 1, \"password-toggle\", 3, \"click\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 4, \"ngIf\"], [1, \"forgot-password\"], [\"href\", \"#\", 1, \"forgot-link\"], [\"type\", \"submit\", 1, \"login-button\", 3, \"disabled\"], [4, \"ngIf\"], [\"class\", \"loading-content\", 4, \"ngIf\"], [1, \"divider\"], [1, \"social-login\"], [\"type\", \"button\", 1, \"social-btn\", \"google-btn\"], [\"viewBox\", \"0 0 24 24\"], [\"fill\", \"#4285F4\", \"d\", \"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z\"], [\"fill\", \"#34A853\", \"d\", \"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z\"], [\"fill\", \"#FBBC05\", \"d\", \"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z\"], [\"fill\", \"#EA4335\", \"d\", \"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z\"], [\"type\", \"button\", 1, \"social-btn\", \"facebook-btn\"], [\"viewBox\", \"0 0 24 24\", \"fill\", \"#1877F2\"], [\"d\", \"M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z\"], [\"type\", \"button\", 1, \"social-btn\", \"apple-btn\"], [\"viewBox\", \"0 0 24 24\", \"fill\", \"#000\"], [\"d\", \"M12.017 0C8.396 0 8.025.044 8.025.044c0 .467.02.94.058 1.4.037.46.094.92.17 1.374.076.454.17.9.282 1.336.112.436.25.86.413 1.27.163.41.35.8.563 1.17.213.37.45.72.712 1.05.262.33.55.64.862.93.312.29.65.56 1.012.81.362.25.75.48 1.162.69.412.21.85.4 1.312.57.462.17.95.32 1.462.45.512.13 1.05.24 1.612.33.562.09 1.15.16 1.762.21.612.05 1.25.08 1.912.09h.08c.662-.01 1.3-.04 1.912-.09.612-.05 1.2-.12 1.762-.21.562-.09 1.1-.2 1.612-.33.512-.13 1-.28 1.462-.45.462-.17.9-.36 1.312-.57.412-.21.8-.44 1.162-.69.362-.25.7-.52 1.012-.81.312-.29.6-.6.862-.93.262-.33.499-.68.712-1.05.213-.37.4-.76.563-1.17.163-.41.301-.834.413-1.27.112-.436.206-.882.282-1.336.076-.454.133-.914.17-1.374.037-.46.058-.933.058-1.4 0 0-.371-.044-3.992-.044z\"], [1, \"register-link\"], [\"href\", \"#\", 1, \"register-btn\"], [\"role\", \"alert\", 1, \"error-message\"], [\"fill\", \"currentColor\", \"viewBox\", \"0 0 20 20\", 1, \"error-icon\"], [\"fill-rule\", \"evenodd\", \"d\", \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z\", \"clip-rule\", \"evenodd\"], [\"type\", \"button\", 1, \"error-close\", 3, \"click\"], [1, \"field-error\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21\"], [1, \"loading-content\"], [\"viewBox\", \"0 0 24 24\", 1, \"loading-spinner\"], [\"cx\", \"12\", \"cy\", \"12\", \"r\", \"10\", \"stroke\", \"currentColor\", \"stroke-width\", \"4\", \"fill\", \"none\", \"opacity\", \"0.25\"], [\"fill\", \"currentColor\", \"d\", \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\", \"opacity\", \"0.75\"]],\n      template: function LoginComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h1\", 3);\n          i0.ɵɵtext(4, \"Paximum Tours\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"p\", 4);\n          i0.ɵɵtext(6, \"Travel is the only purchase that enriches you in ways beyond material wealth\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(7, \"div\", 5);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(8, \"svg\", 6);\n          i0.ɵɵelement(9, \"path\", 7)(10, \"path\", 8)(11, \"circle\", 9)(12, \"circle\", 10)(13, \"circle\", 11)(14, \"path\", 12);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(15, \"div\", 13);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(16, \"svg\", 14);\n          i0.ɵɵelement(17, \"path\", 15)(18, \"path\", 16)(19, \"circle\", 17)(20, \"rect\", 18)(21, \"rect\", 19)(22, \"rect\", 20);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(23, \"div\", 21)(24, \"div\", 22)(25, \"div\", 23)(26, \"div\", 24);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(27, \"svg\", 25);\n          i0.ɵɵelement(28, \"path\", 26);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(29, \"h2\", 27);\n          i0.ɵɵtext(30, \"Welcome\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(31, \"p\", 28);\n          i0.ɵɵtext(32, \"Login with Email\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(33, LoginComponent_div_33_Template, 7, 1, \"div\", 29);\n          i0.ɵɵelementStart(34, \"form\", 30);\n          i0.ɵɵlistener(\"ngSubmit\", function LoginComponent_Template_form_ngSubmit_34_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵelementStart(35, \"div\", 31)(36, \"label\", 32);\n          i0.ɵɵtext(37, \"Email/Agency\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(38, \"div\", 33);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(39, \"svg\", 34);\n          i0.ɵɵelement(40, \"path\", 35);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelement(41, \"input\", 36);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(42, LoginComponent_div_42_Template, 2, 1, \"div\", 37);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(43, \"div\", 31)(44, \"label\", 38);\n          i0.ɵɵtext(45, \"Password\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(46, \"div\", 33);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(47, \"svg\", 34);\n          i0.ɵɵelement(48, \"path\", 39);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelement(49, \"input\", 40);\n          i0.ɵɵelementStart(50, \"button\", 41);\n          i0.ɵɵlistener(\"click\", function LoginComponent_Template_button_click_50_listener() {\n            return ctx.togglePasswordVisibility();\n          });\n          i0.ɵɵtemplate(51, LoginComponent__svg_svg_51_Template, 3, 0, \"svg\", 42);\n          i0.ɵɵtemplate(52, LoginComponent__svg_svg_52_Template, 2, 0, \"svg\", 42);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(53, LoginComponent_div_53_Template, 2, 1, \"div\", 37);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(54, \"div\", 43)(55, \"a\", 44);\n          i0.ɵɵtext(56, \"Forgot your password?\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(57, \"button\", 45);\n          i0.ɵɵtemplate(58, LoginComponent_span_58_Template, 2, 0, \"span\", 46);\n          i0.ɵɵtemplate(59, LoginComponent_span_59_Template, 5, 0, \"span\", 47);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(60, \"div\", 48)(61, \"span\");\n          i0.ɵɵtext(62, \"OR\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(63, \"div\", 49)(64, \"button\", 50);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(65, \"svg\", 51);\n          i0.ɵɵelement(66, \"path\", 52)(67, \"path\", 53)(68, \"path\", 54)(69, \"path\", 55);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(70, \"button\", 56);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(71, \"svg\", 57);\n          i0.ɵɵelement(72, \"path\", 58);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(73, \"button\", 59);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(74, \"svg\", 60);\n          i0.ɵɵelement(75, \"path\", 61);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(76, \"div\", 62)(77, \"span\");\n          i0.ɵɵtext(78, \"Don't have account? \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(79, \"a\", 63);\n          i0.ɵɵtext(80, \"Register Now\");\n          i0.ɵɵelementEnd()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(33);\n          i0.ɵɵproperty(\"ngIf\", ctx.errorMessage);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"formGroup\", ctx.loginForm);\n          i0.ɵɵadvance(7);\n          i0.ɵɵclassProp(\"error\", ctx.hasError(\"agency\", \"required\") || ctx.hasError(\"agency\", \"minlength\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.formControls[\"agency\"].touched && ctx.formControls[\"agency\"].errors);\n          i0.ɵɵadvance(7);\n          i0.ɵɵclassProp(\"error\", ctx.hasError(\"password\", \"required\") || ctx.hasError(\"password\", \"minlength\"));\n          i0.ɵɵproperty(\"type\", ctx.showPassword ? \"text\" : \"password\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", !ctx.showPassword);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.showPassword);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.formControls[\"password\"].touched && ctx.formControls[\"password\"].errors);\n          i0.ɵɵadvance(4);\n          i0.ɵɵclassProp(\"loading\", ctx.isLoading);\n          i0.ɵɵproperty(\"disabled\", ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n        }\n      },\n      dependencies: [i4.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName],\n      styles: [\"\\n\\n[_ngcontent-%COMP%]:root {\\n  --primary-blue: #0ea5e9;\\n  --primary-blue-hover: #0284c7;\\n  --secondary-blue: #38bdf8;\\n  --text-dark: #1e293b;\\n  --text-light: #64748b;\\n  --text-white: #ffffff;\\n  --error-color: #ef4444;\\n  --error-bg: #fef2f2;\\n  --success-color: #10b981;\\n  --border-color: #e2e8f0;\\n  --input-bg: #f8fafc;\\n  --shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);\\n  --transition: all 0.3s ease;\\n}\\n\\n\\n\\n.login-container[_ngcontent-%COMP%] {\\n  min-height: 100vh;\\n  display: flex;\\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;\\n}\\n\\n\\n\\n.login-card[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: var(--border-radius);\\n  box-shadow: var(--card-shadow);\\n  padding: 2.5rem;\\n  width: 100%;\\n  max-width: 420px;\\n  animation: _ngcontent-%COMP%_slideUp 0.6s ease-out;\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.login-card[_ngcontent-%COMP%]::before {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  height: 4px;\\n  background: var(--background-gradient);\\n}\\n\\n@keyframes _ngcontent-%COMP%_slideUp {\\n  from {\\n    opacity: 0;\\n    transform: translateY(30px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n\\n\\n\\n.login-header[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-bottom: 2.5rem;\\n}\\n\\n.logo-container[_ngcontent-%COMP%] {\\n  margin-bottom: 1rem;\\n}\\n\\n.login-title[_ngcontent-%COMP%] {\\n  font-size: 2.25rem;\\n  font-weight: 800;\\n  color: var(--primary-color);\\n  margin: 0 0 0.25rem 0;\\n  letter-spacing: -0.025em;\\n}\\n\\n.logo-subtitle[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: var(--secondary-color);\\n  font-weight: 500;\\n}\\n\\n.login-subtitle[_ngcontent-%COMP%] {\\n  color: var(--secondary-color);\\n  margin: 0;\\n  font-size: 1rem;\\n  font-weight: 400;\\n}\\n\\n\\n\\n.login-form[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 1.75rem;\\n}\\n\\n\\n\\n.form-group[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 0.5rem;\\n}\\n\\n.form-label[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #374151;\\n  font-size: 0.875rem;\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n}\\n\\n.label-icon[_ngcontent-%COMP%] {\\n  width: 1rem;\\n  height: 1rem;\\n  color: var(--secondary-color);\\n}\\n\\n.form-input[_ngcontent-%COMP%] {\\n  padding: 0.875rem 1rem;\\n  border: 2px solid #e5e7eb;\\n  border-radius: 0.5rem;\\n  font-size: 1rem;\\n  transition: var(--transition);\\n  background-color: #f9fafb;\\n  font-weight: 400;\\n}\\n\\n.form-input[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  border-color: var(--primary-color);\\n  box-shadow: 0 0 0 3px var(--primary-light);\\n  background-color: white;\\n}\\n\\n.form-input[_ngcontent-%COMP%]:hover:not(:focus) {\\n  border-color: #d1d5db;\\n}\\n\\n.form-input.error[_ngcontent-%COMP%] {\\n  border-color: var(--error-color);\\n  background-color: var(--error-bg);\\n}\\n\\n.form-input.error[_ngcontent-%COMP%]:focus {\\n  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);\\n}\\n\\n\\n\\n.password-input-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  display: flex;\\n  align-items: center;\\n}\\n\\n.password-input[_ngcontent-%COMP%] {\\n  padding-right: 3.5rem;\\n}\\n\\n.password-toggle[_ngcontent-%COMP%] {\\n  position: absolute;\\n  right: 1rem;\\n  background: none;\\n  border: none;\\n  cursor: pointer;\\n  color: var(--secondary-color);\\n  padding: 0.5rem;\\n  border-radius: 0.375rem;\\n  transition: var(--transition);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n\\n.password-toggle[_ngcontent-%COMP%]:hover {\\n  color: #374151;\\n  background-color: #f3f4f6;\\n}\\n\\n.password-toggle[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  color: var(--primary-color);\\n  background-color: var(--primary-light);\\n}\\n\\n.password-icon[_ngcontent-%COMP%] {\\n  width: 1.25rem;\\n  height: 1.25rem;\\n}\\n\\n\\n\\n.error-message[_ngcontent-%COMP%] {\\n  background-color: var(--error-bg);\\n  border: 1px solid var(--error-border);\\n  color: var(--error-color);\\n  padding: 1rem;\\n  border-radius: 0.5rem;\\n  font-size: 0.875rem;\\n  display: flex;\\n  align-items: flex-start;\\n  gap: 0.75rem;\\n  position: relative;\\n}\\n\\n.error-icon[_ngcontent-%COMP%] {\\n  width: 1.25rem;\\n  height: 1.25rem;\\n  flex-shrink: 0;\\n  margin-top: 0.125rem;\\n}\\n\\n.error-close[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0.75rem;\\n  right: 0.75rem;\\n  background: none;\\n  border: none;\\n  color: var(--error-color);\\n  cursor: pointer;\\n  padding: 0.25rem;\\n  border-radius: 0.25rem;\\n  transition: var(--transition);\\n}\\n\\n.error-close[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(239, 68, 68, 0.1);\\n}\\n\\n.error-close[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  width: 1rem;\\n  height: 1rem;\\n}\\n\\n.field-error[_ngcontent-%COMP%] {\\n  color: var(--error-color);\\n  font-size: 0.75rem;\\n  margin-top: 0.25rem;\\n  font-weight: 500;\\n}\\n\\n\\n\\n.login-button[_ngcontent-%COMP%] {\\n  background: var(--background-gradient);\\n  color: white;\\n  border: none;\\n  padding: 1rem 1.5rem;\\n  border-radius: 0.5rem;\\n  font-size: 1rem;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: var(--transition);\\n  position: relative;\\n  overflow: hidden;\\n  min-height: 3.25rem;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n\\n.login-button[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  transform: translateY(-2px);\\n  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);\\n}\\n\\n.login-button[_ngcontent-%COMP%]:active:not(:disabled) {\\n  transform: translateY(0);\\n}\\n\\n.login-button[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.6;\\n  cursor: not-allowed;\\n  transform: none;\\n}\\n\\n.button-content[_ngcontent-%COMP%], .loading-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 0.5rem;\\n}\\n\\n.button-icon[_ngcontent-%COMP%] {\\n  width: 1.25rem;\\n  height: 1.25rem;\\n}\\n\\n.loading-spinner[_ngcontent-%COMP%] {\\n  width: 1.25rem;\\n  height: 1.25rem;\\n  animation: _ngcontent-%COMP%_spin 1s linear infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_spin {\\n  from {\\n    transform: rotate(0deg);\\n  }\\n  to {\\n    transform: rotate(360deg);\\n  }\\n}\\n\\n\\n\\n.login-footer[_ngcontent-%COMP%] {\\n  margin-top: 2.5rem;\\n  text-align: center;\\n  border-top: 1px solid #e5e7eb;\\n  padding-top: 1.5rem;\\n}\\n\\n.footer-text[_ngcontent-%COMP%] {\\n  color: var(--secondary-color);\\n  font-size: 0.875rem;\\n  margin: 0 0 0.75rem 0;\\n}\\n\\n.footer-link[_ngcontent-%COMP%] {\\n  color: var(--primary-color);\\n  text-decoration: none;\\n  font-weight: 600;\\n  transition: var(--transition);\\n}\\n\\n.footer-link[_ngcontent-%COMP%]:hover {\\n  color: var(--primary-hover);\\n  text-decoration: underline;\\n}\\n\\n.version-info[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  color: #9ca3af;\\n  font-weight: 500;\\n}\\n\\n\\n\\n@media (max-width: 480px) {\\n  .login-container[_ngcontent-%COMP%] {\\n    padding: 0.75rem;\\n  }\\n  \\n  .login-card[_ngcontent-%COMP%] {\\n    padding: 2rem 1.5rem;\\n    border-radius: 0.5rem;\\n  }\\n  \\n  .login-title[_ngcontent-%COMP%] {\\n    font-size: 1.875rem;\\n  }\\n  \\n  .form-input[_ngcontent-%COMP%] {\\n    padding: 0.75rem;\\n    font-size: 0.875rem;\\n  }\\n  \\n  .login-button[_ngcontent-%COMP%] {\\n    padding: 0.875rem 1.25rem;\\n    font-size: 0.875rem;\\n  }\\n}\\n\\n@media (max-width: 360px) {\\n  .login-card[_ngcontent-%COMP%] {\\n    padding: 1.5rem 1rem;\\n  }\\n  \\n  .login-title[_ngcontent-%COMP%] {\\n    font-size: 1.5rem;\\n  }\\n  \\n  .login-form[_ngcontent-%COMP%] {\\n    gap: 1.5rem;\\n  }\\n}\\n\\n\\n\\n@media (prefers-reduced-motion: reduce) {\\n  .login-card[_ngcontent-%COMP%] {\\n    animation: none;\\n  }\\n  \\n  .loading-spinner[_ngcontent-%COMP%] {\\n    animation: none;\\n  }\\n  \\n  .login-button[_ngcontent-%COMP%] {\\n    transition: none;\\n  }\\n  \\n  *[_ngcontent-%COMP%] {\\n    transition: none !important;\\n  }\\n}\\n\\n\\n\\n@media (prefers-color-scheme: dark) {\\n  .login-container[_ngcontent-%COMP%] {\\n    background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);\\n  }\\n  \\n  .login-card[_ngcontent-%COMP%] {\\n    background: #1e293b;\\n    color: #f1f5f9;\\n    border: 1px solid #334155;\\n  }\\n  \\n  .login-title[_ngcontent-%COMP%] {\\n    color: #60a5fa;\\n  }\\n  \\n  .login-subtitle[_ngcontent-%COMP%], .logo-subtitle[_ngcontent-%COMP%] {\\n    color: #cbd5e1;\\n  }\\n  \\n  .form-label[_ngcontent-%COMP%] {\\n    color: #e2e8f0;\\n  }\\n  \\n  .form-input[_ngcontent-%COMP%] {\\n    background-color: #334155;\\n    border-color: #475569;\\n    color: #f1f5f9;\\n  }\\n  \\n  .form-input[_ngcontent-%COMP%]:focus {\\n    background-color: #1e293b;\\n    border-color: #60a5fa;\\n  }\\n  \\n  .footer-text[_ngcontent-%COMP%] {\\n    color: #cbd5e1;\\n  }\\n  \\n  .version-info[_ngcontent-%COMP%] {\\n    color: #64748b;\\n  }\\n}\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "i0", "ɵɵelementStart", "ɵɵnamespaceSVG", "ɵɵelement", "ɵɵelementEnd", "ɵɵnamespaceHTML", "ɵɵtext", "ɵɵlistener", "LoginComponent_div_33_Template_button_click_5_listener", "ɵɵrestoreView", "_r8", "ctx_r7", "ɵɵnextContext", "ɵɵresetView", "clearError", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r0", "errorMessage", "ɵɵtextInterpolate1", "ctx_r1", "getErrorMessage", "ctx_r4", "LoginComponent", "constructor", "formBuilder", "authService", "router", "isLoading", "showPassword", "ngOnInit", "initializeForm", "isAuthenticated", "navigate", "loginForm", "group", "agency", "required", "<PERSON><PERSON><PERSON><PERSON>", "user", "password", "formControls", "controls", "togglePasswordVisibility", "onSubmit", "valid", "authRequest", "Agency", "value", "trim", "User", "Password", "authenticate", "subscribe", "next", "response", "header", "success", "console", "log", "body", "userInfo", "handleApiError", "error", "message", "markFormGroupTouched", "messages", "length", "Object", "keys", "for<PERSON>ach", "key", "control", "get", "<PERSON><PERSON><PERSON><PERSON>ched", "<PERSON><PERSON><PERSON><PERSON>", "fieldName", "errorType", "field", "touched", "getFieldDisplayName", "<PERSON><PERSON><PERSON><PERSON>", "errors", "displayNames", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "AuthService", "i3", "Router", "selectors", "decls", "vars", "consts", "template", "LoginComponent_Template", "rf", "ctx", "ɵɵtemplate", "LoginComponent_div_33_Template", "LoginComponent_Template_form_ngSubmit_34_listener", "LoginComponent_div_42_Template", "LoginComponent_Template_button_click_50_listener", "LoginComponent__svg_svg_51_Template", "LoginComponent__svg_svg_52_Template", "LoginComponent_div_53_Template", "LoginComponent_span_58_Template", "LoginComponent_span_59_Template", "ɵɵproperty", "ɵɵclassProp"], "sources": ["C:\\Users\\<USER>\\Desktop\\angular\\front\\src\\app\\components\\login\\login.component.ts", "C:\\Users\\<USER>\\Desktop\\angular\\front\\src\\app\\components\\login\\login.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { Router } from '@angular/router';\n\nimport { AuthService } from '../../services/auth.service';\nimport { AuthRequest } from '../../models/auth-request.interface';\n\n@Component({\n  selector: 'app-login',\n  templateUrl: './login.component.html',\n  styleUrls: ['./login.component.css']\n})\nexport class LoginComponent implements OnInit {\n  loginForm!: FormGroup;\n  isLoading = false;\n  errorMessage = '';\n  showPassword = false;\n\n  constructor(\n    private formBuilder: FormBuilder,\n    private authService: AuthService,\n    private router: Router\n  ) {}\n\n  ngOnInit(): void {\n    this.initializeForm();\n    \n    // Rediriger si déjà connecté\n    if (this.authService.isAuthenticated()) {\n      this.router.navigate(['/dashboard']); // Ajustez selon votre route de destination\n    }\n  }\n\n  /**\n   * Initialise le formulaire de connexion avec les validations\n   */\n  private initializeForm(): void {\n    this.loginForm = this.formBuilder.group({\n      agency: ['', [Validators.required, Validators.minLength(2)]],\n      user: ['', [Validators.required, Validators.minLength(2)]],\n      password: ['', [Validators.required, Validators.minLength(6)]]\n    });\n  }\n\n  /**\n   * Getter pour accéder facilement aux contrôles du formulaire\n   */\n  get formControls() {\n    return this.loginForm.controls;\n  }\n\n  /**\n   * Bascule la visibilité du mot de passe\n   */\n  togglePasswordVisibility(): void {\n    this.showPassword = !this.showPassword;\n  }\n\n  /**\n   * Soumet le formulaire de connexion\n   */\n  onSubmit(): void {\n    if (this.loginForm.valid && !this.isLoading) {\n      this.isLoading = true;\n      this.errorMessage = '';\n\n      // Créer l'objet de requête avec les noms de propriétés exacts du backend\n      const authRequest: AuthRequest = {\n        Agency: this.loginForm.value.agency.trim(),\n        User: this.loginForm.value.user.trim(),\n        Password: this.loginForm.value.password\n      };\n\n      this.authService.authenticate(authRequest).subscribe({\n        next: (response) => {\n          this.isLoading = false;\n          \n          if (response.header.success) {\n            // Connexion réussie\n            console.log('Connexion réussie:', response.body.userInfo);\n            \n            // Rediriger vers le dashboard ou la page d'accueil\n            this.router.navigate(['/dashboard']); // Ajustez selon votre route\n          } else {\n            // Erreur retournée par l'API\n            this.handleApiError(response);\n          }\n        },\n        error: (error) => {\n          this.isLoading = false;\n          this.errorMessage = error.message || 'Une erreur est survenue lors de la connexion';\n          console.error('Erreur de connexion:', error);\n        }\n      });\n    } else {\n      // Marquer tous les champs comme touchés pour afficher les erreurs\n      this.markFormGroupTouched();\n    }\n  }\n\n  /**\n   * Gère les erreurs retournées par l'API\n   */\n  private handleApiError(response: any): void {\n    if (response.header.messages && response.header.messages.length > 0) {\n      // Utiliser le premier message d'erreur de l'API\n      this.errorMessage = response.header.messages[0].message;\n    } else {\n      this.errorMessage = 'Échec de l\\'authentification';\n    }\n  }\n\n  /**\n   * Marque tous les champs du formulaire comme touchés\n   */\n  private markFormGroupTouched(): void {\n    Object.keys(this.loginForm.controls).forEach(key => {\n      const control = this.loginForm.get(key);\n      control?.markAsTouched();\n    });\n  }\n\n  /**\n   * Vérifie si un champ a une erreur et a été touché\n   */\n  hasError(fieldName: string, errorType: string): boolean {\n    const field = this.loginForm.get(fieldName);\n    return !!(field?.hasError(errorType) && field?.touched);\n  }\n\n  /**\n   * Récupère le message d'erreur pour un champ\n   */\n  getErrorMessage(fieldName: string): string {\n    const field = this.loginForm.get(fieldName);\n    \n    if (field?.hasError('required')) {\n      return `${this.getFieldDisplayName(fieldName)} est requis`;\n    }\n    \n    if (field?.hasError('minlength')) {\n      const requiredLength = field.errors?.['minlength']?.requiredLength;\n      return `${this.getFieldDisplayName(fieldName)} doit contenir au moins ${requiredLength} caractères`;\n    }\n    \n    return '';\n  }\n\n  /**\n   * Retourne le nom d'affichage pour un champ\n   */\n  private getFieldDisplayName(fieldName: string): string {\n    const displayNames: { [key: string]: string } = {\n      agency: 'Le code agence',\n      user: 'Le code utilisateur',\n      password: 'Le mot de passe'\n    };\n    \n    return displayNames[fieldName] || fieldName;\n  }\n\n  /**\n   * Nettoie le message d'erreur\n   */\n  clearError(): void {\n    this.errorMessage = '';\n  }\n}\n", "<div class=\"login-container\">\n  <!-- Section gauche avec image de fond -->\n  <div class=\"login-left\">\n    <div class=\"brand-section\">\n      <h1 class=\"brand-title\">Paximum Tours</h1>\n      <p class=\"brand-subtitle\">Travel is the only purchase that enriches you in ways beyond material wealth</p>\n    </div>\n\n    <!-- Illustration de voyage -->\n    <div class=\"travel-illustration\">\n      <svg class=\"travel-icon\" viewBox=\"0 0 200 200\" fill=\"none\">\n        <!-- Avion -->\n        <path d=\"M50 100 L150 80 L160 85 L150 90 L50 110 Z\" fill=\"white\" opacity=\"0.9\"/>\n        <path d=\"M45 105 L55 100 L55 110 Z\" fill=\"white\" opacity=\"0.7\"/>\n        <!-- Nuages -->\n        <circle cx=\"40\" cy=\"60\" r=\"15\" fill=\"white\" opacity=\"0.6\"/>\n        <circle cx=\"50\" cy=\"60\" r=\"18\" fill=\"white\" opacity=\"0.6\"/>\n        <circle cx=\"60\" cy=\"60\" r=\"15\" fill=\"white\" opacity=\"0.6\"/>\n        <!-- Montagnes -->\n        <path d=\"M0 150 L30 120 L60 140 L90 110 L120 130 L150 100 L180 120 L200 110 L200 200 L0 200 Z\" fill=\"white\" opacity=\"0.3\"/>\n      </svg>\n    </div>\n\n    <!-- Monuments en bas -->\n    <div class=\"monuments\">\n      <svg class=\"monument-icon\" viewBox=\"0 0 300 100\" fill=\"none\">\n        <!-- Tour Eiffel stylisée -->\n        <path d=\"M50 90 L60 20 L70 90 M45 90 L75 90 M55 60 L65 60\" stroke=\"#0ea5e9\" stroke-width=\"2\" fill=\"none\"/>\n        <!-- Taj Mahal stylisé -->\n        <path d=\"M120 90 L120 60 Q120 50 130 50 Q140 50 140 60 L140 90 M110 90 L150 90\" stroke=\"#0ea5e9\" stroke-width=\"2\" fill=\"none\"/>\n        <circle cx=\"130\" cy=\"55\" r=\"3\" fill=\"#0ea5e9\"/>\n        <!-- Building moderne -->\n        <rect x=\"180\" y=\"70\" width=\"15\" height=\"20\" fill=\"#0ea5e9\" opacity=\"0.7\"/>\n        <rect x=\"200\" y=\"60\" width=\"15\" height=\"30\" fill=\"#0ea5e9\" opacity=\"0.8\"/>\n        <rect x=\"220\" y=\"50\" width=\"15\" height=\"40\" fill=\"#0ea5e9\"/>\n      </svg>\n    </div>\n  </div>\n\n  <!-- Section droite avec formulaire -->\n  <div class=\"login-right\">\n    <div class=\"login-form-container\">\n      <!-- Header avec avion -->\n      <div class=\"login-header\">\n        <div class=\"plane-icon\">\n          <svg viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\">\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 19l9 2-9-18-9 18 9-2zm0 0v-8\"/>\n          </svg>\n        </div>\n        <h2 class=\"welcome-title\">Welcome</h2>\n        <p class=\"welcome-subtitle\">Login with Email</p>\n      </div>\n\n      <!-- Message d'erreur global -->\n      <div *ngIf=\"errorMessage\" class=\"error-message\" role=\"alert\">\n        <svg class=\"error-icon\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n          <path fill-rule=\"evenodd\" d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z\" clip-rule=\"evenodd\"></path>\n        </svg>\n        <span>{{ errorMessage }}</span>\n        <button type=\"button\" class=\"error-close\" (click)=\"clearError()\">×</button>\n      </div>\n\n      <!-- Formulaire -->\n      <form [formGroup]=\"loginForm\" (ngSubmit)=\"onSubmit()\" class=\"login-form\" novalidate>\n\n        <!-- Champ Email/Agency -->\n        <div class=\"form-group\">\n          <label for=\"agency\" class=\"form-label\">Email/Agency</label>\n          <div class=\"input-container\">\n            <svg class=\"input-icon\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207\"/>\n            </svg>\n            <input\n              type=\"text\"\n              id=\"agency\"\n              formControlName=\"agency\"\n              class=\"form-input\"\n              [class.error]=\"hasError('agency', 'required') || hasError('agency', 'minlength')\"\n              placeholder=\"Enter your email\"\n              autocomplete=\"email\"\n            >\n          </div>\n          <div *ngIf=\"formControls['agency'].touched && formControls['agency'].errors\" class=\"field-error\">\n            {{ getErrorMessage('agency') }}\n          </div>\n        </div>\n\n        <!-- Champ Password -->\n        <div class=\"form-group\">\n          <label for=\"password\" class=\"form-label\">Password</label>\n          <div class=\"input-container\">\n            <svg class=\"input-icon\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z\"/>\n            </svg>\n            <input\n              [type]=\"showPassword ? 'text' : 'password'\"\n              id=\"password\"\n              formControlName=\"password\"\n              class=\"form-input\"\n              [class.error]=\"hasError('password', 'required') || hasError('password', 'minlength')\"\n              placeholder=\"••••••••••••\"\n              autocomplete=\"current-password\"\n            >\n            <button\n              type=\"button\"\n              class=\"password-toggle\"\n              (click)=\"togglePasswordVisibility()\"\n            >\n              <svg *ngIf=\"!showPassword\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"/>\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z\"/>\n              </svg>\n              <svg *ngIf=\"showPassword\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21\"/>\n              </svg>\n            </button>\n          </div>\n          <div *ngIf=\"formControls['password'].touched && formControls['password'].errors\" class=\"field-error\">\n            {{ getErrorMessage('password') }}\n          </div>\n        </div>\n\n        <!-- Forgot password -->\n        <div class=\"forgot-password\">\n          <a href=\"#\" class=\"forgot-link\">Forgot your password?</a>\n        </div>\n\n        <!-- Bouton LOGIN -->\n        <button\n          type=\"submit\"\n          class=\"login-button\"\n          [disabled]=\"isLoading\"\n          [class.loading]=\"isLoading\"\n        >\n          <span *ngIf=\"!isLoading\">LOGIN</span>\n          <span *ngIf=\"isLoading\" class=\"loading-content\">\n            <svg class=\"loading-spinner\" viewBox=\"0 0 24 24\">\n              <circle cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" stroke-width=\"4\" fill=\"none\" opacity=\"0.25\"/>\n              <path fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\" opacity=\"0.75\"/>\n            </svg>\n            Loading...\n          </span>\n        </button>\n\n        <!-- Divider -->\n        <div class=\"divider\">\n          <span>OR</span>\n        </div>\n\n        <!-- Social login buttons -->\n        <div class=\"social-login\">\n          <button type=\"button\" class=\"social-btn google-btn\">\n            <svg viewBox=\"0 0 24 24\">\n              <path fill=\"#4285F4\" d=\"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z\"/>\n              <path fill=\"#34A853\" d=\"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z\"/>\n              <path fill=\"#FBBC05\" d=\"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z\"/>\n              <path fill=\"#EA4335\" d=\"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z\"/>\n            </svg>\n          </button>\n\n          <button type=\"button\" class=\"social-btn facebook-btn\">\n            <svg viewBox=\"0 0 24 24\" fill=\"#1877F2\">\n              <path d=\"M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z\"/>\n            </svg>\n          </button>\n\n          <button type=\"button\" class=\"social-btn apple-btn\">\n            <svg viewBox=\"0 0 24 24\" fill=\"#000\">\n              <path d=\"M12.017 0C8.396 0 8.025.044 8.025.044c0 .467.02.94.058 1.4.037.46.094.92.17 1.374.076.454.17.9.282 1.336.112.436.25.86.413 1.27.163.41.35.8.563 1.17.213.37.45.72.712 1.05.262.33.55.64.862.93.312.29.65.56 1.012.81.362.25.75.48 1.162.69.412.21.85.4 1.312.57.462.17.95.32 1.462.45.512.13 1.05.24 1.612.33.562.09 1.15.16 1.762.21.612.05 1.25.08 1.912.09h.08c.662-.01 1.3-.04 1.912-.09.612-.05 1.2-.12 1.762-.21.562-.09 1.1-.2 1.612-.33.512-.13 1-.28 1.462-.45.462-.17.9-.36 1.312-.57.412-.21.8-.44 1.162-.69.362-.25.7-.52 1.012-.81.312-.29.6-.6.862-.93.262-.33.499-.68.712-1.05.213-.37.4-.76.563-1.17.163-.41.301-.834.413-1.27.112-.436.206-.882.282-1.336.076-.454.133-.914.17-1.374.037-.46.058-.933.058-1.4 0 0-.371-.044-3.992-.044z\"/>\n            </svg>\n          </button>\n        </div>\n\n        <!-- Register link -->\n        <div class=\"register-link\">\n          <span>Don't have account? </span>\n          <a href=\"#\" class=\"register-btn\">Register Now</a>\n        </div>\n      </form>\n    </div>\n  </div>\n</div>\n"], "mappings": "AACA,SAAiCA,UAAU,QAAQ,gBAAgB;;;;;;;;;ICqD7DC,EAAA,CAAAC,cAAA,cAA6D;IAC3DD,EAAA,CAAAE,cAAA,EAAgE;IAAhEF,EAAA,CAAAC,cAAA,cAAgE;IAC9DD,EAAA,CAAAG,SAAA,eAA2K;IAC7KH,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAK,eAAA,EAAM;IAANL,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAM,MAAA,GAAkB;IAAAN,EAAA,CAAAI,YAAA,EAAO;IAC/BJ,EAAA,CAAAC,cAAA,iBAAiE;IAAvBD,EAAA,CAAAO,UAAA,mBAAAC,uDAAA;MAAAR,EAAA,CAAAS,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAX,EAAA,CAAAY,aAAA;MAAA,OAASZ,EAAA,CAAAa,WAAA,CAAAF,MAAA,CAAAG,UAAA,EAAY;IAAA,EAAC;IAACd,EAAA,CAAAM,MAAA,aAAC;IAAAN,EAAA,CAAAI,YAAA,EAAS;;;;IADrEJ,EAAA,CAAAe,SAAA,GAAkB;IAAlBf,EAAA,CAAAgB,iBAAA,CAAAC,MAAA,CAAAC,YAAA,CAAkB;;;;;IAwBtBlB,EAAA,CAAAC,cAAA,cAAiG;IAC/FD,EAAA,CAAAM,MAAA,GACF;IAAAN,EAAA,CAAAI,YAAA,EAAM;;;;IADJJ,EAAA,CAAAe,SAAA,GACF;IADEf,EAAA,CAAAmB,kBAAA,MAAAC,MAAA,CAAAC,eAAA,gBACF;;;;;IAwBIrB,EAAA,CAAAE,cAAA,EAAiF;IAAjFF,EAAA,CAAAC,cAAA,cAAiF;IAC/ED,EAAA,CAAAG,SAAA,eAA4G;IAE9GH,EAAA,CAAAI,YAAA,EAAM;;;;;IACNJ,EAAA,CAAAE,cAAA,EAAgF;IAAhFF,EAAA,CAAAC,cAAA,cAAgF;IAC9ED,EAAA,CAAAG,SAAA,eAA+P;IACjQH,EAAA,CAAAI,YAAA,EAAM;;;;;IAGVJ,EAAA,CAAAC,cAAA,cAAqG;IACnGD,EAAA,CAAAM,MAAA,GACF;IAAAN,EAAA,CAAAI,YAAA,EAAM;;;;IADJJ,EAAA,CAAAe,SAAA,GACF;IADEf,EAAA,CAAAmB,kBAAA,MAAAG,MAAA,CAAAD,eAAA,kBACF;;;;;IAeArB,EAAA,CAAAC,cAAA,WAAyB;IAAAD,EAAA,CAAAM,MAAA,YAAK;IAAAN,EAAA,CAAAI,YAAA,EAAO;;;;;IACrCJ,EAAA,CAAAC,cAAA,eAAgD;IAC9CD,EAAA,CAAAE,cAAA,EAAiD;IAAjDF,EAAA,CAAAC,cAAA,cAAiD;IAC/CD,EAAA,CAAAG,SAAA,iBAAkG;IAEpGH,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAM,MAAA,mBACF;IAAAN,EAAA,CAAAI,YAAA,EAAO;;;ADjIjB,OAAM,MAAOmB,cAAc;EAMzBC,YACUC,WAAwB,EACxBC,WAAwB,EACxBC,MAAc;IAFd,KAAAF,WAAW,GAAXA,WAAW;IACX,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IAPhB,KAAAC,SAAS,GAAG,KAAK;IACjB,KAAAV,YAAY,GAAG,EAAE;IACjB,KAAAW,YAAY,GAAG,KAAK;EAMjB;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,cAAc,EAAE;IAErB;IACA,IAAI,IAAI,CAACL,WAAW,CAACM,eAAe,EAAE,EAAE;MACtC,IAAI,CAACL,MAAM,CAACM,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;;EAE1C;EAEA;;;EAGQF,cAAcA,CAAA;IACpB,IAAI,CAACG,SAAS,GAAG,IAAI,CAACT,WAAW,CAACU,KAAK,CAAC;MACtCC,MAAM,EAAE,CAAC,EAAE,EAAE,CAACrC,UAAU,CAACsC,QAAQ,EAAEtC,UAAU,CAACuC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5DC,IAAI,EAAE,CAAC,EAAE,EAAE,CAACxC,UAAU,CAACsC,QAAQ,EAAEtC,UAAU,CAACuC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1DE,QAAQ,EAAE,CAAC,EAAE,EAAE,CAACzC,UAAU,CAACsC,QAAQ,EAAEtC,UAAU,CAACuC,SAAS,CAAC,CAAC,CAAC,CAAC;KAC9D,CAAC;EACJ;EAEA;;;EAGA,IAAIG,YAAYA,CAAA;IACd,OAAO,IAAI,CAACP,SAAS,CAACQ,QAAQ;EAChC;EAEA;;;EAGAC,wBAAwBA,CAAA;IACtB,IAAI,CAACd,YAAY,GAAG,CAAC,IAAI,CAACA,YAAY;EACxC;EAEA;;;EAGAe,QAAQA,CAAA;IACN,IAAI,IAAI,CAACV,SAAS,CAACW,KAAK,IAAI,CAAC,IAAI,CAACjB,SAAS,EAAE;MAC3C,IAAI,CAACA,SAAS,GAAG,IAAI;MACrB,IAAI,CAACV,YAAY,GAAG,EAAE;MAEtB;MACA,MAAM4B,WAAW,GAAgB;QAC/BC,MAAM,EAAE,IAAI,CAACb,SAAS,CAACc,KAAK,CAACZ,MAAM,CAACa,IAAI,EAAE;QAC1CC,IAAI,EAAE,IAAI,CAAChB,SAAS,CAACc,KAAK,CAACT,IAAI,CAACU,IAAI,EAAE;QACtCE,QAAQ,EAAE,IAAI,CAACjB,SAAS,CAACc,KAAK,CAACR;OAChC;MAED,IAAI,CAACd,WAAW,CAAC0B,YAAY,CAACN,WAAW,CAAC,CAACO,SAAS,CAAC;QACnDC,IAAI,EAAGC,QAAQ,IAAI;UACjB,IAAI,CAAC3B,SAAS,GAAG,KAAK;UAEtB,IAAI2B,QAAQ,CAACC,MAAM,CAACC,OAAO,EAAE;YAC3B;YACAC,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEJ,QAAQ,CAACK,IAAI,CAACC,QAAQ,CAAC;YAEzD;YACA,IAAI,CAAClC,MAAM,CAACM,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;WACvC,MAAM;YACL;YACA,IAAI,CAAC6B,cAAc,CAACP,QAAQ,CAAC;;QAEjC,CAAC;QACDQ,KAAK,EAAGA,KAAK,IAAI;UACf,IAAI,CAACnC,SAAS,GAAG,KAAK;UACtB,IAAI,CAACV,YAAY,GAAG6C,KAAK,CAACC,OAAO,IAAI,8CAA8C;UACnFN,OAAO,CAACK,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;QAC9C;OACD,CAAC;KACH,MAAM;MACL;MACA,IAAI,CAACE,oBAAoB,EAAE;;EAE/B;EAEA;;;EAGQH,cAAcA,CAACP,QAAa;IAClC,IAAIA,QAAQ,CAACC,MAAM,CAACU,QAAQ,IAAIX,QAAQ,CAACC,MAAM,CAACU,QAAQ,CAACC,MAAM,GAAG,CAAC,EAAE;MACnE;MACA,IAAI,CAACjD,YAAY,GAAGqC,QAAQ,CAACC,MAAM,CAACU,QAAQ,CAAC,CAAC,CAAC,CAACF,OAAO;KACxD,MAAM;MACL,IAAI,CAAC9C,YAAY,GAAG,8BAA8B;;EAEtD;EAEA;;;EAGQ+C,oBAAoBA,CAAA;IAC1BG,MAAM,CAACC,IAAI,CAAC,IAAI,CAACnC,SAAS,CAACQ,QAAQ,CAAC,CAAC4B,OAAO,CAACC,GAAG,IAAG;MACjD,MAAMC,OAAO,GAAG,IAAI,CAACtC,SAAS,CAACuC,GAAG,CAACF,GAAG,CAAC;MACvCC,OAAO,EAAEE,aAAa,EAAE;IAC1B,CAAC,CAAC;EACJ;EAEA;;;EAGAC,QAAQA,CAACC,SAAiB,EAAEC,SAAiB;IAC3C,MAAMC,KAAK,GAAG,IAAI,CAAC5C,SAAS,CAACuC,GAAG,CAACG,SAAS,CAAC;IAC3C,OAAO,CAAC,EAAEE,KAAK,EAAEH,QAAQ,CAACE,SAAS,CAAC,IAAIC,KAAK,EAAEC,OAAO,CAAC;EACzD;EAEA;;;EAGA1D,eAAeA,CAACuD,SAAiB;IAC/B,MAAME,KAAK,GAAG,IAAI,CAAC5C,SAAS,CAACuC,GAAG,CAACG,SAAS,CAAC;IAE3C,IAAIE,KAAK,EAAEH,QAAQ,CAAC,UAAU,CAAC,EAAE;MAC/B,OAAO,GAAG,IAAI,CAACK,mBAAmB,CAACJ,SAAS,CAAC,aAAa;;IAG5D,IAAIE,KAAK,EAAEH,QAAQ,CAAC,WAAW,CAAC,EAAE;MAChC,MAAMM,cAAc,GAAGH,KAAK,CAACI,MAAM,GAAG,WAAW,CAAC,EAAED,cAAc;MAClE,OAAO,GAAG,IAAI,CAACD,mBAAmB,CAACJ,SAAS,CAAC,2BAA2BK,cAAc,aAAa;;IAGrG,OAAO,EAAE;EACX;EAEA;;;EAGQD,mBAAmBA,CAACJ,SAAiB;IAC3C,MAAMO,YAAY,GAA8B;MAC9C/C,MAAM,EAAE,gBAAgB;MACxBG,IAAI,EAAE,qBAAqB;MAC3BC,QAAQ,EAAE;KACX;IAED,OAAO2C,YAAY,CAACP,SAAS,CAAC,IAAIA,SAAS;EAC7C;EAEA;;;EAGA9D,UAAUA,CAAA;IACR,IAAI,CAACI,YAAY,GAAG,EAAE;EACxB;;;uBA1JWK,cAAc,EAAAvB,EAAA,CAAAoF,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAtF,EAAA,CAAAoF,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAAxF,EAAA,CAAAoF,iBAAA,CAAAK,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAdnE,cAAc;MAAAoE,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCZ3BjG,EAAA,CAAAC,cAAA,aAA6B;UAICD,EAAA,CAAAM,MAAA,oBAAa;UAAAN,EAAA,CAAAI,YAAA,EAAK;UAC1CJ,EAAA,CAAAC,cAAA,WAA0B;UAAAD,EAAA,CAAAM,MAAA,mFAA4E;UAAAN,EAAA,CAAAI,YAAA,EAAI;UAI5GJ,EAAA,CAAAC,cAAA,aAAiC;UAC/BD,EAAA,CAAAE,cAAA,EAA2D;UAA3DF,EAAA,CAAAC,cAAA,aAA2D;UAEzDD,EAAA,CAAAG,SAAA,cAAgF;UAQlFH,EAAA,CAAAI,YAAA,EAAM;UAIRJ,EAAA,CAAAK,eAAA,EAAuB;UAAvBL,EAAA,CAAAC,cAAA,eAAuB;UACrBD,EAAA,CAAAE,cAAA,EAA6D;UAA7DF,EAAA,CAAAC,cAAA,eAA6D;UAE3DD,EAAA,CAAAG,SAAA,gBAA0G;UAQ5GH,EAAA,CAAAI,YAAA,EAAM;UAKVJ,EAAA,CAAAK,eAAA,EAAyB;UAAzBL,EAAA,CAAAC,cAAA,eAAyB;UAKjBD,EAAA,CAAAE,cAAA,EAA2D;UAA3DF,EAAA,CAAAC,cAAA,eAA2D;UACzDD,EAAA,CAAAG,SAAA,gBAA4G;UAC9GH,EAAA,CAAAI,YAAA,EAAM;UAERJ,EAAA,CAAAK,eAAA,EAA0B;UAA1BL,EAAA,CAAAC,cAAA,cAA0B;UAAAD,EAAA,CAAAM,MAAA,eAAO;UAAAN,EAAA,CAAAI,YAAA,EAAK;UACtCJ,EAAA,CAAAC,cAAA,aAA4B;UAAAD,EAAA,CAAAM,MAAA,wBAAgB;UAAAN,EAAA,CAAAI,YAAA,EAAI;UAIlDJ,EAAA,CAAAmG,UAAA,KAAAC,8BAAA,kBAMM;UAGNpG,EAAA,CAAAC,cAAA,gBAAoF;UAAtDD,EAAA,CAAAO,UAAA,sBAAA8F,kDAAA;YAAA,OAAYH,GAAA,CAAAtD,QAAA,EAAU;UAAA,EAAC;UAGnD5C,EAAA,CAAAC,cAAA,eAAwB;UACiBD,EAAA,CAAAM,MAAA,oBAAY;UAAAN,EAAA,CAAAI,YAAA,EAAQ;UAC3DJ,EAAA,CAAAC,cAAA,eAA6B;UAC3BD,EAAA,CAAAE,cAAA,EAA8E;UAA9EF,EAAA,CAAAC,cAAA,eAA8E;UAC5ED,EAAA,CAAAG,SAAA,gBAAyL;UAC3LH,EAAA,CAAAI,YAAA,EAAM;UACNJ,EAAA,CAAAK,eAAA,EAQC;UARDL,EAAA,CAAAG,SAAA,iBAQC;UACHH,EAAA,CAAAI,YAAA,EAAM;UACNJ,EAAA,CAAAmG,UAAA,KAAAG,8BAAA,kBAEM;UACRtG,EAAA,CAAAI,YAAA,EAAM;UAGNJ,EAAA,CAAAC,cAAA,eAAwB;UACmBD,EAAA,CAAAM,MAAA,gBAAQ;UAAAN,EAAA,CAAAI,YAAA,EAAQ;UACzDJ,EAAA,CAAAC,cAAA,eAA6B;UAC3BD,EAAA,CAAAE,cAAA,EAA8E;UAA9EF,EAAA,CAAAC,cAAA,eAA8E;UAC5ED,EAAA,CAAAG,SAAA,gBAAgL;UAClLH,EAAA,CAAAI,YAAA,EAAM;UACNJ,EAAA,CAAAK,eAAA,EAQC;UARDL,EAAA,CAAAG,SAAA,iBAQC;UACDH,EAAA,CAAAC,cAAA,kBAIC;UADCD,EAAA,CAAAO,UAAA,mBAAAgG,iDAAA;YAAA,OAASL,GAAA,CAAAvD,wBAAA,EAA0B;UAAA,EAAC;UAEpC3C,EAAA,CAAAmG,UAAA,KAAAK,mCAAA,kBAGM;UACNxG,EAAA,CAAAmG,UAAA,KAAAM,mCAAA,kBAEM;UACRzG,EAAA,CAAAI,YAAA,EAAS;UAEXJ,EAAA,CAAAmG,UAAA,KAAAO,8BAAA,kBAEM;UACR1G,EAAA,CAAAI,YAAA,EAAM;UAGNJ,EAAA,CAAAC,cAAA,eAA6B;UACKD,EAAA,CAAAM,MAAA,6BAAqB;UAAAN,EAAA,CAAAI,YAAA,EAAI;UAI3DJ,EAAA,CAAAC,cAAA,kBAKC;UACCD,EAAA,CAAAmG,UAAA,KAAAQ,+BAAA,mBAAqC;UACrC3G,EAAA,CAAAmG,UAAA,KAAAS,+BAAA,mBAMO;UACT5G,EAAA,CAAAI,YAAA,EAAS;UAGTJ,EAAA,CAAAC,cAAA,eAAqB;UACbD,EAAA,CAAAM,MAAA,UAAE;UAAAN,EAAA,CAAAI,YAAA,EAAO;UAIjBJ,EAAA,CAAAC,cAAA,eAA0B;UAEtBD,EAAA,CAAAE,cAAA,EAAyB;UAAzBF,EAAA,CAAAC,cAAA,eAAyB;UACvBD,EAAA,CAAAG,SAAA,gBAAkJ;UAIpJH,EAAA,CAAAI,YAAA,EAAM;UAGRJ,EAAA,CAAAK,eAAA,EAAsD;UAAtDL,EAAA,CAAAC,cAAA,kBAAsD;UACpDD,EAAA,CAAAE,cAAA,EAAwC;UAAxCF,EAAA,CAAAC,cAAA,eAAwC;UACtCD,EAAA,CAAAG,SAAA,gBAA0S;UAC5SH,EAAA,CAAAI,YAAA,EAAM;UAGRJ,EAAA,CAAAK,eAAA,EAAmD;UAAnDL,EAAA,CAAAC,cAAA,kBAAmD;UACjDD,EAAA,CAAAE,cAAA,EAAqC;UAArCF,EAAA,CAAAC,cAAA,eAAqC;UACnCD,EAAA,CAAAG,SAAA,gBAAouB;UACtuBH,EAAA,CAAAI,YAAA,EAAM;UAKVJ,EAAA,CAAAK,eAAA,EAA2B;UAA3BL,EAAA,CAAAC,cAAA,eAA2B;UACnBD,EAAA,CAAAM,MAAA,4BAAoB;UAAAN,EAAA,CAAAI,YAAA,EAAO;UACjCJ,EAAA,CAAAC,cAAA,aAAiC;UAAAD,EAAA,CAAAM,MAAA,oBAAY;UAAAN,EAAA,CAAAI,YAAA,EAAI;;;UA1H/CJ,EAAA,CAAAe,SAAA,IAAkB;UAAlBf,EAAA,CAAA6G,UAAA,SAAAX,GAAA,CAAAhF,YAAA,CAAkB;UASlBlB,EAAA,CAAAe,SAAA,GAAuB;UAAvBf,EAAA,CAAA6G,UAAA,cAAAX,GAAA,CAAAhE,SAAA,CAAuB;UAcrBlC,EAAA,CAAAe,SAAA,GAAiF;UAAjFf,EAAA,CAAA8G,WAAA,UAAAZ,GAAA,CAAAvB,QAAA,0BAAAuB,GAAA,CAAAvB,QAAA,wBAAiF;UAK/E3E,EAAA,CAAAe,SAAA,GAAqE;UAArEf,EAAA,CAAA6G,UAAA,SAAAX,GAAA,CAAAzD,YAAA,WAAAsC,OAAA,IAAAmB,GAAA,CAAAzD,YAAA,WAAAyC,MAAA,CAAqE;UAiBvElF,EAAA,CAAAe,SAAA,GAAqF;UAArFf,EAAA,CAAA8G,WAAA,UAAAZ,GAAA,CAAAvB,QAAA,4BAAAuB,GAAA,CAAAvB,QAAA,0BAAqF;UAJrF3E,EAAA,CAAA6G,UAAA,SAAAX,GAAA,CAAArE,YAAA,uBAA2C;UAarC7B,EAAA,CAAAe,SAAA,GAAmB;UAAnBf,EAAA,CAAA6G,UAAA,UAAAX,GAAA,CAAArE,YAAA,CAAmB;UAInB7B,EAAA,CAAAe,SAAA,GAAkB;UAAlBf,EAAA,CAAA6G,UAAA,SAAAX,GAAA,CAAArE,YAAA,CAAkB;UAKtB7B,EAAA,CAAAe,SAAA,GAAyE;UAAzEf,EAAA,CAAA6G,UAAA,SAAAX,GAAA,CAAAzD,YAAA,aAAAsC,OAAA,IAAAmB,GAAA,CAAAzD,YAAA,aAAAyC,MAAA,CAAyE;UAe/ElF,EAAA,CAAAe,SAAA,GAA2B;UAA3Bf,EAAA,CAAA8G,WAAA,YAAAZ,GAAA,CAAAtE,SAAA,CAA2B;UAD3B5B,EAAA,CAAA6G,UAAA,aAAAX,GAAA,CAAAtE,SAAA,CAAsB;UAGf5B,EAAA,CAAAe,SAAA,GAAgB;UAAhBf,EAAA,CAAA6G,UAAA,UAAAX,GAAA,CAAAtE,SAAA,CAAgB;UAChB5B,EAAA,CAAAe,SAAA,GAAe;UAAff,EAAA,CAAA6G,UAAA,SAAAX,GAAA,CAAAtE,SAAA,CAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}