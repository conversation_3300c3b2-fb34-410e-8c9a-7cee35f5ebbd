/* Variables CSS pour le composant de recherche de vols */
:host {
  --primary-blue: #4a6fa5;
  --secondary-blue: #7bb3f0;
  --dark-blue: #2c4a7a;
  --light-blue: #e8f4fd;
  --white: #ffffff;
  --light-gray: #f8f9fa;
  --medium-gray: #6c757d;
  --dark-gray: #343a40;
  --border-color: #dee2e6;
  --success-color: #28a745;
  --error-color: #dc3545;
  --error-bg: #f8d7da;
  --shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 4px 16px rgba(0, 0, 0, 0.15);
  --border-radius: 8px;
  --transition: all 0.3s ease;
}

/* Container principal */
.search-flight-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  padding: 2rem;
}

/* Header Section */
.search-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 2rem;
  gap: 2rem;
  background: var(--white);
  padding: 1.5rem 2rem;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
}

.search-title-section {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.search-icon {
  width: 3rem;
  height: 3rem;
  background: var(--primary-blue);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--white);
  box-shadow: 0 2px 8px rgba(74, 111, 165, 0.3);
}

.search-icon svg {
  width: 1.5rem;
  height: 1.5rem;
}

.search-title-content h1 {
  font-size: 1.75rem;
  font-weight: 600;
  color: var(--dark-gray);
  margin: 0 0 0.25rem 0;
}

.search-title-content p {
  font-size: 0.875rem;
  color: var(--medium-gray);
  margin: 0;
}

.latest-searches {
  text-align: right;
}

.latest-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--dark-gray);
  margin: 0 0 0.25rem 0;
}

.latest-subtitle {
  font-size: 0.875rem;
  color: var(--medium-gray);
  margin: 0;
}

/* Main Content */
.search-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 2rem;
  max-width: 1400px;
  margin: 0 auto;
}

/* Form Container */
.search-form-container {
  background: var(--white);
  border-radius: var(--border-radius);
  padding: 2rem;
  box-shadow: var(--shadow);
}

/* Error Message */
.error-message {
  background-color: var(--error-bg);
  border: 1px solid #feb2b2;
  color: var(--error-color);
  padding: 1rem;
  border-radius: var(--border-radius);
  font-size: 0.875rem;
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  margin-bottom: 1.5rem;
  position: relative;
}

.error-icon {
  width: 1.25rem;
  height: 1.25rem;
  flex-shrink: 0;
  margin-top: 0.125rem;
}

.error-close {
  position: absolute;
  top: 0.75rem;
  right: 0.75rem;
  background: none;
  border: none;
  color: var(--error-color);
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 0.25rem;
  transition: var(--transition);
}

.error-close:hover {
  background-color: rgba(229, 62, 62, 0.1);
}

.error-close svg {
  width: 1rem;
  height: 1rem;
}

/* Trip Type Tabs */
.trip-type-tabs {
  display: flex;
  margin-bottom: 2rem;
  border-radius: var(--border-radius);
  overflow: hidden;
  background: var(--light-gray);
  border: 1px solid var(--border-color);
}

.tab-button {
  flex: 1;
  padding: 1rem 1.5rem;
  background: transparent;
  border: none;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--medium-gray);
  cursor: pointer;
  transition: var(--transition);
  position: relative;
}

.tab-button.active {
  background: var(--dark-blue);
  color: var(--white);
  font-weight: 600;
}

.tab-button:hover:not(.active) {
  background: rgba(74, 111, 165, 0.1);
  color: var(--primary-blue);
}

/* Location Row */
.location-row {
  display: flex;
  align-items: flex-end;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.location-field {
  flex: 1;
}

.location-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--dark-gray);
  margin-bottom: 0.5rem;
}

.location-input-container {
  position: relative;
  display: flex;
  align-items: center;
  background: var(--white);
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius);
  transition: var(--transition);
}

.location-input-container:focus-within {
  border-color: var(--primary-blue);
  box-shadow: 0 0 0 3px rgba(74, 111, 165, 0.1);
}

.location-icon {
  position: absolute;
  left: 1rem;
  width: 1.25rem;
  height: 1.25rem;
  color: var(--primary-blue);
  z-index: 1;
}

.location-input {
  width: 100%;
  padding: 1rem 3rem 1rem 3rem;
  border: none;
  border-radius: var(--border-radius);
  font-size: 0.875rem;
  background: transparent;
  transition: var(--transition);
  font-weight: 500;
}

.location-input:focus {
  outline: none;
}

.location-input.error {
  border-color: var(--error-color);
}

.location-button {
  position: absolute;
  right: 0.5rem;
  background: none;
  border: none;
  padding: 0.5rem;
  cursor: pointer;
  color: var(--medium-gray);
  border-radius: 0.25rem;
  transition: var(--transition);
}

.location-button:hover {
  background: var(--light-gray);
  color: var(--primary-blue);
}

.location-button svg {
  width: 1rem;
  height: 1rem;
}

/* Swap Button */
.swap-button {
  background: var(--white);
  border: 2px solid var(--primary-blue);
  border-radius: 50%;
  width: 2.5rem;
  height: 2.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--primary-blue);
  cursor: pointer;
  transition: var(--transition);
  margin-bottom: 1.5rem;
  box-shadow: 0 2px 8px rgba(74, 111, 165, 0.2);
}

.swap-button:hover {
  background: var(--primary-blue);
  color: var(--white);
  transform: rotate(180deg);
  box-shadow: 0 4px 12px rgba(74, 111, 165, 0.3);
}

.swap-button svg {
  width: 1.25rem;
  height: 1.25rem;
}

/* Date Row */
.date-row {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.date-field {
  flex: 1;
}

.date-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--dark-gray);
  margin-bottom: 0.5rem;
}

.date-input {
  width: 100%;
  padding: 1rem;
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius);
  font-size: 0.875rem;
  background: var(--white);
  transition: var(--transition);
  font-weight: 500;
}

.date-input:focus {
  outline: none;
  border-color: var(--primary-blue);
  box-shadow: 0 0 0 3px rgba(74, 111, 165, 0.1);
}

.date-input.error {
  border-color: var(--error-color);
}

/* Passenger & Class Row */
.passenger-class-row {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.passenger-field {
  flex: 2;
}

.passenger-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--dark-gray);
  margin-bottom: 0.5rem;
}

.passenger-display {
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 1rem;
  background: var(--white);
  transition: var(--transition);
}

.passenger-display:focus-within {
  border-color: var(--primary-blue);
  box-shadow: 0 0 0 3px rgba(74, 111, 165, 0.1);
}

.passenger-icons {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  flex-wrap: wrap;
}

.passenger-group,
.class-group {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.passenger-icon,
.class-icon {
  width: 1.25rem;
  height: 1.25rem;
  color: var(--primary-blue);
}

.passenger-select,
.class-select {
  border: 1px solid var(--border-color);
  border-radius: 0.375rem;
  padding: 0.375rem 0.75rem;
  font-size: 0.875rem;
  background: var(--white);
  min-width: 3.5rem;
  font-weight: 500;
  transition: var(--transition);
}

.passenger-select:focus,
.class-select:focus {
  outline: none;
  border-color: var(--primary-blue);
}

/* Airline Field */
.airline-field {
  flex: 1;
}

.airline-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--dark-gray);
  margin-bottom: 0.5rem;
}

.airline-select {
  width: 100%;
  padding: 1rem;
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius);
  font-size: 0.875rem;
  background: var(--white);
  transition: var(--transition);
  font-weight: 500;
}

.airline-select:focus {
  outline: none;
  border-color: var(--primary-blue);
  box-shadow: 0 0 0 3px rgba(74, 111, 165, 0.1);
}

/* Options Row */
.options-row {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
}

.option-group {
  flex: 1;
}

.option-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--dark-gray);
  margin-bottom: 0.5rem;
}

.option-select {
  width: 100%;
  padding: 1rem;
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius);
  font-size: 0.875rem;
  background: var(--white);
  transition: var(--transition);
  font-weight: 500;
}

.option-select:focus {
  outline: none;
  border-color: var(--primary-blue);
  box-shadow: 0 0 0 3px rgba(74, 111, 165, 0.1);
}

.calendar-display {
  padding: 1rem;
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius);
  font-size: 0.875rem;
  background: var(--light-gray);
  color: var(--primary-blue);
  text-align: center;
  font-weight: 600;
}

/* Search Button */
.search-button {
  width: 100%;
  background: linear-gradient(135deg, var(--success-color) 0%, #20c997 100%);
  color: var(--white);
  border: none;
  padding: 1.25rem 2rem;
  border-radius: var(--border-radius);
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition);
  min-height: 3.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
}

.search-button:hover:not(:disabled) {
  background: linear-gradient(135deg, #218838 0%, #1e7e34 100%);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
}

.search-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.loading-content {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.loading-spinner {
  width: 1.25rem;
  height: 1.25rem;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Latest Searches Sidebar */
.latest-searches-sidebar {
  background: var(--white);
  border-radius: var(--border-radius);
  padding: 2rem;
  box-shadow: var(--shadow);
  height: fit-content;
  border: 1px solid var(--border-color);
}

.latest-searches-content {
  text-align: center;
  padding: 2rem 0;
}

.no-searches {
  color: var(--medium-gray);
  font-style: italic;
  font-size: 0.875rem;
}

/* Field Errors */
.field-error {
  color: var(--error-color);
  font-size: 0.75rem;
  margin-top: 0.25rem;
  font-weight: 500;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .search-content {
    grid-template-columns: 1fr;
  }
  
  .latest-searches-sidebar {
    order: -1;
  }
}

@media (max-width: 768px) {
  .search-flight-container {
    padding: 1rem;
  }
  
  .search-header {
    flex-direction: column;
    text-align: center;
  }
  
  .location-row {
    flex-direction: column;
  }
  
  .swap-button {
    align-self: center;
    margin: 0.5rem 0;
  }
  
  .passenger-class-row {
    flex-direction: column;
  }
  
  .options-row {
    flex-direction: column;
  }
  
  .passenger-icons {
    flex-wrap: wrap;
    gap: 0.5rem;
  }
}
