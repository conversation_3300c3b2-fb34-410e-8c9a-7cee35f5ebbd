/* Variables CSS pour le composant de recherche de vols */
:root {
  --primary-blue: #4a90e2;
  --secondary-blue: #7bb3f0;
  --dark-blue: #2c5aa0;
  --light-blue: #e8f4fd;
  --white: #ffffff;
  --light-gray: #f5f7fa;
  --medium-gray: #8fa4b3;
  --dark-gray: #4a5568;
  --border-color: #e2e8f0;
  --success-color: #48bb78;
  --error-color: #e53e3e;
  --error-bg: #fed7d7;
  --shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.15);
  --border-radius: 8px;
  --transition: all 0.3s ease;
}

/* Container principal */
.search-flight-container {
  min-height: 100vh;
  background: var(--light-gray);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  padding: 2rem;
}

/* Header Section */
.search-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 2rem;
  gap: 2rem;
}

.search-title-section {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.search-icon {
  width: 3rem;
  height: 3rem;
  background: var(--primary-blue);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--white);
}

.search-icon svg {
  width: 1.5rem;
  height: 1.5rem;
}

.search-title-content h1 {
  font-size: 2rem;
  font-weight: 700;
  color: var(--dark-gray);
  margin: 0 0 0.25rem 0;
}

.search-title-content p {
  font-size: 0.875rem;
  color: var(--medium-gray);
  margin: 0;
}

.latest-searches {
  text-align: right;
}

.latest-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--dark-gray);
  margin: 0 0 0.25rem 0;
}

.latest-subtitle {
  font-size: 0.875rem;
  color: var(--medium-gray);
  margin: 0;
}

/* Main Content */
.search-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 2rem;
  max-width: 1400px;
  margin: 0 auto;
}

/* Form Container */
.search-form-container {
  background: var(--white);
  border-radius: var(--border-radius);
  padding: 2rem;
  box-shadow: var(--shadow);
}

/* Error Message */
.error-message {
  background-color: var(--error-bg);
  border: 1px solid #feb2b2;
  color: var(--error-color);
  padding: 1rem;
  border-radius: var(--border-radius);
  font-size: 0.875rem;
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  margin-bottom: 1.5rem;
  position: relative;
}

.error-icon {
  width: 1.25rem;
  height: 1.25rem;
  flex-shrink: 0;
  margin-top: 0.125rem;
}

.error-close {
  position: absolute;
  top: 0.75rem;
  right: 0.75rem;
  background: none;
  border: none;
  color: var(--error-color);
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 0.25rem;
  transition: var(--transition);
}

.error-close:hover {
  background-color: rgba(229, 62, 62, 0.1);
}

.error-close svg {
  width: 1rem;
  height: 1rem;
}

/* Trip Type Tabs */
.trip-type-tabs {
  display: flex;
  margin-bottom: 2rem;
  border-radius: var(--border-radius);
  overflow: hidden;
  border: 1px solid var(--border-color);
}

.tab-button {
  flex: 1;
  padding: 0.875rem 1.5rem;
  background: var(--white);
  border: none;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--medium-gray);
  cursor: pointer;
  transition: var(--transition);
  border-right: 1px solid var(--border-color);
}

.tab-button:last-child {
  border-right: none;
}

.tab-button.active {
  background: var(--dark-blue);
  color: var(--white);
}

.tab-button:hover:not(.active) {
  background: var(--light-gray);
}

/* Location Row */
.location-row {
  display: flex;
  align-items: flex-end;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.location-field {
  flex: 1;
}

.location-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--dark-gray);
  margin-bottom: 0.5rem;
}

.location-input-container {
  position: relative;
  display: flex;
  align-items: center;
}

.location-icon {
  position: absolute;
  left: 1rem;
  width: 1.25rem;
  height: 1.25rem;
  color: var(--medium-gray);
  z-index: 1;
}

.location-input {
  width: 100%;
  padding: 0.875rem 3rem 0.875rem 3rem;
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius);
  font-size: 0.875rem;
  background: var(--white);
  transition: var(--transition);
}

.location-input:focus {
  outline: none;
  border-color: var(--primary-blue);
  box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.1);
}

.location-input.error {
  border-color: var(--error-color);
}

.location-button {
  position: absolute;
  right: 0.5rem;
  background: none;
  border: none;
  padding: 0.5rem;
  cursor: pointer;
  color: var(--medium-gray);
  border-radius: 0.25rem;
  transition: var(--transition);
}

.location-button:hover {
  background: var(--light-gray);
  color: var(--primary-blue);
}

.location-button svg {
  width: 1rem;
  height: 1rem;
}

/* Swap Button */
.swap-button {
  background: var(--primary-blue);
  border: none;
  border-radius: 50%;
  width: 2.5rem;
  height: 2.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--white);
  cursor: pointer;
  transition: var(--transition);
  margin-bottom: 1.5rem;
}

.swap-button:hover {
  background: var(--dark-blue);
  transform: rotate(180deg);
}

.swap-button svg {
  width: 1.25rem;
  height: 1.25rem;
}

/* Date Row */
.date-row {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.date-field {
  flex: 1;
}

.date-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--dark-gray);
  margin-bottom: 0.5rem;
}

.date-input {
  width: 100%;
  padding: 0.875rem 1rem;
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius);
  font-size: 0.875rem;
  background: var(--white);
  transition: var(--transition);
}

.date-input:focus {
  outline: none;
  border-color: var(--primary-blue);
  box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.1);
}

.date-input.error {
  border-color: var(--error-color);
}

/* Passenger & Class Row */
.passenger-class-row {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.passenger-field {
  flex: 2;
}

.passenger-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--dark-gray);
  margin-bottom: 0.5rem;
}

.passenger-display {
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 0.875rem 1rem;
  background: var(--white);
}

.passenger-icons {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.passenger-group,
.class-group {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.passenger-icon,
.class-icon {
  width: 1.25rem;
  height: 1.25rem;
  color: var(--medium-gray);
}

.passenger-select,
.class-select {
  border: 1px solid var(--border-color);
  border-radius: 0.25rem;
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
  background: var(--white);
  min-width: 3rem;
}

/* Airline Field */
.airline-field {
  flex: 1;
}

.airline-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--dark-gray);
  margin-bottom: 0.5rem;
}

.airline-select {
  width: 100%;
  padding: 0.875rem 1rem;
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius);
  font-size: 0.875rem;
  background: var(--white);
  transition: var(--transition);
}

.airline-select:focus {
  outline: none;
  border-color: var(--primary-blue);
  box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.1);
}

/* Options Row */
.options-row {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
}

.option-group {
  flex: 1;
}

.option-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--dark-gray);
  margin-bottom: 0.5rem;
}

.option-select {
  width: 100%;
  padding: 0.875rem 1rem;
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius);
  font-size: 0.875rem;
  background: var(--white);
  transition: var(--transition);
}

.option-select:focus {
  outline: none;
  border-color: var(--primary-blue);
  box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.1);
}

.calendar-display {
  padding: 0.875rem 1rem;
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius);
  font-size: 0.875rem;
  background: var(--light-gray);
  color: var(--medium-gray);
  text-align: center;
}

/* Search Button */
.search-button {
  width: 100%;
  background: var(--success-color);
  color: var(--white);
  border: none;
  padding: 1rem 2rem;
  border-radius: var(--border-radius);
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition);
  min-height: 3.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.search-button:hover:not(:disabled) {
  background: #38a169;
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.search-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.loading-content {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.loading-spinner {
  width: 1.25rem;
  height: 1.25rem;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Latest Searches Sidebar */
.latest-searches-sidebar {
  background: var(--white);
  border-radius: var(--border-radius);
  padding: 2rem;
  box-shadow: var(--shadow);
  height: fit-content;
}

.latest-searches-content {
  text-align: center;
}

.no-searches {
  color: var(--medium-gray);
  font-style: italic;
}

/* Field Errors */
.field-error {
  color: var(--error-color);
  font-size: 0.75rem;
  margin-top: 0.25rem;
  font-weight: 500;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .search-content {
    grid-template-columns: 1fr;
  }
  
  .latest-searches-sidebar {
    order: -1;
  }
}

@media (max-width: 768px) {
  .search-flight-container {
    padding: 1rem;
  }
  
  .search-header {
    flex-direction: column;
    text-align: center;
  }
  
  .location-row {
    flex-direction: column;
  }
  
  .swap-button {
    align-self: center;
    margin: 0.5rem 0;
  }
  
  .passenger-class-row {
    flex-direction: column;
  }
  
  .options-row {
    flex-direction: column;
  }
  
  .passenger-icons {
    flex-wrap: wrap;
    gap: 0.5rem;
  }
}
