import { ComponentFixture, TestBed } from '@angular/core/testing';
import { Router } from '@angular/router';
import { AuthService } from '../../services/auth.service';
import { DashboardComponent } from './dashboard.component';

describe('DashboardComponent', () => {
  let component: DashboardComponent;
  let fixture: ComponentFixture<DashboardComponent>;
  let authService: jasmine.SpyObj<AuthService>;
  let router: jasmine.SpyObj<Router>;

  const mockUserInfo = {
    code: 'USER001',
    name: 'Test User',
    agency: { code: 'AG001', name: 'Test Agency', registerCode: 'REG001' },
    office: { code: 'OFF001', name: 'Test Office' },
    operator: { code: 'OP001', name: 'Test Operator', thumbnail: 'thumb.jpg' },
    market: { code: 'MK001', name: 'Test Market', favicon: 'favicon.ico' }
  };

  beforeEach(async () => {
    const authServiceSpy = jasmine.createSpyObj('AuthService', ['getUserInfo', 'logout']);
    const routerSpy = jasmine.createSpyObj('Router', ['navigate']);

    await TestBed.configureTestingModule({
      declarations: [DashboardComponent],
      providers: [
        { provide: AuthService, useValue: authServiceSpy },
        { provide: Router, useValue: routerSpy }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(DashboardComponent);
    component = fixture.componentInstance;
    authService = TestBed.inject(AuthService) as jasmine.SpyObj<AuthService>;
    router = TestBed.inject(Router) as jasmine.SpyObj<Router>;
  });

  beforeEach(() => {
    authService.getUserInfo.and.returnValue(mockUserInfo);
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should load user info on init', () => {
    component.ngOnInit();
    expect(authService.getUserInfo).toHaveBeenCalled();
    expect(component.userInfo).toEqual(mockUserInfo);
  });

  it('should logout and redirect to login', () => {
    component.logout();
    expect(authService.logout).toHaveBeenCalled();
    expect(router.navigate).toHaveBeenCalledWith(['/login']);
  });
});
