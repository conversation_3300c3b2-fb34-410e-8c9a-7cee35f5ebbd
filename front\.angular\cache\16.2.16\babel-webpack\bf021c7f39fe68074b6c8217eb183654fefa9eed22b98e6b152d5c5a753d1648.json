{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./dashboard.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./dashboard.component.css?ngResource\";\nimport { Component } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { AuthService } from '../../services/auth.service';\nexport let DashboardComponent = class DashboardComponent {\n  constructor(authService, router) {\n    this.authService = authService;\n    this.router = router;\n  }\n  ngOnInit() {\n    this.userInfo = this.authService.getUserInfo();\n  }\n  logout() {\n    this.authService.logout();\n    this.router.navigate(['/login']);\n  }\n  static {\n    this.ctorParameters = () => [{\n      type: AuthService\n    }, {\n      type: Router\n    }];\n  }\n};\nDashboardComponent = __decorate([Component({\n  selector: 'app-dashboard',\n  template: __NG_CLI_RESOURCE__0,\n  styles: [__NG_CLI_RESOURCE__1]\n})], DashboardComponent);", "map": {"version": 3, "names": ["Component", "Router", "AuthService", "DashboardComponent", "constructor", "authService", "router", "ngOnInit", "userInfo", "getUserInfo", "logout", "navigate", "__decorate", "selector", "template", "__NG_CLI_RESOURCE__0"], "sources": ["C:\\Users\\<USER>\\Desktop\\angular\\front\\src\\app\\components\\dashboard\\dashboard.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { AuthService } from '../../services/auth.service';\n\n@Component({\n  selector: 'app-dashboard',\n  templateUrl: './dashboard.component.html',\n  styleUrls: ['./dashboard.component.css']\n})\nexport class DashboardComponent implements OnInit {\n  userInfo: any;\n\n  constructor(\n    private authService: AuthService,\n    private router: Router\n  ) {}\n\n  ngOnInit(): void {\n    this.userInfo = this.authService.getUserInfo();\n  }\n\n  logout(): void {\n    this.authService.logout();\n    this.router.navigate(['/login']);\n  }\n}\n"], "mappings": ";;;AAAA,SAASA,SAAS,QAAgB,eAAe;AACjD,SAASC,MAAM,QAAQ,iBAAiB;AACxC,SAASC,WAAW,QAAQ,6BAA6B;AAOlD,WAAMC,kBAAkB,GAAxB,MAAMA,kBAAkB;EAG7BC,YACUC,WAAwB,EACxBC,MAAc;IADd,KAAAD,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;EACb;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,QAAQ,GAAG,IAAI,CAACH,WAAW,CAACI,WAAW,EAAE;EAChD;EAEAC,MAAMA,CAAA;IACJ,IAAI,CAACL,WAAW,CAACK,MAAM,EAAE;IACzB,IAAI,CAACJ,MAAM,CAACK,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;EAClC;;;;;;;;;AAfWR,kBAAkB,GAAAS,UAAA,EAL9BZ,SAAS,CAAC;EACTa,QAAQ,EAAE,eAAe;EACzBC,QAAA,EAAAC,oBAAyC;;CAE1C,CAAC,C,EACWZ,kBAAkB,CAgB9B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}