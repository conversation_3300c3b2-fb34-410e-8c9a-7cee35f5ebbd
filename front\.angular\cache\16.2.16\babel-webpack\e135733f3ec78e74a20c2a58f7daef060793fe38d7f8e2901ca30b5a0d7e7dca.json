{"ast": null, "code": "/**\n * @license Angular v16.2.12\n * (c) 2010-2022 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport * as i0 from '@angular/core';\nimport { EventEmitter, Injectable, InjectionToken, Inject, Optional } from '@angular/core';\nimport { LocationStrategy, Location } from '@angular/common';\nimport { Subject } from 'rxjs';\n\n/**\n * Joins two parts of a URL with a slash if needed.\n *\n * @param start  URL string\n * @param end    URL string\n *\n *\n * @returns The joined URL string.\n */\nfunction joinWithSlash(start, end) {\n  if (start.length == 0) {\n    return end;\n  }\n  if (end.length == 0) {\n    return start;\n  }\n  let slashes = 0;\n  if (start.endsWith('/')) {\n    slashes++;\n  }\n  if (end.startsWith('/')) {\n    slashes++;\n  }\n  if (slashes == 2) {\n    return start + end.substring(1);\n  }\n  if (slashes == 1) {\n    return start + end;\n  }\n  return start + '/' + end;\n}\n/**\n * Removes a trailing slash from a URL string if needed.\n * Looks for the first occurrence of either `#`, `?`, or the end of the\n * line as `/` characters and removes the trailing slash if one exists.\n *\n * @param url URL string.\n *\n * @returns The URL string, modified if needed.\n */\nfunction stripTrailingSlash(url) {\n  const match = url.match(/#|\\?|$/);\n  const pathEndIdx = match && match.index || url.length;\n  const droppedSlashIdx = pathEndIdx - (url[pathEndIdx - 1] === '/' ? 1 : 0);\n  return url.slice(0, droppedSlashIdx) + url.slice(pathEndIdx);\n}\n/**\n * Normalizes URL parameters by prepending with `?` if needed.\n *\n * @param  params String of URL parameters.\n *\n * @returns The normalized URL parameters string.\n */\nfunction normalizeQueryParams(params) {\n  return params && params[0] !== '?' ? '?' + params : params;\n}\n\n/**\n * A spy for {@link Location} that allows tests to fire simulated location events.\n *\n * @publicApi\n */\nclass SpyLocation {\n  constructor() {\n    this.urlChanges = [];\n    this._history = [new LocationState('', '', null)];\n    this._historyIndex = 0;\n    /** @internal */\n    this._subject = new EventEmitter();\n    /** @internal */\n    this._basePath = '';\n    /** @internal */\n    this._locationStrategy = null;\n    /** @internal */\n    this._urlChangeListeners = [];\n    /** @internal */\n    this._urlChangeSubscription = null;\n  }\n  /** @nodoc */\n  ngOnDestroy() {\n    this._urlChangeSubscription?.unsubscribe();\n    this._urlChangeListeners = [];\n  }\n  setInitialPath(url) {\n    this._history[this._historyIndex].path = url;\n  }\n  setBaseHref(url) {\n    this._basePath = url;\n  }\n  path() {\n    return this._history[this._historyIndex].path;\n  }\n  getState() {\n    return this._history[this._historyIndex].state;\n  }\n  isCurrentPathEqualTo(path, query = '') {\n    const givenPath = path.endsWith('/') ? path.substring(0, path.length - 1) : path;\n    const currPath = this.path().endsWith('/') ? this.path().substring(0, this.path().length - 1) : this.path();\n    return currPath == givenPath + (query.length > 0 ? '?' + query : '');\n  }\n  simulateUrlPop(pathname) {\n    this._subject.emit({\n      'url': pathname,\n      'pop': true,\n      'type': 'popstate'\n    });\n  }\n  simulateHashChange(pathname) {\n    const path = this.prepareExternalUrl(pathname);\n    this.pushHistory(path, '', null);\n    this.urlChanges.push('hash: ' + pathname);\n    // the browser will automatically fire popstate event before each `hashchange` event, so we need\n    // to simulate it.\n    this._subject.emit({\n      'url': pathname,\n      'pop': true,\n      'type': 'popstate'\n    });\n    this._subject.emit({\n      'url': pathname,\n      'pop': true,\n      'type': 'hashchange'\n    });\n  }\n  prepareExternalUrl(url) {\n    if (url.length > 0 && !url.startsWith('/')) {\n      url = '/' + url;\n    }\n    return this._basePath + url;\n  }\n  go(path, query = '', state = null) {\n    path = this.prepareExternalUrl(path);\n    this.pushHistory(path, query, state);\n    const locationState = this._history[this._historyIndex - 1];\n    if (locationState.path == path && locationState.query == query) {\n      return;\n    }\n    const url = path + (query.length > 0 ? '?' + query : '');\n    this.urlChanges.push(url);\n    this._notifyUrlChangeListeners(path + normalizeQueryParams(query), state);\n  }\n  replaceState(path, query = '', state = null) {\n    path = this.prepareExternalUrl(path);\n    const history = this._history[this._historyIndex];\n    history.state = state;\n    if (history.path == path && history.query == query) {\n      return;\n    }\n    history.path = path;\n    history.query = query;\n    const url = path + (query.length > 0 ? '?' + query : '');\n    this.urlChanges.push('replace: ' + url);\n    this._notifyUrlChangeListeners(path + normalizeQueryParams(query), state);\n  }\n  forward() {\n    if (this._historyIndex < this._history.length - 1) {\n      this._historyIndex++;\n      this._subject.emit({\n        'url': this.path(),\n        'state': this.getState(),\n        'pop': true,\n        'type': 'popstate'\n      });\n    }\n  }\n  back() {\n    if (this._historyIndex > 0) {\n      this._historyIndex--;\n      this._subject.emit({\n        'url': this.path(),\n        'state': this.getState(),\n        'pop': true,\n        'type': 'popstate'\n      });\n    }\n  }\n  historyGo(relativePosition = 0) {\n    const nextPageIndex = this._historyIndex + relativePosition;\n    if (nextPageIndex >= 0 && nextPageIndex < this._history.length) {\n      this._historyIndex = nextPageIndex;\n      this._subject.emit({\n        'url': this.path(),\n        'state': this.getState(),\n        'pop': true,\n        'type': 'popstate'\n      });\n    }\n  }\n  onUrlChange(fn) {\n    this._urlChangeListeners.push(fn);\n    if (!this._urlChangeSubscription) {\n      this._urlChangeSubscription = this.subscribe(v => {\n        this._notifyUrlChangeListeners(v.url, v.state);\n      });\n    }\n    return () => {\n      const fnIndex = this._urlChangeListeners.indexOf(fn);\n      this._urlChangeListeners.splice(fnIndex, 1);\n      if (this._urlChangeListeners.length === 0) {\n        this._urlChangeSubscription?.unsubscribe();\n        this._urlChangeSubscription = null;\n      }\n    };\n  }\n  /** @internal */\n  _notifyUrlChangeListeners(url = '', state) {\n    this._urlChangeListeners.forEach(fn => fn(url, state));\n  }\n  subscribe(onNext, onThrow, onReturn) {\n    return this._subject.subscribe({\n      next: onNext,\n      error: onThrow,\n      complete: onReturn\n    });\n  }\n  normalize(url) {\n    return null;\n  }\n  pushHistory(path, query, state) {\n    if (this._historyIndex > 0) {\n      this._history.splice(this._historyIndex + 1);\n    }\n    this._history.push(new LocationState(path, query, state));\n    this._historyIndex = this._history.length - 1;\n  }\n  static {\n    this.ɵfac = function SpyLocation_Factory(t) {\n      return new (t || SpyLocation)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: SpyLocation,\n      factory: SpyLocation.ɵfac\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(SpyLocation, [{\n    type: Injectable\n  }], null, null);\n})();\nclass LocationState {\n  constructor(path, query, state) {\n    this.path = path;\n    this.query = query;\n    this.state = state;\n  }\n}\n\n/**\n * A mock implementation of {@link LocationStrategy} that allows tests to fire simulated\n * location events.\n *\n * @publicApi\n */\nclass MockLocationStrategy extends LocationStrategy {\n  constructor() {\n    super();\n    this.internalBaseHref = '/';\n    this.internalPath = '/';\n    this.internalTitle = '';\n    this.urlChanges = [];\n    /** @internal */\n    this._subject = new EventEmitter();\n    this.stateChanges = [];\n  }\n  simulatePopState(url) {\n    this.internalPath = url;\n    this._subject.emit(new _MockPopStateEvent(this.path()));\n  }\n  path(includeHash = false) {\n    return this.internalPath;\n  }\n  prepareExternalUrl(internal) {\n    if (internal.startsWith('/') && this.internalBaseHref.endsWith('/')) {\n      return this.internalBaseHref + internal.substring(1);\n    }\n    return this.internalBaseHref + internal;\n  }\n  pushState(ctx, title, path, query) {\n    // Add state change to changes array\n    this.stateChanges.push(ctx);\n    this.internalTitle = title;\n    const url = path + (query.length > 0 ? '?' + query : '');\n    this.internalPath = url;\n    const externalUrl = this.prepareExternalUrl(url);\n    this.urlChanges.push(externalUrl);\n  }\n  replaceState(ctx, title, path, query) {\n    // Reset the last index of stateChanges to the ctx (state) object\n    this.stateChanges[(this.stateChanges.length || 1) - 1] = ctx;\n    this.internalTitle = title;\n    const url = path + (query.length > 0 ? '?' + query : '');\n    this.internalPath = url;\n    const externalUrl = this.prepareExternalUrl(url);\n    this.urlChanges.push('replace: ' + externalUrl);\n  }\n  onPopState(fn) {\n    this._subject.subscribe({\n      next: fn\n    });\n  }\n  getBaseHref() {\n    return this.internalBaseHref;\n  }\n  back() {\n    if (this.urlChanges.length > 0) {\n      this.urlChanges.pop();\n      this.stateChanges.pop();\n      const nextUrl = this.urlChanges.length > 0 ? this.urlChanges[this.urlChanges.length - 1] : '';\n      this.simulatePopState(nextUrl);\n    }\n  }\n  forward() {\n    throw 'not implemented';\n  }\n  getState() {\n    return this.stateChanges[(this.stateChanges.length || 1) - 1];\n  }\n  static {\n    this.ɵfac = function MockLocationStrategy_Factory(t) {\n      return new (t || MockLocationStrategy)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: MockLocationStrategy,\n      factory: MockLocationStrategy.ɵfac\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MockLocationStrategy, [{\n    type: Injectable\n  }], function () {\n    return [];\n  }, null);\n})();\nclass _MockPopStateEvent {\n  constructor(newUrl) {\n    this.newUrl = newUrl;\n    this.pop = true;\n    this.type = 'popstate';\n  }\n}\n\n/**\n * Parser from https://tools.ietf.org/html/rfc3986#appendix-B\n * ^(([^:/?#]+):)?(//([^/?#]*))?([^?#]*)(\\?([^#]*))?(#(.*))?\n *  12            3  4          5       6  7        8 9\n *\n * Example: http://www.ics.uci.edu/pub/ietf/uri/#Related\n *\n * Results in:\n *\n * $1 = http:\n * $2 = http\n * $3 = //www.ics.uci.edu\n * $4 = www.ics.uci.edu\n * $5 = /pub/ietf/uri/\n * $6 = <undefined>\n * $7 = <undefined>\n * $8 = #Related\n * $9 = Related\n */\nconst urlParse = /^(([^:\\/?#]+):)?(\\/\\/([^\\/?#]*))?([^?#]*)(\\?([^#]*))?(#(.*))?/;\nfunction parseUrl(urlStr, baseHref) {\n  const verifyProtocol = /^((http[s]?|ftp):\\/\\/)/;\n  let serverBase;\n  // URL class requires full URL. If the URL string doesn't start with protocol, we need to add\n  // an arbitrary base URL which can be removed afterward.\n  if (!verifyProtocol.test(urlStr)) {\n    serverBase = 'http://empty.com/';\n  }\n  let parsedUrl;\n  try {\n    parsedUrl = new URL(urlStr, serverBase);\n  } catch (e) {\n    const result = urlParse.exec(serverBase || '' + urlStr);\n    if (!result) {\n      throw new Error(`Invalid URL: ${urlStr} with base: ${baseHref}`);\n    }\n    const hostSplit = result[4].split(':');\n    parsedUrl = {\n      protocol: result[1],\n      hostname: hostSplit[0],\n      port: hostSplit[1] || '',\n      pathname: result[5],\n      search: result[6],\n      hash: result[8]\n    };\n  }\n  if (parsedUrl.pathname && parsedUrl.pathname.indexOf(baseHref) === 0) {\n    parsedUrl.pathname = parsedUrl.pathname.substring(baseHref.length);\n  }\n  return {\n    hostname: !serverBase && parsedUrl.hostname || '',\n    protocol: !serverBase && parsedUrl.protocol || '',\n    port: !serverBase && parsedUrl.port || '',\n    pathname: parsedUrl.pathname || '/',\n    search: parsedUrl.search || '',\n    hash: parsedUrl.hash || ''\n  };\n}\n/**\n * Provider for mock platform location config\n *\n * @publicApi\n */\nconst MOCK_PLATFORM_LOCATION_CONFIG = new InjectionToken('MOCK_PLATFORM_LOCATION_CONFIG');\n/**\n * Mock implementation of URL state.\n *\n * @publicApi\n */\nclass MockPlatformLocation {\n  constructor(config) {\n    this.baseHref = '';\n    this.hashUpdate = new Subject();\n    this.popStateSubject = new Subject();\n    this.urlChangeIndex = 0;\n    this.urlChanges = [{\n      hostname: '',\n      protocol: '',\n      port: '',\n      pathname: '/',\n      search: '',\n      hash: '',\n      state: null\n    }];\n    if (config) {\n      this.baseHref = config.appBaseHref || '';\n      const parsedChanges = this.parseChanges(null, config.startUrl || 'http://_empty_/', this.baseHref);\n      this.urlChanges[0] = {\n        ...parsedChanges\n      };\n    }\n  }\n  get hostname() {\n    return this.urlChanges[this.urlChangeIndex].hostname;\n  }\n  get protocol() {\n    return this.urlChanges[this.urlChangeIndex].protocol;\n  }\n  get port() {\n    return this.urlChanges[this.urlChangeIndex].port;\n  }\n  get pathname() {\n    return this.urlChanges[this.urlChangeIndex].pathname;\n  }\n  get search() {\n    return this.urlChanges[this.urlChangeIndex].search;\n  }\n  get hash() {\n    return this.urlChanges[this.urlChangeIndex].hash;\n  }\n  get state() {\n    return this.urlChanges[this.urlChangeIndex].state;\n  }\n  getBaseHrefFromDOM() {\n    return this.baseHref;\n  }\n  onPopState(fn) {\n    const subscription = this.popStateSubject.subscribe(fn);\n    return () => subscription.unsubscribe();\n  }\n  onHashChange(fn) {\n    const subscription = this.hashUpdate.subscribe(fn);\n    return () => subscription.unsubscribe();\n  }\n  get href() {\n    let url = `${this.protocol}//${this.hostname}${this.port ? ':' + this.port : ''}`;\n    url += `${this.pathname === '/' ? '' : this.pathname}${this.search}${this.hash}`;\n    return url;\n  }\n  get url() {\n    return `${this.pathname}${this.search}${this.hash}`;\n  }\n  parseChanges(state, url, baseHref = '') {\n    // When the `history.state` value is stored, it is always copied.\n    state = JSON.parse(JSON.stringify(state));\n    return {\n      ...parseUrl(url, baseHref),\n      state\n    };\n  }\n  replaceState(state, title, newUrl) {\n    const {\n      pathname,\n      search,\n      state: parsedState,\n      hash\n    } = this.parseChanges(state, newUrl);\n    this.urlChanges[this.urlChangeIndex] = {\n      ...this.urlChanges[this.urlChangeIndex],\n      pathname,\n      search,\n      hash,\n      state: parsedState\n    };\n  }\n  pushState(state, title, newUrl) {\n    const {\n      pathname,\n      search,\n      state: parsedState,\n      hash\n    } = this.parseChanges(state, newUrl);\n    if (this.urlChangeIndex > 0) {\n      this.urlChanges.splice(this.urlChangeIndex + 1);\n    }\n    this.urlChanges.push({\n      ...this.urlChanges[this.urlChangeIndex],\n      pathname,\n      search,\n      hash,\n      state: parsedState\n    });\n    this.urlChangeIndex = this.urlChanges.length - 1;\n  }\n  forward() {\n    const oldUrl = this.url;\n    const oldHash = this.hash;\n    if (this.urlChangeIndex < this.urlChanges.length) {\n      this.urlChangeIndex++;\n    }\n    this.emitEvents(oldHash, oldUrl);\n  }\n  back() {\n    const oldUrl = this.url;\n    const oldHash = this.hash;\n    if (this.urlChangeIndex > 0) {\n      this.urlChangeIndex--;\n    }\n    this.emitEvents(oldHash, oldUrl);\n  }\n  historyGo(relativePosition = 0) {\n    const oldUrl = this.url;\n    const oldHash = this.hash;\n    const nextPageIndex = this.urlChangeIndex + relativePosition;\n    if (nextPageIndex >= 0 && nextPageIndex < this.urlChanges.length) {\n      this.urlChangeIndex = nextPageIndex;\n    }\n    this.emitEvents(oldHash, oldUrl);\n  }\n  getState() {\n    return this.state;\n  }\n  /**\n   * Browsers are inconsistent in when they fire events and perform the state updates\n   * The most easiest thing to do in our mock is synchronous and that happens to match\n   * Firefox and Chrome, at least somewhat closely\n   *\n   * https://github.com/WICG/navigation-api#watching-for-navigations\n   * https://docs.google.com/document/d/1Pdve-DJ1JCGilj9Yqf5HxRJyBKSel5owgOvUJqTauwU/edit#heading=h.3ye4v71wsz94\n   * popstate is always sent before hashchange:\n   * https://developer.mozilla.org/en-US/docs/Web/API/Window/popstate_event#when_popstate_is_sent\n   */\n  emitEvents(oldHash, oldUrl) {\n    this.popStateSubject.next({\n      type: 'popstate',\n      state: this.getState(),\n      oldUrl,\n      newUrl: this.url\n    });\n    if (oldHash !== this.hash) {\n      this.hashUpdate.next({\n        type: 'hashchange',\n        state: null,\n        oldUrl,\n        newUrl: this.url\n      });\n    }\n  }\n  static {\n    this.ɵfac = function MockPlatformLocation_Factory(t) {\n      return new (t || MockPlatformLocation)(i0.ɵɵinject(MOCK_PLATFORM_LOCATION_CONFIG, 8));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: MockPlatformLocation,\n      factory: MockPlatformLocation.ɵfac\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MockPlatformLocation, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [MOCK_PLATFORM_LOCATION_CONFIG]\n      }, {\n        type: Optional\n      }]\n    }];\n  }, null);\n})();\n\n/**\n * Returns mock providers for the `Location` and `LocationStrategy` classes.\n * The mocks are helpful in tests to fire simulated location events.\n *\n * @publicApi\n */\nfunction provideLocationMocks() {\n  return [{\n    provide: Location,\n    useClass: SpyLocation\n  }, {\n    provide: LocationStrategy,\n    useClass: MockLocationStrategy\n  }];\n}\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of the common/testing package.\n */\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of this package.\n */\n// This file only reexports content of the `src` folder. Keep it that way.\n\n// This file is not used to build this module. It is only used during editing\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MOCK_PLATFORM_LOCATION_CONFIG, MockLocationStrategy, MockPlatformLocation, SpyLocation, provideLocationMocks };", "map": {"version": 3, "names": ["i0", "EventEmitter", "Injectable", "InjectionToken", "Inject", "Optional", "LocationStrategy", "Location", "Subject", "joinWithSlash", "start", "end", "length", "slashes", "endsWith", "startsWith", "substring", "stripTrailingSlash", "url", "match", "pathEndIdx", "index", "droppedSlashIdx", "slice", "normalizeQueryParams", "params", "SpyLocation", "constructor", "url<PERSON><PERSON><PERSON>", "_history", "LocationState", "_historyIndex", "_subject", "_basePath", "_locationStrategy", "_urlChangeListeners", "_urlChangeSubscription", "ngOnDestroy", "unsubscribe", "setInitialPath", "path", "setBaseHref", "getState", "state", "isCurrentPathEqualTo", "query", "<PERSON><PERSON><PERSON>", "currPath", "simulateUrlPop", "pathname", "emit", "simulateHashChange", "prepareExternalUrl", "pushHistory", "push", "go", "locationState", "_notifyUrlChangeListeners", "replaceState", "history", "forward", "back", "historyGo", "relativePosition", "nextPageIndex", "onUrlChange", "fn", "subscribe", "v", "fnIndex", "indexOf", "splice", "for<PERSON>ach", "onNext", "onThrow", "onReturn", "next", "error", "complete", "normalize", "ɵfac", "SpyLocation_Factory", "t", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "ngDevMode", "ɵsetClassMetadata", "type", "MockLocationStrategy", "internalBaseHref", "internalPath", "internalTitle", "stateChanges", "simulatePopState", "_MockPopStateEvent", "includeHash", "internal", "pushState", "ctx", "title", "externalUrl", "onPopState", "getBaseHref", "pop", "nextUrl", "MockLocationStrategy_Factory", "newUrl", "urlParse", "parseUrl", "urlStr", "baseHref", "verifyProtocol", "serverBase", "test", "parsedUrl", "URL", "e", "result", "exec", "Error", "hostSplit", "split", "protocol", "hostname", "port", "search", "hash", "MOCK_PLATFORM_LOCATION_CONFIG", "MockPlatformLocation", "config", "hashUpdate", "popStateSubject", "urlChangeIndex", "appBaseHref", "parsedChanges", "parseChanges", "startUrl", "getBaseHrefFromDOM", "subscription", "onHashChange", "href", "JSON", "parse", "stringify", "parsedState", "oldUrl", "oldHash", "emitEvents", "MockPlatformLocation_Factory", "ɵɵinject", "undefined", "decorators", "args", "provideLocationMocks", "provide", "useClass"], "sources": ["C:/Users/<USER>/Desktop/angular/front/node_modules/@angular/common/fesm2022/testing.mjs"], "sourcesContent": ["/**\n * @license Angular v16.2.12\n * (c) 2010-2022 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport * as i0 from '@angular/core';\nimport { EventEmitter, Injectable, InjectionToken, Inject, Optional } from '@angular/core';\nimport { LocationStrategy, Location } from '@angular/common';\nimport { Subject } from 'rxjs';\n\n/**\n * Joins two parts of a URL with a slash if needed.\n *\n * @param start  URL string\n * @param end    URL string\n *\n *\n * @returns The joined URL string.\n */\nfunction joinWithSlash(start, end) {\n    if (start.length == 0) {\n        return end;\n    }\n    if (end.length == 0) {\n        return start;\n    }\n    let slashes = 0;\n    if (start.endsWith('/')) {\n        slashes++;\n    }\n    if (end.startsWith('/')) {\n        slashes++;\n    }\n    if (slashes == 2) {\n        return start + end.substring(1);\n    }\n    if (slashes == 1) {\n        return start + end;\n    }\n    return start + '/' + end;\n}\n/**\n * Removes a trailing slash from a URL string if needed.\n * Looks for the first occurrence of either `#`, `?`, or the end of the\n * line as `/` characters and removes the trailing slash if one exists.\n *\n * @param url URL string.\n *\n * @returns The URL string, modified if needed.\n */\nfunction stripTrailingSlash(url) {\n    const match = url.match(/#|\\?|$/);\n    const pathEndIdx = match && match.index || url.length;\n    const droppedSlashIdx = pathEndIdx - (url[pathEndIdx - 1] === '/' ? 1 : 0);\n    return url.slice(0, droppedSlashIdx) + url.slice(pathEndIdx);\n}\n/**\n * Normalizes URL parameters by prepending with `?` if needed.\n *\n * @param  params String of URL parameters.\n *\n * @returns The normalized URL parameters string.\n */\nfunction normalizeQueryParams(params) {\n    return params && params[0] !== '?' ? '?' + params : params;\n}\n\n/**\n * A spy for {@link Location} that allows tests to fire simulated location events.\n *\n * @publicApi\n */\nclass SpyLocation {\n    constructor() {\n        this.urlChanges = [];\n        this._history = [new LocationState('', '', null)];\n        this._historyIndex = 0;\n        /** @internal */\n        this._subject = new EventEmitter();\n        /** @internal */\n        this._basePath = '';\n        /** @internal */\n        this._locationStrategy = null;\n        /** @internal */\n        this._urlChangeListeners = [];\n        /** @internal */\n        this._urlChangeSubscription = null;\n    }\n    /** @nodoc */\n    ngOnDestroy() {\n        this._urlChangeSubscription?.unsubscribe();\n        this._urlChangeListeners = [];\n    }\n    setInitialPath(url) {\n        this._history[this._historyIndex].path = url;\n    }\n    setBaseHref(url) {\n        this._basePath = url;\n    }\n    path() {\n        return this._history[this._historyIndex].path;\n    }\n    getState() {\n        return this._history[this._historyIndex].state;\n    }\n    isCurrentPathEqualTo(path, query = '') {\n        const givenPath = path.endsWith('/') ? path.substring(0, path.length - 1) : path;\n        const currPath = this.path().endsWith('/') ? this.path().substring(0, this.path().length - 1) : this.path();\n        return currPath == givenPath + (query.length > 0 ? ('?' + query) : '');\n    }\n    simulateUrlPop(pathname) {\n        this._subject.emit({ 'url': pathname, 'pop': true, 'type': 'popstate' });\n    }\n    simulateHashChange(pathname) {\n        const path = this.prepareExternalUrl(pathname);\n        this.pushHistory(path, '', null);\n        this.urlChanges.push('hash: ' + pathname);\n        // the browser will automatically fire popstate event before each `hashchange` event, so we need\n        // to simulate it.\n        this._subject.emit({ 'url': pathname, 'pop': true, 'type': 'popstate' });\n        this._subject.emit({ 'url': pathname, 'pop': true, 'type': 'hashchange' });\n    }\n    prepareExternalUrl(url) {\n        if (url.length > 0 && !url.startsWith('/')) {\n            url = '/' + url;\n        }\n        return this._basePath + url;\n    }\n    go(path, query = '', state = null) {\n        path = this.prepareExternalUrl(path);\n        this.pushHistory(path, query, state);\n        const locationState = this._history[this._historyIndex - 1];\n        if (locationState.path == path && locationState.query == query) {\n            return;\n        }\n        const url = path + (query.length > 0 ? ('?' + query) : '');\n        this.urlChanges.push(url);\n        this._notifyUrlChangeListeners(path + normalizeQueryParams(query), state);\n    }\n    replaceState(path, query = '', state = null) {\n        path = this.prepareExternalUrl(path);\n        const history = this._history[this._historyIndex];\n        history.state = state;\n        if (history.path == path && history.query == query) {\n            return;\n        }\n        history.path = path;\n        history.query = query;\n        const url = path + (query.length > 0 ? ('?' + query) : '');\n        this.urlChanges.push('replace: ' + url);\n        this._notifyUrlChangeListeners(path + normalizeQueryParams(query), state);\n    }\n    forward() {\n        if (this._historyIndex < (this._history.length - 1)) {\n            this._historyIndex++;\n            this._subject.emit({ 'url': this.path(), 'state': this.getState(), 'pop': true, 'type': 'popstate' });\n        }\n    }\n    back() {\n        if (this._historyIndex > 0) {\n            this._historyIndex--;\n            this._subject.emit({ 'url': this.path(), 'state': this.getState(), 'pop': true, 'type': 'popstate' });\n        }\n    }\n    historyGo(relativePosition = 0) {\n        const nextPageIndex = this._historyIndex + relativePosition;\n        if (nextPageIndex >= 0 && nextPageIndex < this._history.length) {\n            this._historyIndex = nextPageIndex;\n            this._subject.emit({ 'url': this.path(), 'state': this.getState(), 'pop': true, 'type': 'popstate' });\n        }\n    }\n    onUrlChange(fn) {\n        this._urlChangeListeners.push(fn);\n        if (!this._urlChangeSubscription) {\n            this._urlChangeSubscription = this.subscribe(v => {\n                this._notifyUrlChangeListeners(v.url, v.state);\n            });\n        }\n        return () => {\n            const fnIndex = this._urlChangeListeners.indexOf(fn);\n            this._urlChangeListeners.splice(fnIndex, 1);\n            if (this._urlChangeListeners.length === 0) {\n                this._urlChangeSubscription?.unsubscribe();\n                this._urlChangeSubscription = null;\n            }\n        };\n    }\n    /** @internal */\n    _notifyUrlChangeListeners(url = '', state) {\n        this._urlChangeListeners.forEach(fn => fn(url, state));\n    }\n    subscribe(onNext, onThrow, onReturn) {\n        return this._subject.subscribe({ next: onNext, error: onThrow, complete: onReturn });\n    }\n    normalize(url) {\n        return null;\n    }\n    pushHistory(path, query, state) {\n        if (this._historyIndex > 0) {\n            this._history.splice(this._historyIndex + 1);\n        }\n        this._history.push(new LocationState(path, query, state));\n        this._historyIndex = this._history.length - 1;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: SpyLocation, deps: [], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: SpyLocation }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: SpyLocation, decorators: [{\n            type: Injectable\n        }] });\nclass LocationState {\n    constructor(path, query, state) {\n        this.path = path;\n        this.query = query;\n        this.state = state;\n    }\n}\n\n/**\n * A mock implementation of {@link LocationStrategy} that allows tests to fire simulated\n * location events.\n *\n * @publicApi\n */\nclass MockLocationStrategy extends LocationStrategy {\n    constructor() {\n        super();\n        this.internalBaseHref = '/';\n        this.internalPath = '/';\n        this.internalTitle = '';\n        this.urlChanges = [];\n        /** @internal */\n        this._subject = new EventEmitter();\n        this.stateChanges = [];\n    }\n    simulatePopState(url) {\n        this.internalPath = url;\n        this._subject.emit(new _MockPopStateEvent(this.path()));\n    }\n    path(includeHash = false) {\n        return this.internalPath;\n    }\n    prepareExternalUrl(internal) {\n        if (internal.startsWith('/') && this.internalBaseHref.endsWith('/')) {\n            return this.internalBaseHref + internal.substring(1);\n        }\n        return this.internalBaseHref + internal;\n    }\n    pushState(ctx, title, path, query) {\n        // Add state change to changes array\n        this.stateChanges.push(ctx);\n        this.internalTitle = title;\n        const url = path + (query.length > 0 ? ('?' + query) : '');\n        this.internalPath = url;\n        const externalUrl = this.prepareExternalUrl(url);\n        this.urlChanges.push(externalUrl);\n    }\n    replaceState(ctx, title, path, query) {\n        // Reset the last index of stateChanges to the ctx (state) object\n        this.stateChanges[(this.stateChanges.length || 1) - 1] = ctx;\n        this.internalTitle = title;\n        const url = path + (query.length > 0 ? ('?' + query) : '');\n        this.internalPath = url;\n        const externalUrl = this.prepareExternalUrl(url);\n        this.urlChanges.push('replace: ' + externalUrl);\n    }\n    onPopState(fn) {\n        this._subject.subscribe({ next: fn });\n    }\n    getBaseHref() {\n        return this.internalBaseHref;\n    }\n    back() {\n        if (this.urlChanges.length > 0) {\n            this.urlChanges.pop();\n            this.stateChanges.pop();\n            const nextUrl = this.urlChanges.length > 0 ? this.urlChanges[this.urlChanges.length - 1] : '';\n            this.simulatePopState(nextUrl);\n        }\n    }\n    forward() {\n        throw 'not implemented';\n    }\n    getState() {\n        return this.stateChanges[(this.stateChanges.length || 1) - 1];\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: MockLocationStrategy, deps: [], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: MockLocationStrategy }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: MockLocationStrategy, decorators: [{\n            type: Injectable\n        }], ctorParameters: function () { return []; } });\nclass _MockPopStateEvent {\n    constructor(newUrl) {\n        this.newUrl = newUrl;\n        this.pop = true;\n        this.type = 'popstate';\n    }\n}\n\n/**\n * Parser from https://tools.ietf.org/html/rfc3986#appendix-B\n * ^(([^:/?#]+):)?(//([^/?#]*))?([^?#]*)(\\?([^#]*))?(#(.*))?\n *  12            3  4          5       6  7        8 9\n *\n * Example: http://www.ics.uci.edu/pub/ietf/uri/#Related\n *\n * Results in:\n *\n * $1 = http:\n * $2 = http\n * $3 = //www.ics.uci.edu\n * $4 = www.ics.uci.edu\n * $5 = /pub/ietf/uri/\n * $6 = <undefined>\n * $7 = <undefined>\n * $8 = #Related\n * $9 = Related\n */\nconst urlParse = /^(([^:\\/?#]+):)?(\\/\\/([^\\/?#]*))?([^?#]*)(\\?([^#]*))?(#(.*))?/;\nfunction parseUrl(urlStr, baseHref) {\n    const verifyProtocol = /^((http[s]?|ftp):\\/\\/)/;\n    let serverBase;\n    // URL class requires full URL. If the URL string doesn't start with protocol, we need to add\n    // an arbitrary base URL which can be removed afterward.\n    if (!verifyProtocol.test(urlStr)) {\n        serverBase = 'http://empty.com/';\n    }\n    let parsedUrl;\n    try {\n        parsedUrl = new URL(urlStr, serverBase);\n    }\n    catch (e) {\n        const result = urlParse.exec(serverBase || '' + urlStr);\n        if (!result) {\n            throw new Error(`Invalid URL: ${urlStr} with base: ${baseHref}`);\n        }\n        const hostSplit = result[4].split(':');\n        parsedUrl = {\n            protocol: result[1],\n            hostname: hostSplit[0],\n            port: hostSplit[1] || '',\n            pathname: result[5],\n            search: result[6],\n            hash: result[8],\n        };\n    }\n    if (parsedUrl.pathname && parsedUrl.pathname.indexOf(baseHref) === 0) {\n        parsedUrl.pathname = parsedUrl.pathname.substring(baseHref.length);\n    }\n    return {\n        hostname: !serverBase && parsedUrl.hostname || '',\n        protocol: !serverBase && parsedUrl.protocol || '',\n        port: !serverBase && parsedUrl.port || '',\n        pathname: parsedUrl.pathname || '/',\n        search: parsedUrl.search || '',\n        hash: parsedUrl.hash || '',\n    };\n}\n/**\n * Provider for mock platform location config\n *\n * @publicApi\n */\nconst MOCK_PLATFORM_LOCATION_CONFIG = new InjectionToken('MOCK_PLATFORM_LOCATION_CONFIG');\n/**\n * Mock implementation of URL state.\n *\n * @publicApi\n */\nclass MockPlatformLocation {\n    constructor(config) {\n        this.baseHref = '';\n        this.hashUpdate = new Subject();\n        this.popStateSubject = new Subject();\n        this.urlChangeIndex = 0;\n        this.urlChanges = [{ hostname: '', protocol: '', port: '', pathname: '/', search: '', hash: '', state: null }];\n        if (config) {\n            this.baseHref = config.appBaseHref || '';\n            const parsedChanges = this.parseChanges(null, config.startUrl || 'http://_empty_/', this.baseHref);\n            this.urlChanges[0] = { ...parsedChanges };\n        }\n    }\n    get hostname() {\n        return this.urlChanges[this.urlChangeIndex].hostname;\n    }\n    get protocol() {\n        return this.urlChanges[this.urlChangeIndex].protocol;\n    }\n    get port() {\n        return this.urlChanges[this.urlChangeIndex].port;\n    }\n    get pathname() {\n        return this.urlChanges[this.urlChangeIndex].pathname;\n    }\n    get search() {\n        return this.urlChanges[this.urlChangeIndex].search;\n    }\n    get hash() {\n        return this.urlChanges[this.urlChangeIndex].hash;\n    }\n    get state() {\n        return this.urlChanges[this.urlChangeIndex].state;\n    }\n    getBaseHrefFromDOM() {\n        return this.baseHref;\n    }\n    onPopState(fn) {\n        const subscription = this.popStateSubject.subscribe(fn);\n        return () => subscription.unsubscribe();\n    }\n    onHashChange(fn) {\n        const subscription = this.hashUpdate.subscribe(fn);\n        return () => subscription.unsubscribe();\n    }\n    get href() {\n        let url = `${this.protocol}//${this.hostname}${this.port ? ':' + this.port : ''}`;\n        url += `${this.pathname === '/' ? '' : this.pathname}${this.search}${this.hash}`;\n        return url;\n    }\n    get url() {\n        return `${this.pathname}${this.search}${this.hash}`;\n    }\n    parseChanges(state, url, baseHref = '') {\n        // When the `history.state` value is stored, it is always copied.\n        state = JSON.parse(JSON.stringify(state));\n        return { ...parseUrl(url, baseHref), state };\n    }\n    replaceState(state, title, newUrl) {\n        const { pathname, search, state: parsedState, hash } = this.parseChanges(state, newUrl);\n        this.urlChanges[this.urlChangeIndex] =\n            { ...this.urlChanges[this.urlChangeIndex], pathname, search, hash, state: parsedState };\n    }\n    pushState(state, title, newUrl) {\n        const { pathname, search, state: parsedState, hash } = this.parseChanges(state, newUrl);\n        if (this.urlChangeIndex > 0) {\n            this.urlChanges.splice(this.urlChangeIndex + 1);\n        }\n        this.urlChanges.push({ ...this.urlChanges[this.urlChangeIndex], pathname, search, hash, state: parsedState });\n        this.urlChangeIndex = this.urlChanges.length - 1;\n    }\n    forward() {\n        const oldUrl = this.url;\n        const oldHash = this.hash;\n        if (this.urlChangeIndex < this.urlChanges.length) {\n            this.urlChangeIndex++;\n        }\n        this.emitEvents(oldHash, oldUrl);\n    }\n    back() {\n        const oldUrl = this.url;\n        const oldHash = this.hash;\n        if (this.urlChangeIndex > 0) {\n            this.urlChangeIndex--;\n        }\n        this.emitEvents(oldHash, oldUrl);\n    }\n    historyGo(relativePosition = 0) {\n        const oldUrl = this.url;\n        const oldHash = this.hash;\n        const nextPageIndex = this.urlChangeIndex + relativePosition;\n        if (nextPageIndex >= 0 && nextPageIndex < this.urlChanges.length) {\n            this.urlChangeIndex = nextPageIndex;\n        }\n        this.emitEvents(oldHash, oldUrl);\n    }\n    getState() {\n        return this.state;\n    }\n    /**\n     * Browsers are inconsistent in when they fire events and perform the state updates\n     * The most easiest thing to do in our mock is synchronous and that happens to match\n     * Firefox and Chrome, at least somewhat closely\n     *\n     * https://github.com/WICG/navigation-api#watching-for-navigations\n     * https://docs.google.com/document/d/1Pdve-DJ1JCGilj9Yqf5HxRJyBKSel5owgOvUJqTauwU/edit#heading=h.3ye4v71wsz94\n     * popstate is always sent before hashchange:\n     * https://developer.mozilla.org/en-US/docs/Web/API/Window/popstate_event#when_popstate_is_sent\n     */\n    emitEvents(oldHash, oldUrl) {\n        this.popStateSubject.next({ type: 'popstate', state: this.getState(), oldUrl, newUrl: this.url });\n        if (oldHash !== this.hash) {\n            this.hashUpdate.next({ type: 'hashchange', state: null, oldUrl, newUrl: this.url });\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: MockPlatformLocation, deps: [{ token: MOCK_PLATFORM_LOCATION_CONFIG, optional: true }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: MockPlatformLocation }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: MockPlatformLocation, decorators: [{\n            type: Injectable\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [MOCK_PLATFORM_LOCATION_CONFIG]\n                }, {\n                    type: Optional\n                }] }]; } });\n\n/**\n * Returns mock providers for the `Location` and `LocationStrategy` classes.\n * The mocks are helpful in tests to fire simulated location events.\n *\n * @publicApi\n */\nfunction provideLocationMocks() {\n    return [\n        { provide: Location, useClass: SpyLocation },\n        { provide: LocationStrategy, useClass: MockLocationStrategy },\n    ];\n}\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of the common/testing package.\n */\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of this package.\n */\n// This file only reexports content of the `src` folder. Keep it that way.\n\n// This file is not used to build this module. It is only used during editing\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MOCK_PLATFORM_LOCATION_CONFIG, MockLocationStrategy, MockPlatformLocation, SpyLocation, provideLocationMocks };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,YAAY,EAAEC,UAAU,EAAEC,cAAc,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,eAAe;AAC1F,SAASC,gBAAgB,EAAEC,QAAQ,QAAQ,iBAAiB;AAC5D,SAASC,OAAO,QAAQ,MAAM;;AAE9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,aAAaA,CAACC,KAAK,EAAEC,GAAG,EAAE;EAC/B,IAAID,KAAK,CAACE,MAAM,IAAI,CAAC,EAAE;IACnB,OAAOD,GAAG;EACd;EACA,IAAIA,GAAG,CAACC,MAAM,IAAI,CAAC,EAAE;IACjB,OAAOF,KAAK;EAChB;EACA,IAAIG,OAAO,GAAG,CAAC;EACf,IAAIH,KAAK,CAACI,QAAQ,CAAC,GAAG,CAAC,EAAE;IACrBD,OAAO,EAAE;EACb;EACA,IAAIF,GAAG,CAACI,UAAU,CAAC,GAAG,CAAC,EAAE;IACrBF,OAAO,EAAE;EACb;EACA,IAAIA,OAAO,IAAI,CAAC,EAAE;IACd,OAAOH,KAAK,GAAGC,GAAG,CAACK,SAAS,CAAC,CAAC,CAAC;EACnC;EACA,IAAIH,OAAO,IAAI,CAAC,EAAE;IACd,OAAOH,KAAK,GAAGC,GAAG;EACtB;EACA,OAAOD,KAAK,GAAG,GAAG,GAAGC,GAAG;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASM,kBAAkBA,CAACC,GAAG,EAAE;EAC7B,MAAMC,KAAK,GAAGD,GAAG,CAACC,KAAK,CAAC,QAAQ,CAAC;EACjC,MAAMC,UAAU,GAAGD,KAAK,IAAIA,KAAK,CAACE,KAAK,IAAIH,GAAG,CAACN,MAAM;EACrD,MAAMU,eAAe,GAAGF,UAAU,IAAIF,GAAG,CAACE,UAAU,GAAG,CAAC,CAAC,KAAK,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;EAC1E,OAAOF,GAAG,CAACK,KAAK,CAAC,CAAC,EAAED,eAAe,CAAC,GAAGJ,GAAG,CAACK,KAAK,CAACH,UAAU,CAAC;AAChE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASI,oBAAoBA,CAACC,MAAM,EAAE;EAClC,OAAOA,MAAM,IAAIA,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,GAAG,GAAG,GAAGA,MAAM,GAAGA,MAAM;AAC9D;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAMC,WAAW,CAAC;EACdC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,UAAU,GAAG,EAAE;IACpB,IAAI,CAACC,QAAQ,GAAG,CAAC,IAAIC,aAAa,CAAC,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,CAAC;IACjD,IAAI,CAACC,aAAa,GAAG,CAAC;IACtB;IACA,IAAI,CAACC,QAAQ,GAAG,IAAI/B,YAAY,CAAC,CAAC;IAClC;IACA,IAAI,CAACgC,SAAS,GAAG,EAAE;IACnB;IACA,IAAI,CAACC,iBAAiB,GAAG,IAAI;IAC7B;IACA,IAAI,CAACC,mBAAmB,GAAG,EAAE;IAC7B;IACA,IAAI,CAACC,sBAAsB,GAAG,IAAI;EACtC;EACA;EACAC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACD,sBAAsB,EAAEE,WAAW,CAAC,CAAC;IAC1C,IAAI,CAACH,mBAAmB,GAAG,EAAE;EACjC;EACAI,cAAcA,CAACrB,GAAG,EAAE;IAChB,IAAI,CAACW,QAAQ,CAAC,IAAI,CAACE,aAAa,CAAC,CAACS,IAAI,GAAGtB,GAAG;EAChD;EACAuB,WAAWA,CAACvB,GAAG,EAAE;IACb,IAAI,CAACe,SAAS,GAAGf,GAAG;EACxB;EACAsB,IAAIA,CAAA,EAAG;IACH,OAAO,IAAI,CAACX,QAAQ,CAAC,IAAI,CAACE,aAAa,CAAC,CAACS,IAAI;EACjD;EACAE,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACb,QAAQ,CAAC,IAAI,CAACE,aAAa,CAAC,CAACY,KAAK;EAClD;EACAC,oBAAoBA,CAACJ,IAAI,EAAEK,KAAK,GAAG,EAAE,EAAE;IACnC,MAAMC,SAAS,GAAGN,IAAI,CAAC1B,QAAQ,CAAC,GAAG,CAAC,GAAG0B,IAAI,CAACxB,SAAS,CAAC,CAAC,EAAEwB,IAAI,CAAC5B,MAAM,GAAG,CAAC,CAAC,GAAG4B,IAAI;IAChF,MAAMO,QAAQ,GAAG,IAAI,CAACP,IAAI,CAAC,CAAC,CAAC1B,QAAQ,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC0B,IAAI,CAAC,CAAC,CAACxB,SAAS,CAAC,CAAC,EAAE,IAAI,CAACwB,IAAI,CAAC,CAAC,CAAC5B,MAAM,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC4B,IAAI,CAAC,CAAC;IAC3G,OAAOO,QAAQ,IAAID,SAAS,IAAID,KAAK,CAACjC,MAAM,GAAG,CAAC,GAAI,GAAG,GAAGiC,KAAK,GAAI,EAAE,CAAC;EAC1E;EACAG,cAAcA,CAACC,QAAQ,EAAE;IACrB,IAAI,CAACjB,QAAQ,CAACkB,IAAI,CAAC;MAAE,KAAK,EAAED,QAAQ;MAAE,KAAK,EAAE,IAAI;MAAE,MAAM,EAAE;IAAW,CAAC,CAAC;EAC5E;EACAE,kBAAkBA,CAACF,QAAQ,EAAE;IACzB,MAAMT,IAAI,GAAG,IAAI,CAACY,kBAAkB,CAACH,QAAQ,CAAC;IAC9C,IAAI,CAACI,WAAW,CAACb,IAAI,EAAE,EAAE,EAAE,IAAI,CAAC;IAChC,IAAI,CAACZ,UAAU,CAAC0B,IAAI,CAAC,QAAQ,GAAGL,QAAQ,CAAC;IACzC;IACA;IACA,IAAI,CAACjB,QAAQ,CAACkB,IAAI,CAAC;MAAE,KAAK,EAAED,QAAQ;MAAE,KAAK,EAAE,IAAI;MAAE,MAAM,EAAE;IAAW,CAAC,CAAC;IACxE,IAAI,CAACjB,QAAQ,CAACkB,IAAI,CAAC;MAAE,KAAK,EAAED,QAAQ;MAAE,KAAK,EAAE,IAAI;MAAE,MAAM,EAAE;IAAa,CAAC,CAAC;EAC9E;EACAG,kBAAkBA,CAAClC,GAAG,EAAE;IACpB,IAAIA,GAAG,CAACN,MAAM,GAAG,CAAC,IAAI,CAACM,GAAG,CAACH,UAAU,CAAC,GAAG,CAAC,EAAE;MACxCG,GAAG,GAAG,GAAG,GAAGA,GAAG;IACnB;IACA,OAAO,IAAI,CAACe,SAAS,GAAGf,GAAG;EAC/B;EACAqC,EAAEA,CAACf,IAAI,EAAEK,KAAK,GAAG,EAAE,EAAEF,KAAK,GAAG,IAAI,EAAE;IAC/BH,IAAI,GAAG,IAAI,CAACY,kBAAkB,CAACZ,IAAI,CAAC;IACpC,IAAI,CAACa,WAAW,CAACb,IAAI,EAAEK,KAAK,EAAEF,KAAK,CAAC;IACpC,MAAMa,aAAa,GAAG,IAAI,CAAC3B,QAAQ,CAAC,IAAI,CAACE,aAAa,GAAG,CAAC,CAAC;IAC3D,IAAIyB,aAAa,CAAChB,IAAI,IAAIA,IAAI,IAAIgB,aAAa,CAACX,KAAK,IAAIA,KAAK,EAAE;MAC5D;IACJ;IACA,MAAM3B,GAAG,GAAGsB,IAAI,IAAIK,KAAK,CAACjC,MAAM,GAAG,CAAC,GAAI,GAAG,GAAGiC,KAAK,GAAI,EAAE,CAAC;IAC1D,IAAI,CAACjB,UAAU,CAAC0B,IAAI,CAACpC,GAAG,CAAC;IACzB,IAAI,CAACuC,yBAAyB,CAACjB,IAAI,GAAGhB,oBAAoB,CAACqB,KAAK,CAAC,EAAEF,KAAK,CAAC;EAC7E;EACAe,YAAYA,CAAClB,IAAI,EAAEK,KAAK,GAAG,EAAE,EAAEF,KAAK,GAAG,IAAI,EAAE;IACzCH,IAAI,GAAG,IAAI,CAACY,kBAAkB,CAACZ,IAAI,CAAC;IACpC,MAAMmB,OAAO,GAAG,IAAI,CAAC9B,QAAQ,CAAC,IAAI,CAACE,aAAa,CAAC;IACjD4B,OAAO,CAAChB,KAAK,GAAGA,KAAK;IACrB,IAAIgB,OAAO,CAACnB,IAAI,IAAIA,IAAI,IAAImB,OAAO,CAACd,KAAK,IAAIA,KAAK,EAAE;MAChD;IACJ;IACAc,OAAO,CAACnB,IAAI,GAAGA,IAAI;IACnBmB,OAAO,CAACd,KAAK,GAAGA,KAAK;IACrB,MAAM3B,GAAG,GAAGsB,IAAI,IAAIK,KAAK,CAACjC,MAAM,GAAG,CAAC,GAAI,GAAG,GAAGiC,KAAK,GAAI,EAAE,CAAC;IAC1D,IAAI,CAACjB,UAAU,CAAC0B,IAAI,CAAC,WAAW,GAAGpC,GAAG,CAAC;IACvC,IAAI,CAACuC,yBAAyB,CAACjB,IAAI,GAAGhB,oBAAoB,CAACqB,KAAK,CAAC,EAAEF,KAAK,CAAC;EAC7E;EACAiB,OAAOA,CAAA,EAAG;IACN,IAAI,IAAI,CAAC7B,aAAa,GAAI,IAAI,CAACF,QAAQ,CAACjB,MAAM,GAAG,CAAE,EAAE;MACjD,IAAI,CAACmB,aAAa,EAAE;MACpB,IAAI,CAACC,QAAQ,CAACkB,IAAI,CAAC;QAAE,KAAK,EAAE,IAAI,CAACV,IAAI,CAAC,CAAC;QAAE,OAAO,EAAE,IAAI,CAACE,QAAQ,CAAC,CAAC;QAAE,KAAK,EAAE,IAAI;QAAE,MAAM,EAAE;MAAW,CAAC,CAAC;IACzG;EACJ;EACAmB,IAAIA,CAAA,EAAG;IACH,IAAI,IAAI,CAAC9B,aAAa,GAAG,CAAC,EAAE;MACxB,IAAI,CAACA,aAAa,EAAE;MACpB,IAAI,CAACC,QAAQ,CAACkB,IAAI,CAAC;QAAE,KAAK,EAAE,IAAI,CAACV,IAAI,CAAC,CAAC;QAAE,OAAO,EAAE,IAAI,CAACE,QAAQ,CAAC,CAAC;QAAE,KAAK,EAAE,IAAI;QAAE,MAAM,EAAE;MAAW,CAAC,CAAC;IACzG;EACJ;EACAoB,SAASA,CAACC,gBAAgB,GAAG,CAAC,EAAE;IAC5B,MAAMC,aAAa,GAAG,IAAI,CAACjC,aAAa,GAAGgC,gBAAgB;IAC3D,IAAIC,aAAa,IAAI,CAAC,IAAIA,aAAa,GAAG,IAAI,CAACnC,QAAQ,CAACjB,MAAM,EAAE;MAC5D,IAAI,CAACmB,aAAa,GAAGiC,aAAa;MAClC,IAAI,CAAChC,QAAQ,CAACkB,IAAI,CAAC;QAAE,KAAK,EAAE,IAAI,CAACV,IAAI,CAAC,CAAC;QAAE,OAAO,EAAE,IAAI,CAACE,QAAQ,CAAC,CAAC;QAAE,KAAK,EAAE,IAAI;QAAE,MAAM,EAAE;MAAW,CAAC,CAAC;IACzG;EACJ;EACAuB,WAAWA,CAACC,EAAE,EAAE;IACZ,IAAI,CAAC/B,mBAAmB,CAACmB,IAAI,CAACY,EAAE,CAAC;IACjC,IAAI,CAAC,IAAI,CAAC9B,sBAAsB,EAAE;MAC9B,IAAI,CAACA,sBAAsB,GAAG,IAAI,CAAC+B,SAAS,CAACC,CAAC,IAAI;QAC9C,IAAI,CAACX,yBAAyB,CAACW,CAAC,CAAClD,GAAG,EAAEkD,CAAC,CAACzB,KAAK,CAAC;MAClD,CAAC,CAAC;IACN;IACA,OAAO,MAAM;MACT,MAAM0B,OAAO,GAAG,IAAI,CAAClC,mBAAmB,CAACmC,OAAO,CAACJ,EAAE,CAAC;MACpD,IAAI,CAAC/B,mBAAmB,CAACoC,MAAM,CAACF,OAAO,EAAE,CAAC,CAAC;MAC3C,IAAI,IAAI,CAAClC,mBAAmB,CAACvB,MAAM,KAAK,CAAC,EAAE;QACvC,IAAI,CAACwB,sBAAsB,EAAEE,WAAW,CAAC,CAAC;QAC1C,IAAI,CAACF,sBAAsB,GAAG,IAAI;MACtC;IACJ,CAAC;EACL;EACA;EACAqB,yBAAyBA,CAACvC,GAAG,GAAG,EAAE,EAAEyB,KAAK,EAAE;IACvC,IAAI,CAACR,mBAAmB,CAACqC,OAAO,CAACN,EAAE,IAAIA,EAAE,CAAChD,GAAG,EAAEyB,KAAK,CAAC,CAAC;EAC1D;EACAwB,SAASA,CAACM,MAAM,EAAEC,OAAO,EAAEC,QAAQ,EAAE;IACjC,OAAO,IAAI,CAAC3C,QAAQ,CAACmC,SAAS,CAAC;MAAES,IAAI,EAAEH,MAAM;MAAEI,KAAK,EAAEH,OAAO;MAAEI,QAAQ,EAAEH;IAAS,CAAC,CAAC;EACxF;EACAI,SAASA,CAAC7D,GAAG,EAAE;IACX,OAAO,IAAI;EACf;EACAmC,WAAWA,CAACb,IAAI,EAAEK,KAAK,EAAEF,KAAK,EAAE;IAC5B,IAAI,IAAI,CAACZ,aAAa,GAAG,CAAC,EAAE;MACxB,IAAI,CAACF,QAAQ,CAAC0C,MAAM,CAAC,IAAI,CAACxC,aAAa,GAAG,CAAC,CAAC;IAChD;IACA,IAAI,CAACF,QAAQ,CAACyB,IAAI,CAAC,IAAIxB,aAAa,CAACU,IAAI,EAAEK,KAAK,EAAEF,KAAK,CAAC,CAAC;IACzD,IAAI,CAACZ,aAAa,GAAG,IAAI,CAACF,QAAQ,CAACjB,MAAM,GAAG,CAAC;EACjD;EACA;IAAS,IAAI,CAACoE,IAAI,YAAAC,oBAAAC,CAAA;MAAA,YAAAA,CAAA,IAAyFxD,WAAW;IAAA,CAAoD;EAAE;EAC5K;IAAS,IAAI,CAACyD,KAAK,kBAD8EnF,EAAE,CAAAoF,kBAAA;MAAAC,KAAA,EACY3D,WAAW;MAAA4D,OAAA,EAAX5D,WAAW,CAAAsD;IAAA,EAAG;EAAE;AACnI;AACA;EAAA,QAAAO,SAAA,oBAAAA,SAAA,KAHqGvF,EAAE,CAAAwF,iBAAA,CAGX9D,WAAW,EAAc,CAAC;IAC1G+D,IAAI,EAAEvF;EACV,CAAC,CAAC;AAAA;AACV,MAAM4B,aAAa,CAAC;EAChBH,WAAWA,CAACa,IAAI,EAAEK,KAAK,EAAEF,KAAK,EAAE;IAC5B,IAAI,CAACH,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACK,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACF,KAAK,GAAGA,KAAK;EACtB;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM+C,oBAAoB,SAASpF,gBAAgB,CAAC;EAChDqB,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,CAAC;IACP,IAAI,CAACgE,gBAAgB,GAAG,GAAG;IAC3B,IAAI,CAACC,YAAY,GAAG,GAAG;IACvB,IAAI,CAACC,aAAa,GAAG,EAAE;IACvB,IAAI,CAACjE,UAAU,GAAG,EAAE;IACpB;IACA,IAAI,CAACI,QAAQ,GAAG,IAAI/B,YAAY,CAAC,CAAC;IAClC,IAAI,CAAC6F,YAAY,GAAG,EAAE;EAC1B;EACAC,gBAAgBA,CAAC7E,GAAG,EAAE;IAClB,IAAI,CAAC0E,YAAY,GAAG1E,GAAG;IACvB,IAAI,CAACc,QAAQ,CAACkB,IAAI,CAAC,IAAI8C,kBAAkB,CAAC,IAAI,CAACxD,IAAI,CAAC,CAAC,CAAC,CAAC;EAC3D;EACAA,IAAIA,CAACyD,WAAW,GAAG,KAAK,EAAE;IACtB,OAAO,IAAI,CAACL,YAAY;EAC5B;EACAxC,kBAAkBA,CAAC8C,QAAQ,EAAE;IACzB,IAAIA,QAAQ,CAACnF,UAAU,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC4E,gBAAgB,CAAC7E,QAAQ,CAAC,GAAG,CAAC,EAAE;MACjE,OAAO,IAAI,CAAC6E,gBAAgB,GAAGO,QAAQ,CAAClF,SAAS,CAAC,CAAC,CAAC;IACxD;IACA,OAAO,IAAI,CAAC2E,gBAAgB,GAAGO,QAAQ;EAC3C;EACAC,SAASA,CAACC,GAAG,EAAEC,KAAK,EAAE7D,IAAI,EAAEK,KAAK,EAAE;IAC/B;IACA,IAAI,CAACiD,YAAY,CAACxC,IAAI,CAAC8C,GAAG,CAAC;IAC3B,IAAI,CAACP,aAAa,GAAGQ,KAAK;IAC1B,MAAMnF,GAAG,GAAGsB,IAAI,IAAIK,KAAK,CAACjC,MAAM,GAAG,CAAC,GAAI,GAAG,GAAGiC,KAAK,GAAI,EAAE,CAAC;IAC1D,IAAI,CAAC+C,YAAY,GAAG1E,GAAG;IACvB,MAAMoF,WAAW,GAAG,IAAI,CAAClD,kBAAkB,CAAClC,GAAG,CAAC;IAChD,IAAI,CAACU,UAAU,CAAC0B,IAAI,CAACgD,WAAW,CAAC;EACrC;EACA5C,YAAYA,CAAC0C,GAAG,EAAEC,KAAK,EAAE7D,IAAI,EAAEK,KAAK,EAAE;IAClC;IACA,IAAI,CAACiD,YAAY,CAAC,CAAC,IAAI,CAACA,YAAY,CAAClF,MAAM,IAAI,CAAC,IAAI,CAAC,CAAC,GAAGwF,GAAG;IAC5D,IAAI,CAACP,aAAa,GAAGQ,KAAK;IAC1B,MAAMnF,GAAG,GAAGsB,IAAI,IAAIK,KAAK,CAACjC,MAAM,GAAG,CAAC,GAAI,GAAG,GAAGiC,KAAK,GAAI,EAAE,CAAC;IAC1D,IAAI,CAAC+C,YAAY,GAAG1E,GAAG;IACvB,MAAMoF,WAAW,GAAG,IAAI,CAAClD,kBAAkB,CAAClC,GAAG,CAAC;IAChD,IAAI,CAACU,UAAU,CAAC0B,IAAI,CAAC,WAAW,GAAGgD,WAAW,CAAC;EACnD;EACAC,UAAUA,CAACrC,EAAE,EAAE;IACX,IAAI,CAAClC,QAAQ,CAACmC,SAAS,CAAC;MAAES,IAAI,EAAEV;IAAG,CAAC,CAAC;EACzC;EACAsC,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAACb,gBAAgB;EAChC;EACA9B,IAAIA,CAAA,EAAG;IACH,IAAI,IAAI,CAACjC,UAAU,CAAChB,MAAM,GAAG,CAAC,EAAE;MAC5B,IAAI,CAACgB,UAAU,CAAC6E,GAAG,CAAC,CAAC;MACrB,IAAI,CAACX,YAAY,CAACW,GAAG,CAAC,CAAC;MACvB,MAAMC,OAAO,GAAG,IAAI,CAAC9E,UAAU,CAAChB,MAAM,GAAG,CAAC,GAAG,IAAI,CAACgB,UAAU,CAAC,IAAI,CAACA,UAAU,CAAChB,MAAM,GAAG,CAAC,CAAC,GAAG,EAAE;MAC7F,IAAI,CAACmF,gBAAgB,CAACW,OAAO,CAAC;IAClC;EACJ;EACA9C,OAAOA,CAAA,EAAG;IACN,MAAM,iBAAiB;EAC3B;EACAlB,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACoD,YAAY,CAAC,CAAC,IAAI,CAACA,YAAY,CAAClF,MAAM,IAAI,CAAC,IAAI,CAAC,CAAC;EACjE;EACA;IAAS,IAAI,CAACoE,IAAI,YAAA2B,6BAAAzB,CAAA;MAAA,YAAAA,CAAA,IAAyFQ,oBAAoB;IAAA,CAAoD;EAAE;EACrL;IAAS,IAAI,CAACP,KAAK,kBAnF8EnF,EAAE,CAAAoF,kBAAA;MAAAC,KAAA,EAmFYK,oBAAoB;MAAAJ,OAAA,EAApBI,oBAAoB,CAAAV;IAAA,EAAG;EAAE;AAC5I;AACA;EAAA,QAAAO,SAAA,oBAAAA,SAAA,KArFqGvF,EAAE,CAAAwF,iBAAA,CAqFXE,oBAAoB,EAAc,CAAC;IACnHD,IAAI,EAAEvF;EACV,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,EAAE;EAAE,CAAC;AAAA;AACtD,MAAM8F,kBAAkB,CAAC;EACrBrE,WAAWA,CAACiF,MAAM,EAAE;IAChB,IAAI,CAACA,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACH,GAAG,GAAG,IAAI;IACf,IAAI,CAAChB,IAAI,GAAG,UAAU;EAC1B;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMoB,QAAQ,GAAG,+DAA+D;AAChF,SAASC,QAAQA,CAACC,MAAM,EAAEC,QAAQ,EAAE;EAChC,MAAMC,cAAc,GAAG,wBAAwB;EAC/C,IAAIC,UAAU;EACd;EACA;EACA,IAAI,CAACD,cAAc,CAACE,IAAI,CAACJ,MAAM,CAAC,EAAE;IAC9BG,UAAU,GAAG,mBAAmB;EACpC;EACA,IAAIE,SAAS;EACb,IAAI;IACAA,SAAS,GAAG,IAAIC,GAAG,CAACN,MAAM,EAAEG,UAAU,CAAC;EAC3C,CAAC,CACD,OAAOI,CAAC,EAAE;IACN,MAAMC,MAAM,GAAGV,QAAQ,CAACW,IAAI,CAACN,UAAU,IAAI,EAAE,GAAGH,MAAM,CAAC;IACvD,IAAI,CAACQ,MAAM,EAAE;MACT,MAAM,IAAIE,KAAK,CAAE,gBAAeV,MAAO,eAAcC,QAAS,EAAC,CAAC;IACpE;IACA,MAAMU,SAAS,GAAGH,MAAM,CAAC,CAAC,CAAC,CAACI,KAAK,CAAC,GAAG,CAAC;IACtCP,SAAS,GAAG;MACRQ,QAAQ,EAAEL,MAAM,CAAC,CAAC,CAAC;MACnBM,QAAQ,EAAEH,SAAS,CAAC,CAAC,CAAC;MACtBI,IAAI,EAAEJ,SAAS,CAAC,CAAC,CAAC,IAAI,EAAE;MACxBzE,QAAQ,EAAEsE,MAAM,CAAC,CAAC,CAAC;MACnBQ,MAAM,EAAER,MAAM,CAAC,CAAC,CAAC;MACjBS,IAAI,EAAET,MAAM,CAAC,CAAC;IAClB,CAAC;EACL;EACA,IAAIH,SAAS,CAACnE,QAAQ,IAAImE,SAAS,CAACnE,QAAQ,CAACqB,OAAO,CAAC0C,QAAQ,CAAC,KAAK,CAAC,EAAE;IAClEI,SAAS,CAACnE,QAAQ,GAAGmE,SAAS,CAACnE,QAAQ,CAACjC,SAAS,CAACgG,QAAQ,CAACpG,MAAM,CAAC;EACtE;EACA,OAAO;IACHiH,QAAQ,EAAE,CAACX,UAAU,IAAIE,SAAS,CAACS,QAAQ,IAAI,EAAE;IACjDD,QAAQ,EAAE,CAACV,UAAU,IAAIE,SAAS,CAACQ,QAAQ,IAAI,EAAE;IACjDE,IAAI,EAAE,CAACZ,UAAU,IAAIE,SAAS,CAACU,IAAI,IAAI,EAAE;IACzC7E,QAAQ,EAAEmE,SAAS,CAACnE,QAAQ,IAAI,GAAG;IACnC8E,MAAM,EAAEX,SAAS,CAACW,MAAM,IAAI,EAAE;IAC9BC,IAAI,EAAEZ,SAAS,CAACY,IAAI,IAAI;EAC5B,CAAC;AACL;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,6BAA6B,GAAG,IAAI9H,cAAc,CAAC,+BAA+B,CAAC;AACzF;AACA;AACA;AACA;AACA;AACA,MAAM+H,oBAAoB,CAAC;EACvBvG,WAAWA,CAACwG,MAAM,EAAE;IAChB,IAAI,CAACnB,QAAQ,GAAG,EAAE;IAClB,IAAI,CAACoB,UAAU,GAAG,IAAI5H,OAAO,CAAC,CAAC;IAC/B,IAAI,CAAC6H,eAAe,GAAG,IAAI7H,OAAO,CAAC,CAAC;IACpC,IAAI,CAAC8H,cAAc,GAAG,CAAC;IACvB,IAAI,CAAC1G,UAAU,GAAG,CAAC;MAAEiG,QAAQ,EAAE,EAAE;MAAED,QAAQ,EAAE,EAAE;MAAEE,IAAI,EAAE,EAAE;MAAE7E,QAAQ,EAAE,GAAG;MAAE8E,MAAM,EAAE,EAAE;MAAEC,IAAI,EAAE,EAAE;MAAErF,KAAK,EAAE;IAAK,CAAC,CAAC;IAC9G,IAAIwF,MAAM,EAAE;MACR,IAAI,CAACnB,QAAQ,GAAGmB,MAAM,CAACI,WAAW,IAAI,EAAE;MACxC,MAAMC,aAAa,GAAG,IAAI,CAACC,YAAY,CAAC,IAAI,EAAEN,MAAM,CAACO,QAAQ,IAAI,iBAAiB,EAAE,IAAI,CAAC1B,QAAQ,CAAC;MAClG,IAAI,CAACpF,UAAU,CAAC,CAAC,CAAC,GAAG;QAAE,GAAG4G;MAAc,CAAC;IAC7C;EACJ;EACA,IAAIX,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACjG,UAAU,CAAC,IAAI,CAAC0G,cAAc,CAAC,CAACT,QAAQ;EACxD;EACA,IAAID,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAAChG,UAAU,CAAC,IAAI,CAAC0G,cAAc,CAAC,CAACV,QAAQ;EACxD;EACA,IAAIE,IAAIA,CAAA,EAAG;IACP,OAAO,IAAI,CAAClG,UAAU,CAAC,IAAI,CAAC0G,cAAc,CAAC,CAACR,IAAI;EACpD;EACA,IAAI7E,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACrB,UAAU,CAAC,IAAI,CAAC0G,cAAc,CAAC,CAACrF,QAAQ;EACxD;EACA,IAAI8E,MAAMA,CAAA,EAAG;IACT,OAAO,IAAI,CAACnG,UAAU,CAAC,IAAI,CAAC0G,cAAc,CAAC,CAACP,MAAM;EACtD;EACA,IAAIC,IAAIA,CAAA,EAAG;IACP,OAAO,IAAI,CAACpG,UAAU,CAAC,IAAI,CAAC0G,cAAc,CAAC,CAACN,IAAI;EACpD;EACA,IAAIrF,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAACf,UAAU,CAAC,IAAI,CAAC0G,cAAc,CAAC,CAAC3F,KAAK;EACrD;EACAgG,kBAAkBA,CAAA,EAAG;IACjB,OAAO,IAAI,CAAC3B,QAAQ;EACxB;EACAT,UAAUA,CAACrC,EAAE,EAAE;IACX,MAAM0E,YAAY,GAAG,IAAI,CAACP,eAAe,CAAClE,SAAS,CAACD,EAAE,CAAC;IACvD,OAAO,MAAM0E,YAAY,CAACtG,WAAW,CAAC,CAAC;EAC3C;EACAuG,YAAYA,CAAC3E,EAAE,EAAE;IACb,MAAM0E,YAAY,GAAG,IAAI,CAACR,UAAU,CAACjE,SAAS,CAACD,EAAE,CAAC;IAClD,OAAO,MAAM0E,YAAY,CAACtG,WAAW,CAAC,CAAC;EAC3C;EACA,IAAIwG,IAAIA,CAAA,EAAG;IACP,IAAI5H,GAAG,GAAI,GAAE,IAAI,CAAC0G,QAAS,KAAI,IAAI,CAACC,QAAS,GAAE,IAAI,CAACC,IAAI,GAAG,GAAG,GAAG,IAAI,CAACA,IAAI,GAAG,EAAG,EAAC;IACjF5G,GAAG,IAAK,GAAE,IAAI,CAAC+B,QAAQ,KAAK,GAAG,GAAG,EAAE,GAAG,IAAI,CAACA,QAAS,GAAE,IAAI,CAAC8E,MAAO,GAAE,IAAI,CAACC,IAAK,EAAC;IAChF,OAAO9G,GAAG;EACd;EACA,IAAIA,GAAGA,CAAA,EAAG;IACN,OAAQ,GAAE,IAAI,CAAC+B,QAAS,GAAE,IAAI,CAAC8E,MAAO,GAAE,IAAI,CAACC,IAAK,EAAC;EACvD;EACAS,YAAYA,CAAC9F,KAAK,EAAEzB,GAAG,EAAE8F,QAAQ,GAAG,EAAE,EAAE;IACpC;IACArE,KAAK,GAAGoG,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,SAAS,CAACtG,KAAK,CAAC,CAAC;IACzC,OAAO;MAAE,GAAGmE,QAAQ,CAAC5F,GAAG,EAAE8F,QAAQ,CAAC;MAAErE;IAAM,CAAC;EAChD;EACAe,YAAYA,CAACf,KAAK,EAAE0D,KAAK,EAAEO,MAAM,EAAE;IAC/B,MAAM;MAAE3D,QAAQ;MAAE8E,MAAM;MAAEpF,KAAK,EAAEuG,WAAW;MAAElB;IAAK,CAAC,GAAG,IAAI,CAACS,YAAY,CAAC9F,KAAK,EAAEiE,MAAM,CAAC;IACvF,IAAI,CAAChF,UAAU,CAAC,IAAI,CAAC0G,cAAc,CAAC,GAChC;MAAE,GAAG,IAAI,CAAC1G,UAAU,CAAC,IAAI,CAAC0G,cAAc,CAAC;MAAErF,QAAQ;MAAE8E,MAAM;MAAEC,IAAI;MAAErF,KAAK,EAAEuG;IAAY,CAAC;EAC/F;EACA/C,SAASA,CAACxD,KAAK,EAAE0D,KAAK,EAAEO,MAAM,EAAE;IAC5B,MAAM;MAAE3D,QAAQ;MAAE8E,MAAM;MAAEpF,KAAK,EAAEuG,WAAW;MAAElB;IAAK,CAAC,GAAG,IAAI,CAACS,YAAY,CAAC9F,KAAK,EAAEiE,MAAM,CAAC;IACvF,IAAI,IAAI,CAAC0B,cAAc,GAAG,CAAC,EAAE;MACzB,IAAI,CAAC1G,UAAU,CAAC2C,MAAM,CAAC,IAAI,CAAC+D,cAAc,GAAG,CAAC,CAAC;IACnD;IACA,IAAI,CAAC1G,UAAU,CAAC0B,IAAI,CAAC;MAAE,GAAG,IAAI,CAAC1B,UAAU,CAAC,IAAI,CAAC0G,cAAc,CAAC;MAAErF,QAAQ;MAAE8E,MAAM;MAAEC,IAAI;MAAErF,KAAK,EAAEuG;IAAY,CAAC,CAAC;IAC7G,IAAI,CAACZ,cAAc,GAAG,IAAI,CAAC1G,UAAU,CAAChB,MAAM,GAAG,CAAC;EACpD;EACAgD,OAAOA,CAAA,EAAG;IACN,MAAMuF,MAAM,GAAG,IAAI,CAACjI,GAAG;IACvB,MAAMkI,OAAO,GAAG,IAAI,CAACpB,IAAI;IACzB,IAAI,IAAI,CAACM,cAAc,GAAG,IAAI,CAAC1G,UAAU,CAAChB,MAAM,EAAE;MAC9C,IAAI,CAAC0H,cAAc,EAAE;IACzB;IACA,IAAI,CAACe,UAAU,CAACD,OAAO,EAAED,MAAM,CAAC;EACpC;EACAtF,IAAIA,CAAA,EAAG;IACH,MAAMsF,MAAM,GAAG,IAAI,CAACjI,GAAG;IACvB,MAAMkI,OAAO,GAAG,IAAI,CAACpB,IAAI;IACzB,IAAI,IAAI,CAACM,cAAc,GAAG,CAAC,EAAE;MACzB,IAAI,CAACA,cAAc,EAAE;IACzB;IACA,IAAI,CAACe,UAAU,CAACD,OAAO,EAAED,MAAM,CAAC;EACpC;EACArF,SAASA,CAACC,gBAAgB,GAAG,CAAC,EAAE;IAC5B,MAAMoF,MAAM,GAAG,IAAI,CAACjI,GAAG;IACvB,MAAMkI,OAAO,GAAG,IAAI,CAACpB,IAAI;IACzB,MAAMhE,aAAa,GAAG,IAAI,CAACsE,cAAc,GAAGvE,gBAAgB;IAC5D,IAAIC,aAAa,IAAI,CAAC,IAAIA,aAAa,GAAG,IAAI,CAACpC,UAAU,CAAChB,MAAM,EAAE;MAC9D,IAAI,CAAC0H,cAAc,GAAGtE,aAAa;IACvC;IACA,IAAI,CAACqF,UAAU,CAACD,OAAO,EAAED,MAAM,CAAC;EACpC;EACAzG,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACC,KAAK;EACrB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI0G,UAAUA,CAACD,OAAO,EAAED,MAAM,EAAE;IACxB,IAAI,CAACd,eAAe,CAACzD,IAAI,CAAC;MAAEa,IAAI,EAAE,UAAU;MAAE9C,KAAK,EAAE,IAAI,CAACD,QAAQ,CAAC,CAAC;MAAEyG,MAAM;MAAEvC,MAAM,EAAE,IAAI,CAAC1F;IAAI,CAAC,CAAC;IACjG,IAAIkI,OAAO,KAAK,IAAI,CAACpB,IAAI,EAAE;MACvB,IAAI,CAACI,UAAU,CAACxD,IAAI,CAAC;QAAEa,IAAI,EAAE,YAAY;QAAE9C,KAAK,EAAE,IAAI;QAAEwG,MAAM;QAAEvC,MAAM,EAAE,IAAI,CAAC1F;MAAI,CAAC,CAAC;IACvF;EACJ;EACA;IAAS,IAAI,CAAC8D,IAAI,YAAAsE,6BAAApE,CAAA;MAAA,YAAAA,CAAA,IAAyFgD,oBAAoB,EAzR9BlI,EAAE,CAAAuJ,QAAA,CAyR8CtB,6BAA6B;IAAA,CAA6D;EAAE;EAC7O;IAAS,IAAI,CAAC9C,KAAK,kBA1R8EnF,EAAE,CAAAoF,kBAAA;MAAAC,KAAA,EA0RY6C,oBAAoB;MAAA5C,OAAA,EAApB4C,oBAAoB,CAAAlD;IAAA,EAAG;EAAE;AAC5I;AACA;EAAA,QAAAO,SAAA,oBAAAA,SAAA,KA5RqGvF,EAAE,CAAAwF,iBAAA,CA4RX0C,oBAAoB,EAAc,CAAC;IACnHzC,IAAI,EAAEvF;EACV,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEuF,IAAI,EAAE+D,SAAS;MAAEC,UAAU,EAAE,CAAC;QAC9DhE,IAAI,EAAErF,MAAM;QACZsJ,IAAI,EAAE,CAACzB,6BAA6B;MACxC,CAAC,EAAE;QACCxC,IAAI,EAAEpF;MACV,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC;AAAA;;AAExB;AACA;AACA;AACA;AACA;AACA;AACA,SAASsJ,oBAAoBA,CAAA,EAAG;EAC5B,OAAO,CACH;IAAEC,OAAO,EAAErJ,QAAQ;IAAEsJ,QAAQ,EAAEnI;EAAY,CAAC,EAC5C;IAAEkI,OAAO,EAAEtJ,gBAAgB;IAAEuJ,QAAQ,EAAEnE;EAAqB,CAAC,CAChE;AACL;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA,SAASuC,6BAA6B,EAAEvC,oBAAoB,EAAEwC,oBAAoB,EAAExG,WAAW,EAAEiI,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}