{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/auth.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/common\";\nfunction DashboardComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 11)(1, \"span\", 12);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 13);\n    i0.ɵɵlistener(\"click\", function DashboardComponent_div_5_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.logout());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(4, \"svg\", 14);\n    i0.ɵɵelement(5, \"path\", 15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(6, \" D\\u00E9connexion \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"Bienvenue, \", ctx_r0.userInfo.name, \"\");\n  }\n}\nfunction DashboardComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 16)(1, \"h2\");\n    i0.ɵɵtext(2, \"Informations utilisateur\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 17)(4, \"div\", 18)(5, \"label\");\n    i0.ɵɵtext(6, \"Code utilisateur :\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 18)(10, \"label\");\n    i0.ɵɵtext(11, \"Nom :\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"span\");\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 18)(15, \"label\");\n    i0.ɵɵtext(16, \"Agence :\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"span\");\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"div\", 18)(20, \"label\");\n    i0.ɵɵtext(21, \"Bureau :\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"span\");\n    i0.ɵɵtext(23);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(24, \"div\", 18)(25, \"label\");\n    i0.ɵɵtext(26, \"Op\\u00E9rateur :\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"span\");\n    i0.ɵɵtext(28);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(29, \"div\", 18)(30, \"label\");\n    i0.ɵɵtext(31, \"March\\u00E9 :\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"span\");\n    i0.ɵɵtext(33);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(ctx_r1.userInfo.code);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r1.userInfo.name);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r1.userInfo.agency == null ? null : ctx_r1.userInfo.agency.name, \" (\", ctx_r1.userInfo.agency == null ? null : ctx_r1.userInfo.agency.code, \")\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r1.userInfo.office == null ? null : ctx_r1.userInfo.office.name, \" (\", ctx_r1.userInfo.office == null ? null : ctx_r1.userInfo.office.code, \")\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r1.userInfo.operator == null ? null : ctx_r1.userInfo.operator.name, \" (\", ctx_r1.userInfo.operator == null ? null : ctx_r1.userInfo.operator.code, \")\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r1.userInfo.market == null ? null : ctx_r1.userInfo.market.name, \" (\", ctx_r1.userInfo.market == null ? null : ctx_r1.userInfo.market.code, \")\");\n  }\n}\nexport class DashboardComponent {\n  constructor(authService, router) {\n    this.authService = authService;\n    this.router = router;\n  }\n  ngOnInit() {\n    this.userInfo = this.authService.getUserInfo();\n  }\n  logout() {\n    this.authService.logout();\n    this.router.navigate(['/login']);\n  }\n  static {\n    this.ɵfac = function DashboardComponent_Factory(t) {\n      return new (t || DashboardComponent)(i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i2.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: DashboardComponent,\n      selectors: [[\"app-dashboard\"]],\n      decls: 16,\n      vars: 2,\n      consts: [[1, \"dashboard-container\"], [1, \"dashboard-header\"], [1, \"header-content\"], [1, \"dashboard-title\"], [\"class\", \"user-info\", 4, \"ngIf\"], [1, \"dashboard-main\"], [1, \"dashboard-content\"], [\"class\", \"info-card\", 4, \"ngIf\"], [1, \"success-message\"], [\"fill\", \"currentColor\", \"viewBox\", \"0 0 20 20\", 1, \"success-icon\"], [\"fill-rule\", \"evenodd\", \"d\", \"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\", \"clip-rule\", \"evenodd\"], [1, \"user-info\"], [1, \"welcome-text\"], [1, \"logout-button\", 3, \"click\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"logout-icon\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1\"], [1, \"info-card\"], [1, \"info-grid\"], [1, \"info-item\"]],\n      template: function DashboardComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"header\", 1)(2, \"div\", 2)(3, \"h1\", 3);\n          i0.ɵɵtext(4, \"Tableau de bord Paximum\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(5, DashboardComponent_div_5_Template, 7, 1, \"div\", 4);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(6, \"main\", 5)(7, \"div\", 6);\n          i0.ɵɵtemplate(8, DashboardComponent_div_8_Template, 34, 10, \"div\", 7);\n          i0.ɵɵelementStart(9, \"div\", 8);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(10, \"svg\", 9);\n          i0.ɵɵelement(11, \"path\", 10);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(12, \"h3\");\n          i0.ɵɵtext(13, \"Connexion r\\u00E9ussie !\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"p\");\n          i0.ɵɵtext(15, \"Vous \\u00EAtes maintenant connect\\u00E9 \\u00E0 votre compte Paximum.\");\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngIf\", ctx.userInfo);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.userInfo);\n        }\n      },\n      dependencies: [i3.NgIf],\n      styles: [\".dashboard-container[_ngcontent-%COMP%] {\\n  min-height: 100vh;\\n  background-color: #f8fafc;\\n}\\n\\n.dashboard-header[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  color: white;\\n  padding: 1.5rem 0;\\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);\\n}\\n\\n.header-content[_ngcontent-%COMP%] {\\n  max-width: 1200px;\\n  margin: 0 auto;\\n  padding: 0 2rem;\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n}\\n\\n.dashboard-title[_ngcontent-%COMP%] {\\n  font-size: 1.875rem;\\n  font-weight: 700;\\n  margin: 0;\\n}\\n\\n.user-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 1rem;\\n}\\n\\n.welcome-text[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n}\\n\\n.logout-button[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.2);\\n  color: white;\\n  border: 1px solid rgba(255, 255, 255, 0.3);\\n  padding: 0.5rem 1rem;\\n  border-radius: 0.375rem;\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  transition: all 0.2s ease;\\n  font-weight: 500;\\n}\\n\\n.logout-button[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.3);\\n  border-color: rgba(255, 255, 255, 0.5);\\n}\\n\\n.logout-icon[_ngcontent-%COMP%] {\\n  width: 1rem;\\n  height: 1rem;\\n}\\n\\n.dashboard-main[_ngcontent-%COMP%] {\\n  padding: 2rem 0;\\n}\\n\\n.dashboard-content[_ngcontent-%COMP%] {\\n  max-width: 1200px;\\n  margin: 0 auto;\\n  padding: 0 2rem;\\n  display: grid;\\n  gap: 2rem;\\n}\\n\\n.info-card[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 0.75rem;\\n  padding: 2rem;\\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);\\n}\\n\\n.info-card[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  margin: 0 0 1.5rem 0;\\n  color: #1f2937;\\n  font-size: 1.5rem;\\n  font-weight: 600;\\n}\\n\\n.info-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\\n  gap: 1rem;\\n}\\n\\n.info-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 0.25rem;\\n}\\n\\n.info-item[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #6b7280;\\n  font-size: 0.875rem;\\n}\\n\\n.info-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  color: #1f2937;\\n  font-weight: 500;\\n}\\n\\n.success-message[_ngcontent-%COMP%] {\\n  background: #f0fdf4;\\n  border: 1px solid #bbf7d0;\\n  color: #059669;\\n  padding: 2rem;\\n  border-radius: 0.75rem;\\n  text-align: center;\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  gap: 1rem;\\n}\\n\\n.success-icon[_ngcontent-%COMP%] {\\n  width: 3rem;\\n  height: 3rem;\\n}\\n\\n.success-message[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 1.5rem;\\n  font-weight: 600;\\n}\\n\\n.success-message[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 1rem;\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .header-content[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 1rem;\\n    text-align: center;\\n  }\\n  \\n  .dashboard-content[_ngcontent-%COMP%] {\\n    padding: 0 1rem;\\n  }\\n  \\n  .info-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  \\n  .info-card[_ngcontent-%COMP%] {\\n    padding: 1.5rem;\\n  }\\n}\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "DashboardComponent_div_5_Template_button_click_3_listener", "ɵɵrestoreView", "_r3", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "logout", "ɵɵnamespaceSVG", "ɵɵelement", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r0", "userInfo", "name", "ɵɵtextInterpolate", "ctx_r1", "code", "ɵɵtextInterpolate2", "agency", "office", "operator", "market", "DashboardComponent", "constructor", "authService", "router", "ngOnInit", "getUserInfo", "navigate", "ɵɵdirectiveInject", "i1", "AuthService", "i2", "Router", "selectors", "decls", "vars", "consts", "template", "DashboardComponent_Template", "rf", "ctx", "ɵɵtemplate", "DashboardComponent_div_5_Template", "DashboardComponent_div_8_Template", "ɵɵnamespaceHTML", "ɵɵproperty"], "sources": ["C:\\Users\\<USER>\\Desktop\\angular\\front\\src\\app\\components\\dashboard\\dashboard.component.ts", "C:\\Users\\<USER>\\Desktop\\angular\\front\\src\\app\\components\\dashboard\\dashboard.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { AuthService } from '../../services/auth.service';\n\n@Component({\n  selector: 'app-dashboard',\n  templateUrl: './dashboard.component.html',\n  styleUrls: ['./dashboard.component.css']\n})\nexport class DashboardComponent implements OnInit {\n  userInfo: any;\n\n  constructor(\n    private authService: AuthService,\n    private router: Router\n  ) {}\n\n  ngOnInit(): void {\n    this.userInfo = this.authService.getUserInfo();\n  }\n\n  logout(): void {\n    this.authService.logout();\n    this.router.navigate(['/login']);\n  }\n}\n", "<div class=\"dashboard-container\">\n  <header class=\"dashboard-header\">\n    <div class=\"header-content\">\n      <h1 class=\"dashboard-title\">Tableau de bord Paximum</h1>\n      <div class=\"user-info\" *ngIf=\"userInfo\">\n        <span class=\"welcome-text\">Bienvenue, {{ userInfo.name }}</span>\n        <button class=\"logout-button\" (click)=\"logout()\">\n          <svg class=\"logout-icon\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1\"></path>\n          </svg>\n          Déconnexion\n        </button>\n      </div>\n    </div>\n  </header>\n\n  <main class=\"dashboard-main\">\n    <div class=\"dashboard-content\">\n      <div class=\"info-card\" *ngIf=\"userInfo\">\n        <h2>Informations utilisateur</h2>\n        <div class=\"info-grid\">\n          <div class=\"info-item\">\n            <label>Code utilisateur :</label>\n            <span>{{ userInfo.code }}</span>\n          </div>\n          <div class=\"info-item\">\n            <label>Nom :</label>\n            <span>{{ userInfo.name }}</span>\n          </div>\n          <div class=\"info-item\">\n            <label>Agence :</label>\n            <span>{{ userInfo.agency?.name }} ({{ userInfo.agency?.code }})</span>\n          </div>\n          <div class=\"info-item\">\n            <label>Bureau :</label>\n            <span>{{ userInfo.office?.name }} ({{ userInfo.office?.code }})</span>\n          </div>\n          <div class=\"info-item\">\n            <label>Opérateur :</label>\n            <span>{{ userInfo.operator?.name }} ({{ userInfo.operator?.code }})</span>\n          </div>\n          <div class=\"info-item\">\n            <label>Marché :</label>\n            <span>{{ userInfo.market?.name }} ({{ userInfo.market?.code }})</span>\n          </div>\n        </div>\n      </div>\n\n      <div class=\"success-message\">\n        <svg class=\"success-icon\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n          <path fill-rule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\" clip-rule=\"evenodd\"></path>\n        </svg>\n        <h3>Connexion réussie !</h3>\n        <p>Vous êtes maintenant connecté à votre compte Paximum.</p>\n      </div>\n    </div>\n  </main>\n</div>\n"], "mappings": ";;;;;;;ICIMA,EAAA,CAAAC,cAAA,cAAwC;IACXD,EAAA,CAAAE,MAAA,GAA8B;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAChEH,EAAA,CAAAC,cAAA,iBAAiD;IAAnBD,EAAA,CAAAI,UAAA,mBAAAC,0DAAA;MAAAL,EAAA,CAAAM,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAST,EAAA,CAAAU,WAAA,CAAAF,MAAA,CAAAG,MAAA,EAAQ;IAAA,EAAC;IAC9CX,EAAA,CAAAY,cAAA,EAA+E;IAA/EZ,EAAA,CAAAC,cAAA,cAA+E;IAC7ED,EAAA,CAAAa,SAAA,eAA2K;IAC7Kb,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAE,MAAA,yBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IANkBH,EAAA,CAAAc,SAAA,GAA8B;IAA9Bd,EAAA,CAAAe,kBAAA,gBAAAC,MAAA,CAAAC,QAAA,CAAAC,IAAA,KAA8B;;;;;IAa3DlB,EAAA,CAAAC,cAAA,cAAwC;IAClCD,EAAA,CAAAE,MAAA,+BAAwB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACjCH,EAAA,CAAAC,cAAA,cAAuB;IAEZD,EAAA,CAAAE,MAAA,yBAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACjCH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAElCH,EAAA,CAAAC,cAAA,cAAuB;IACdD,EAAA,CAAAE,MAAA,aAAK;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACpBH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,IAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAElCH,EAAA,CAAAC,cAAA,eAAuB;IACdD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACvBH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,IAAyD;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAExEH,EAAA,CAAAC,cAAA,eAAuB;IACdD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACvBH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,IAAyD;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAExEH,EAAA,CAAAC,cAAA,eAAuB;IACdD,EAAA,CAAAE,MAAA,wBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC1BH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,IAA6D;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAE5EH,EAAA,CAAAC,cAAA,eAAuB;IACdD,EAAA,CAAAE,MAAA,qBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACvBH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,IAAyD;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IApBhEH,EAAA,CAAAc,SAAA,GAAmB;IAAnBd,EAAA,CAAAmB,iBAAA,CAAAC,MAAA,CAAAH,QAAA,CAAAI,IAAA,CAAmB;IAInBrB,EAAA,CAAAc,SAAA,GAAmB;IAAnBd,EAAA,CAAAmB,iBAAA,CAAAC,MAAA,CAAAH,QAAA,CAAAC,IAAA,CAAmB;IAInBlB,EAAA,CAAAc,SAAA,GAAyD;IAAzDd,EAAA,CAAAsB,kBAAA,KAAAF,MAAA,CAAAH,QAAA,CAAAM,MAAA,kBAAAH,MAAA,CAAAH,QAAA,CAAAM,MAAA,CAAAL,IAAA,QAAAE,MAAA,CAAAH,QAAA,CAAAM,MAAA,kBAAAH,MAAA,CAAAH,QAAA,CAAAM,MAAA,CAAAF,IAAA,MAAyD;IAIzDrB,EAAA,CAAAc,SAAA,GAAyD;IAAzDd,EAAA,CAAAsB,kBAAA,KAAAF,MAAA,CAAAH,QAAA,CAAAO,MAAA,kBAAAJ,MAAA,CAAAH,QAAA,CAAAO,MAAA,CAAAN,IAAA,QAAAE,MAAA,CAAAH,QAAA,CAAAO,MAAA,kBAAAJ,MAAA,CAAAH,QAAA,CAAAO,MAAA,CAAAH,IAAA,MAAyD;IAIzDrB,EAAA,CAAAc,SAAA,GAA6D;IAA7Dd,EAAA,CAAAsB,kBAAA,KAAAF,MAAA,CAAAH,QAAA,CAAAQ,QAAA,kBAAAL,MAAA,CAAAH,QAAA,CAAAQ,QAAA,CAAAP,IAAA,QAAAE,MAAA,CAAAH,QAAA,CAAAQ,QAAA,kBAAAL,MAAA,CAAAH,QAAA,CAAAQ,QAAA,CAAAJ,IAAA,MAA6D;IAI7DrB,EAAA,CAAAc,SAAA,GAAyD;IAAzDd,EAAA,CAAAsB,kBAAA,KAAAF,MAAA,CAAAH,QAAA,CAAAS,MAAA,kBAAAN,MAAA,CAAAH,QAAA,CAAAS,MAAA,CAAAR,IAAA,QAAAE,MAAA,CAAAH,QAAA,CAAAS,MAAA,kBAAAN,MAAA,CAAAH,QAAA,CAAAS,MAAA,CAAAL,IAAA,MAAyD;;;ADlC3E,OAAM,MAAOM,kBAAkB;EAG7BC,YACUC,WAAwB,EACxBC,MAAc;IADd,KAAAD,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;EACb;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACd,QAAQ,GAAG,IAAI,CAACY,WAAW,CAACG,WAAW,EAAE;EAChD;EAEArB,MAAMA,CAAA;IACJ,IAAI,CAACkB,WAAW,CAAClB,MAAM,EAAE;IACzB,IAAI,CAACmB,MAAM,CAACG,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;EAClC;;;uBAfWN,kBAAkB,EAAA3B,EAAA,CAAAkC,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAApC,EAAA,CAAAkC,iBAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAlBX,kBAAkB;MAAAY,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,4BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCT/B7C,EAAA,CAAAC,cAAA,aAAiC;UAGCD,EAAA,CAAAE,MAAA,8BAAuB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACxDH,EAAA,CAAA+C,UAAA,IAAAC,iCAAA,iBAQM;UACRhD,EAAA,CAAAG,YAAA,EAAM;UAGRH,EAAA,CAAAC,cAAA,cAA6B;UAEzBD,EAAA,CAAA+C,UAAA,IAAAE,iCAAA,mBA4BM;UAENjD,EAAA,CAAAC,cAAA,aAA6B;UAC3BD,EAAA,CAAAY,cAAA,EAAkE;UAAlEZ,EAAA,CAAAC,cAAA,cAAkE;UAChED,EAAA,CAAAa,SAAA,gBAA+L;UACjMb,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAkD,eAAA,EAAI;UAAJlD,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAE,MAAA,gCAAmB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC5BH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAE,MAAA,4EAAqD;UAAAF,EAAA,CAAAG,YAAA,EAAI;;;UAjDtCH,EAAA,CAAAc,SAAA,GAAc;UAAdd,EAAA,CAAAmD,UAAA,SAAAL,GAAA,CAAA7B,QAAA,CAAc;UAcdjB,EAAA,CAAAc,SAAA,GAAc;UAAdd,EAAA,CAAAmD,UAAA,SAAAL,GAAA,CAAA7B,QAAA,CAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}