import { Injectable } from '@angular/core';
import { HttpClient, HttpErrorResponse, HttpHeaders } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError, map } from 'rxjs/operators';

import { AuthRequest } from '../models/auth-request.interface';
import { AuthResponse } from '../models/auth-response.interface';
import { environment } from '../../environments/environment';

@Injectable({
  providedIn: 'root'
})
export class AuthService {
  private readonly baseUrl = environment.apiUrl;
  private readonly authEndpoint = environment.authEndpoint;

  constructor(private http: HttpClient) {}

  /**
   * Authentifie un utilisateur avec les informations de connexion
   * @param authRequest Les informations de connexion
   * @returns Observable<AuthResponse>
   */
  authenticate(authRequest: AuthRequest): Observable<AuthResponse> {
    const headers = new HttpHeaders({
      'Content-Type': 'application/json',
      'Accept': 'application/json'
    });

    return this.http.post<AuthResponse>(`${this.baseUrl}${this.authEndpoint}`, authRequest, { headers })
      .pipe(
        map(response => {
          // Stocker le token si l'authentification réussit
          if (response.header.success && response.body?.token) {
            this.setToken(response.body.token);
            this.setUserInfo(response.body.userInfo);
            this.setTokenExpiration(response.body.expiresOn);
          }
          return response;
        }),
        catchError(this.handleError)
      );
  }

  /**
   * Stocke le token d'authentification
   * @param token Le token JWT
   */
  private setToken(token: string): void {
    localStorage.setItem('auth_token', token);
  }

  /**
   * Récupère le token d'authentification
   * @returns Le token ou null
   */
  getToken(): string | null {
    return localStorage.getItem('auth_token');
  }

  /**
   * Stocke la date d'expiration du token
   * @param expiresOn Date d'expiration
   */
  private setTokenExpiration(expiresOn: string): void {
    localStorage.setItem('token_expires_on', expiresOn);
  }

  /**
   * Récupère la date d'expiration du token
   * @returns Date d'expiration ou null
   */
  getTokenExpiration(): string | null {
    return localStorage.getItem('token_expires_on');
  }

  /**
   * Stocke les informations utilisateur
   * @param userInfo Les informations utilisateur
   */
  private setUserInfo(userInfo: any): void {
    localStorage.setItem('user_info', JSON.stringify(userInfo));
  }

  /**
   * Récupère les informations utilisateur
   * @returns Les informations utilisateur ou null
   */
  getUserInfo(): any {
    const userInfo = localStorage.getItem('user_info');
    return userInfo ? JSON.parse(userInfo) : null;
  }

  /**
   * Vérifie si l'utilisateur est connecté et si le token n'est pas expiré
   * @returns true si connecté et token valide, false sinon
   */
  isAuthenticated(): boolean {
    const token = this.getToken();
    const expiresOn = this.getTokenExpiration();
    
    if (!token || !expiresOn) {
      return false;
    }

    // Vérifier si le token n'est pas expiré
    const expirationDate = new Date(expiresOn);
    const now = new Date();
    
    if (now >= expirationDate) {
      this.logout(); // Nettoyer les données expirées
      return false;
    }

    return true;
  }

  /**
   * Déconnecte l'utilisateur
   */
  logout(): void {
    localStorage.removeItem('auth_token');
    localStorage.removeItem('user_info');
    localStorage.removeItem('token_expires_on');
  }

  /**
   * Gère les erreurs HTTP
   * @param error L'erreur HTTP
   * @returns Observable avec l'erreur
   */
  private handleError(error: HttpErrorResponse): Observable<never> {
    let errorMessage = 'Une erreur est survenue lors de l\'authentification';
    
    if (error.error instanceof ErrorEvent) {
      // Erreur côté client
      errorMessage = `Erreur: ${error.error.message}`;
    } else {
      // Erreur côté serveur
      switch (error.status) {
        case 401:
          errorMessage = 'Identifiants incorrects';
          break;
        case 403:
          errorMessage = 'Accès refusé';
          break;
        case 404:
          errorMessage = 'Service d\'authentification non trouvé';
          break;
        case 500:
          errorMessage = 'Erreur interne du serveur';
          break;
        case 0:
          errorMessage = 'Impossible de contacter le serveur. Vérifiez votre connexion.';
          break;
        default:
          errorMessage = `Erreur ${error.status}: ${error.message}`;
      }
      
      // Si l'erreur contient un message du backend, l'utiliser
      if (error.error && typeof error.error === 'object' && error.error.message) {
        errorMessage = error.error.message;
      }
    }
    
    console.error('Erreur d\'authentification:', error);
    return throwError(() => new Error(errorMessage));
  }

  /**
   * Obtient les headers d'autorisation pour les requêtes authentifiées
   * @returns HttpHeaders avec le token d'autorisation
   */
  getAuthHeaders(): HttpHeaders {
    const token = this.getToken();
    if (token) {
      return new HttpHeaders({
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      });
    }
    return new HttpHeaders({
      'Content-Type': 'application/json'
    });
  }
}
