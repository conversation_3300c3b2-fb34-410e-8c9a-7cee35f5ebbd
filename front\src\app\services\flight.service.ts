import { Injectable } from '@angular/core';
import { HttpClient, HttpErrorResponse, HttpHeaders } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError, map } from 'rxjs/operators';

import { 
  OneWayRequest, 
  RoundTripRequest, 
  MulticityRequest,
  OneWayResponse,
  RoundTripResponse,
  MulticityResponse,
  FlightSearchForm,
  Location,
  Passenger,
  PassengerType,
  LocationType,
  ProductType
} from '../models/flight-search.interface';
import { AuthService } from './auth.service';
import { environment } from '../../environments/environment';

@Injectable({
  providedIn: 'root'
})
export class FlightService {
  private readonly baseUrl = environment.apiUrl;
  private readonly flightSearchEndpoint = '/api/flights';

  constructor(
    private http: HttpClient,
    private authService: AuthService
  ) {}

  /**
   * Recherche de vols aller simple
   */
  searchOneWayFlights(searchForm: FlightSearchForm): Observable<OneWayResponse> {
    const request = this.buildOneWayRequest(searchForm);
    const headers = this.authService.getAuthHeaders();

    return this.http.post<OneWayResponse>(`${this.baseUrl}${this.flightSearchEndpoint}/search/oneway`, request, { headers })
      .pipe(
        catchError(this.handleError)
      );
  }

  /**
   * Recherche de vols aller-retour
   */
  searchRoundTripFlights(searchForm: FlightSearchForm): Observable<RoundTripResponse> {
    const request = this.buildRoundTripRequest(searchForm);
    const headers = this.authService.getAuthHeaders();

    return this.http.post<RoundTripResponse>(`${this.baseUrl}${this.flightSearchEndpoint}/search/roundtrip`, request, { headers })
      .pipe(
        catchError(this.handleError)
      );
  }

  /**
   * Recherche de vols multi-destinations
   */
  searchMulticityFlights(searchForm: FlightSearchForm): Observable<MulticityResponse> {
    const request = this.buildMulticityRequest(searchForm);
    const headers = this.authService.getAuthHeaders();

    return this.http.post<MulticityResponse>(`${this.baseUrl}${this.flightSearchEndpoint}/search/multicity`, request, { headers })
      .pipe(
        catchError(this.handleError)
      );
  }

  /**
   * Construction de la requête One Way
   */
  private buildOneWayRequest(searchForm: FlightSearchForm): OneWayRequest {
    return {
      ProductType: ProductType.FLIGHT,
      ServiceTypes: ['1'],
      CheckIn: searchForm.departureDate,
      DepartureLocations: [this.buildLocation(searchForm.departureLocation)],
      ArrivalLocations: [this.buildLocation(searchForm.arrivalLocation)],
      Passengers: this.buildPassengers(searchForm.passengers),
      showOnlyNonStopFlight: searchForm.directFlightsOnly,
      acceptPendingProviders: true,
      forceFlightBundlePackage: false,
      disablePackageOfferTotalPrice: false,
      calculateFlightFees: true,
      flightClasses: [searchForm.flightClass],
      Culture: 'en-US',
      Currency: 'USD'
    };
  }

  /**
   * Construction de la requête Round Trip
   */
  private buildRoundTripRequest(searchForm: FlightSearchForm): RoundTripRequest {
    const nights = this.calculateNights(searchForm.departureDate, searchForm.returnDate || '');
    
    return {
      ProductType: ProductType.FLIGHT,
      ServiceTypes: ['1'],
      DepartureLocations: [this.buildLocation(searchForm.departureLocation)],
      ArrivalLocations: [this.buildLocation(searchForm.arrivalLocation)],
      CheckIn: searchForm.departureDate,
      Night: nights,
      Passengers: this.buildPassengers(searchForm.passengers),
      acceptPendingProviders: true,
      forceFlightBundlePackage: false,
      disablePackageOfferTotalPrice: false,
      showOnlyNonStopFlight: searchForm.directFlightsOnly,
      calculateFlightFees: true,
      Culture: 'en-US',
      Currency: 'USD'
    };
  }

  /**
   * Construction de la requête Multi-city
   */
  private buildMulticityRequest(searchForm: FlightSearchForm): MulticityRequest {
    return {
      serviceTypes: ['1'],
      productType: ProductType.FLIGHT,
      arrivalLocations: [this.buildLocation(searchForm.arrivalLocation)],
      departureLocations: [this.buildLocation(searchForm.departureLocation)],
      passengers: this.buildPassengers(searchForm.passengers),
      checkIns: [searchForm.departureDate],
      calculateFlightFees: true,
      acceptPendingProviders: true,
      forceFlightBundlePackage: false,
      disablePackageOfferTotalPrice: false,
      showOnlyNonStopFlight: searchForm.directFlightsOnly,
      culture: 'en-US',
      currency: 'USD'
    };
  }

  /**
   * Construction d'un objet Location
   */
  private buildLocation(locationCode: string): Location {
    return {
      id: locationCode,
      type: LocationType.AIRPORT // Par défaut, peut être ajusté selon vos besoins
    };
  }

  /**
   * Construction de la liste des passagers
   */
  private buildPassengers(passengers: { adults: number; children: number; infants: number }): Passenger[] {
    const passengerList: Passenger[] = [];

    if (passengers.adults > 0) {
      passengerList.push({
        type: PassengerType.ADULT,
        count: passengers.adults
      });
    }

    if (passengers.children > 0) {
      passengerList.push({
        type: PassengerType.CHILD,
        count: passengers.children
      });
    }

    if (passengers.infants > 0) {
      passengerList.push({
        type: PassengerType.INFANT,
        count: passengers.infants
      });
    }

    return passengerList;
  }

  /**
   * Calcul du nombre de nuits entre deux dates
   */
  private calculateNights(checkIn: string, checkOut: string): number {
    const startDate = new Date(checkIn);
    const endDate = new Date(checkOut);
    const timeDiff = endDate.getTime() - startDate.getTime();
    return Math.ceil(timeDiff / (1000 * 3600 * 24));
  }

  /**
   * Gestion des erreurs HTTP
   */
  private handleError(error: HttpErrorResponse): Observable<never> {
    let errorMessage = 'Une erreur est survenue lors de la recherche de vols';
    
    if (error.error instanceof ErrorEvent) {
      errorMessage = `Erreur: ${error.error.message}`;
    } else {
      switch (error.status) {
        case 401:
          errorMessage = 'Session expirée. Veuillez vous reconnecter.';
          break;
        case 403:
          errorMessage = 'Accès refusé pour cette recherche';
          break;
        case 404:
          errorMessage = 'Service de recherche non trouvé';
          break;
        case 500:
          errorMessage = 'Erreur interne du serveur';
          break;
        case 0:
          errorMessage = 'Impossible de contacter le serveur. Vérifiez votre connexion.';
          break;
        default:
          errorMessage = `Erreur ${error.status}: ${error.message}`;
      }
      
      if (error.error && typeof error.error === 'object' && error.error.message) {
        errorMessage = error.error.message;
      }
    }
    
    console.error('Erreur de recherche de vols:', error);
    return throwError(() => new Error(errorMessage));
  }
}
