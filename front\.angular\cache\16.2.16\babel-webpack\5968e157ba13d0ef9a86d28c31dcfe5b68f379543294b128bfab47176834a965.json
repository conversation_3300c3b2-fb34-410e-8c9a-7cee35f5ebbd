{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./login.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./login.component.css?ngResource\";\nimport { Component } from '@angular/core';\nimport { FormBuilder, Validators } from '@angular/forms';\nimport { Router } from '@angular/router';\nimport { AuthService } from '../../services/auth.service';\nexport let LoginComponent = class LoginComponent {\n  constructor(formBuilder, authService, router) {\n    this.formBuilder = formBuilder;\n    this.authService = authService;\n    this.router = router;\n    this.isLoading = false;\n    this.errorMessage = '';\n    this.showPassword = false;\n  }\n  ngOnInit() {\n    this.initializeForm();\n    // Rediriger si déjà connecté\n    if (this.authService.isAuthenticated()) {\n      this.router.navigate(['/dashboard']); // Ajustez selon votre route de destination\n    }\n  }\n  /**\n   * Initialise le formulaire de connexion avec les validations\n   */\n  initializeForm() {\n    this.loginForm = this.formBuilder.group({\n      agency: ['', [Validators.required, Validators.minLength(2)]],\n      user: ['', [Validators.required, Validators.minLength(2)]],\n      password: ['', [Validators.required, Validators.minLength(6)]]\n    });\n  }\n  /**\n   * Getter pour accéder facilement aux contrôles du formulaire\n   */\n  get formControls() {\n    return this.loginForm.controls;\n  }\n  /**\n   * Bascule la visibilité du mot de passe\n   */\n  togglePasswordVisibility() {\n    this.showPassword = !this.showPassword;\n  }\n  /**\n   * Soumet le formulaire de connexion\n   */\n  onSubmit() {\n    if (this.loginForm.valid && !this.isLoading) {\n      this.isLoading = true;\n      this.errorMessage = '';\n      // Créer l'objet de requête avec les noms de propriétés exacts du backend\n      const authRequest = {\n        Agency: this.loginForm.value.agency.trim(),\n        User: this.loginForm.value.user.trim(),\n        Password: this.loginForm.value.password\n      };\n      this.authService.authenticate(authRequest).subscribe({\n        next: response => {\n          this.isLoading = false;\n          if (response.header.success) {\n            // Connexion réussie\n            console.log('Connexion réussie:', response.body.userInfo);\n            // Rediriger vers le dashboard ou la page d'accueil\n            this.router.navigate(['/dashboard']); // Ajustez selon votre route\n          } else {\n            // Erreur retournée par l'API\n            this.handleApiError(response);\n          }\n        },\n        error: error => {\n          this.isLoading = false;\n          this.errorMessage = error.message || 'Une erreur est survenue lors de la connexion';\n          console.error('Erreur de connexion:', error);\n        }\n      });\n    } else {\n      // Marquer tous les champs comme touchés pour afficher les erreurs\n      this.markFormGroupTouched();\n    }\n  }\n  /**\n   * Gère les erreurs retournées par l'API\n   */\n  handleApiError(response) {\n    if (response.header.messages && response.header.messages.length > 0) {\n      // Utiliser le premier message d'erreur de l'API\n      this.errorMessage = response.header.messages[0].message;\n    } else {\n      this.errorMessage = 'Échec de l\\'authentification';\n    }\n  }\n  /**\n   * Marque tous les champs du formulaire comme touchés\n   */\n  markFormGroupTouched() {\n    Object.keys(this.loginForm.controls).forEach(key => {\n      const control = this.loginForm.get(key);\n      control?.markAsTouched();\n    });\n  }\n  /**\n   * Vérifie si un champ a une erreur et a été touché\n   */\n  hasError(fieldName, errorType) {\n    const field = this.loginForm.get(fieldName);\n    return !!(field?.hasError(errorType) && field?.touched);\n  }\n  /**\n   * Récupère le message d'erreur pour un champ\n   */\n  getErrorMessage(fieldName) {\n    const field = this.loginForm.get(fieldName);\n    if (field?.hasError('required')) {\n      return `${this.getFieldDisplayName(fieldName)} est requis`;\n    }\n    if (field?.hasError('minlength')) {\n      const requiredLength = field.errors?.['minlength']?.requiredLength;\n      return `${this.getFieldDisplayName(fieldName)} doit contenir au moins ${requiredLength} caractères`;\n    }\n    return '';\n  }\n  /**\n   * Retourne le nom d'affichage pour un champ\n   */\n  getFieldDisplayName(fieldName) {\n    const displayNames = {\n      agency: 'Le code agence',\n      user: 'Le code utilisateur',\n      password: 'Le mot de passe'\n    };\n    return displayNames[fieldName] || fieldName;\n  }\n  /**\n   * Nettoie le message d'erreur\n   */\n  clearError() {\n    this.errorMessage = '';\n  }\n  static {\n    this.ctorParameters = () => [{\n      type: FormBuilder\n    }, {\n      type: AuthService\n    }, {\n      type: Router\n    }];\n  }\n};\nLoginComponent = __decorate([Component({\n  selector: 'app-login',\n  template: __NG_CLI_RESOURCE__0,\n  styles: [__NG_CLI_RESOURCE__1]\n})], LoginComponent);", "map": {"version": 3, "names": ["Component", "FormBuilder", "Validators", "Router", "AuthService", "LoginComponent", "constructor", "formBuilder", "authService", "router", "isLoading", "errorMessage", "showPassword", "ngOnInit", "initializeForm", "isAuthenticated", "navigate", "loginForm", "group", "agency", "required", "<PERSON><PERSON><PERSON><PERSON>", "user", "password", "formControls", "controls", "togglePasswordVisibility", "onSubmit", "valid", "authRequest", "Agency", "value", "trim", "User", "Password", "authenticate", "subscribe", "next", "response", "header", "success", "console", "log", "body", "userInfo", "handleApiError", "error", "message", "markFormGroupTouched", "messages", "length", "Object", "keys", "for<PERSON>ach", "key", "control", "get", "<PERSON><PERSON><PERSON><PERSON>ched", "<PERSON><PERSON><PERSON><PERSON>", "fieldName", "errorType", "field", "touched", "getErrorMessage", "getFieldDisplayName", "<PERSON><PERSON><PERSON><PERSON>", "errors", "displayNames", "clearError", "__decorate", "selector", "template", "__NG_CLI_RESOURCE__0"], "sources": ["C:\\Users\\<USER>\\Desktop\\angular\\front\\src\\app\\components\\login\\login.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { Router } from '@angular/router';\n\nimport { AuthService } from '../../services/auth.service';\nimport { AuthRequest } from '../../models/auth-request.interface';\n\n@Component({\n  selector: 'app-login',\n  templateUrl: './login.component.html',\n  styleUrls: ['./login.component.css']\n})\nexport class LoginComponent implements OnInit {\n  loginForm!: FormGroup;\n  isLoading = false;\n  errorMessage = '';\n  showPassword = false;\n\n  constructor(\n    private formBuilder: FormBuilder,\n    private authService: AuthService,\n    private router: Router\n  ) {}\n\n  ngOnInit(): void {\n    this.initializeForm();\n    \n    // Rediriger si déjà connecté\n    if (this.authService.isAuthenticated()) {\n      this.router.navigate(['/dashboard']); // Ajustez selon votre route de destination\n    }\n  }\n\n  /**\n   * Initialise le formulaire de connexion avec les validations\n   */\n  private initializeForm(): void {\n    this.loginForm = this.formBuilder.group({\n      agency: ['', [Validators.required, Validators.minLength(2)]],\n      user: ['', [Validators.required, Validators.minLength(2)]],\n      password: ['', [Validators.required, Validators.minLength(6)]]\n    });\n  }\n\n  /**\n   * Getter pour accéder facilement aux contrôles du formulaire\n   */\n  get formControls() {\n    return this.loginForm.controls;\n  }\n\n  /**\n   * Bascule la visibilité du mot de passe\n   */\n  togglePasswordVisibility(): void {\n    this.showPassword = !this.showPassword;\n  }\n\n  /**\n   * Soumet le formulaire de connexion\n   */\n  onSubmit(): void {\n    if (this.loginForm.valid && !this.isLoading) {\n      this.isLoading = true;\n      this.errorMessage = '';\n\n      // Créer l'objet de requête avec les noms de propriétés exacts du backend\n      const authRequest: AuthRequest = {\n        Agency: this.loginForm.value.agency.trim(),\n        User: this.loginForm.value.user.trim(),\n        Password: this.loginForm.value.password\n      };\n\n      this.authService.authenticate(authRequest).subscribe({\n        next: (response) => {\n          this.isLoading = false;\n          \n          if (response.header.success) {\n            // Connexion réussie\n            console.log('Connexion réussie:', response.body.userInfo);\n            \n            // Rediriger vers le dashboard ou la page d'accueil\n            this.router.navigate(['/dashboard']); // Ajustez selon votre route\n          } else {\n            // Erreur retournée par l'API\n            this.handleApiError(response);\n          }\n        },\n        error: (error) => {\n          this.isLoading = false;\n          this.errorMessage = error.message || 'Une erreur est survenue lors de la connexion';\n          console.error('Erreur de connexion:', error);\n        }\n      });\n    } else {\n      // Marquer tous les champs comme touchés pour afficher les erreurs\n      this.markFormGroupTouched();\n    }\n  }\n\n  /**\n   * Gère les erreurs retournées par l'API\n   */\n  private handleApiError(response: any): void {\n    if (response.header.messages && response.header.messages.length > 0) {\n      // Utiliser le premier message d'erreur de l'API\n      this.errorMessage = response.header.messages[0].message;\n    } else {\n      this.errorMessage = 'Échec de l\\'authentification';\n    }\n  }\n\n  /**\n   * Marque tous les champs du formulaire comme touchés\n   */\n  private markFormGroupTouched(): void {\n    Object.keys(this.loginForm.controls).forEach(key => {\n      const control = this.loginForm.get(key);\n      control?.markAsTouched();\n    });\n  }\n\n  /**\n   * Vérifie si un champ a une erreur et a été touché\n   */\n  hasError(fieldName: string, errorType: string): boolean {\n    const field = this.loginForm.get(fieldName);\n    return !!(field?.hasError(errorType) && field?.touched);\n  }\n\n  /**\n   * Récupère le message d'erreur pour un champ\n   */\n  getErrorMessage(fieldName: string): string {\n    const field = this.loginForm.get(fieldName);\n    \n    if (field?.hasError('required')) {\n      return `${this.getFieldDisplayName(fieldName)} est requis`;\n    }\n    \n    if (field?.hasError('minlength')) {\n      const requiredLength = field.errors?.['minlength']?.requiredLength;\n      return `${this.getFieldDisplayName(fieldName)} doit contenir au moins ${requiredLength} caractères`;\n    }\n    \n    return '';\n  }\n\n  /**\n   * Retourne le nom d'affichage pour un champ\n   */\n  private getFieldDisplayName(fieldName: string): string {\n    const displayNames: { [key: string]: string } = {\n      agency: 'Le code agence',\n      user: 'Le code utilisateur',\n      password: 'Le mot de passe'\n    };\n    \n    return displayNames[fieldName] || fieldName;\n  }\n\n  /**\n   * Nettoie le message d'erreur\n   */\n  clearError(): void {\n    this.errorMessage = '';\n  }\n}\n"], "mappings": ";;;AAAA,SAASA,SAAS,QAAgB,eAAe;AACjD,SAASC,WAAW,EAAaC,UAAU,QAAQ,gBAAgB;AACnE,SAASC,MAAM,QAAQ,iBAAiB;AAExC,SAASC,WAAW,QAAQ,6BAA6B;AAQlD,WAAMC,cAAc,GAApB,MAAMA,cAAc;EAMzBC,YACUC,WAAwB,EACxBC,WAAwB,EACxBC,MAAc;IAFd,KAAAF,WAAW,GAAXA,WAAW;IACX,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IAPhB,KAAAC,SAAS,GAAG,KAAK;IACjB,KAAAC,YAAY,GAAG,EAAE;IACjB,KAAAC,YAAY,GAAG,KAAK;EAMjB;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,cAAc,EAAE;IAErB;IACA,IAAI,IAAI,CAACN,WAAW,CAACO,eAAe,EAAE,EAAE;MACtC,IAAI,CAACN,MAAM,CAACO,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;;EAE1C;EAEA;;;EAGQF,cAAcA,CAAA;IACpB,IAAI,CAACG,SAAS,GAAG,IAAI,CAACV,WAAW,CAACW,KAAK,CAAC;MACtCC,MAAM,EAAE,CAAC,EAAE,EAAE,CAACjB,UAAU,CAACkB,QAAQ,EAAElB,UAAU,CAACmB,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5DC,IAAI,EAAE,CAAC,EAAE,EAAE,CAACpB,UAAU,CAACkB,QAAQ,EAAElB,UAAU,CAACmB,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1DE,QAAQ,EAAE,CAAC,EAAE,EAAE,CAACrB,UAAU,CAACkB,QAAQ,EAAElB,UAAU,CAACmB,SAAS,CAAC,CAAC,CAAC,CAAC;KAC9D,CAAC;EACJ;EAEA;;;EAGA,IAAIG,YAAYA,CAAA;IACd,OAAO,IAAI,CAACP,SAAS,CAACQ,QAAQ;EAChC;EAEA;;;EAGAC,wBAAwBA,CAAA;IACtB,IAAI,CAACd,YAAY,GAAG,CAAC,IAAI,CAACA,YAAY;EACxC;EAEA;;;EAGAe,QAAQA,CAAA;IACN,IAAI,IAAI,CAACV,SAAS,CAACW,KAAK,IAAI,CAAC,IAAI,CAAClB,SAAS,EAAE;MAC3C,IAAI,CAACA,SAAS,GAAG,IAAI;MACrB,IAAI,CAACC,YAAY,GAAG,EAAE;MAEtB;MACA,MAAMkB,WAAW,GAAgB;QAC/BC,MAAM,EAAE,IAAI,CAACb,SAAS,CAACc,KAAK,CAACZ,MAAM,CAACa,IAAI,EAAE;QAC1CC,IAAI,EAAE,IAAI,CAAChB,SAAS,CAACc,KAAK,CAACT,IAAI,CAACU,IAAI,EAAE;QACtCE,QAAQ,EAAE,IAAI,CAACjB,SAAS,CAACc,KAAK,CAACR;OAChC;MAED,IAAI,CAACf,WAAW,CAAC2B,YAAY,CAACN,WAAW,CAAC,CAACO,SAAS,CAAC;QACnDC,IAAI,EAAGC,QAAQ,IAAI;UACjB,IAAI,CAAC5B,SAAS,GAAG,KAAK;UAEtB,IAAI4B,QAAQ,CAACC,MAAM,CAACC,OAAO,EAAE;YAC3B;YACAC,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEJ,QAAQ,CAACK,IAAI,CAACC,QAAQ,CAAC;YAEzD;YACA,IAAI,CAACnC,MAAM,CAACO,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;WACvC,MAAM;YACL;YACA,IAAI,CAAC6B,cAAc,CAACP,QAAQ,CAAC;;QAEjC,CAAC;QACDQ,KAAK,EAAGA,KAAK,IAAI;UACf,IAAI,CAACpC,SAAS,GAAG,KAAK;UACtB,IAAI,CAACC,YAAY,GAAGmC,KAAK,CAACC,OAAO,IAAI,8CAA8C;UACnFN,OAAO,CAACK,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;QAC9C;OACD,CAAC;KACH,MAAM;MACL;MACA,IAAI,CAACE,oBAAoB,EAAE;;EAE/B;EAEA;;;EAGQH,cAAcA,CAACP,QAAa;IAClC,IAAIA,QAAQ,CAACC,MAAM,CAACU,QAAQ,IAAIX,QAAQ,CAACC,MAAM,CAACU,QAAQ,CAACC,MAAM,GAAG,CAAC,EAAE;MACnE;MACA,IAAI,CAACvC,YAAY,GAAG2B,QAAQ,CAACC,MAAM,CAACU,QAAQ,CAAC,CAAC,CAAC,CAACF,OAAO;KACxD,MAAM;MACL,IAAI,CAACpC,YAAY,GAAG,8BAA8B;;EAEtD;EAEA;;;EAGQqC,oBAAoBA,CAAA;IAC1BG,MAAM,CAACC,IAAI,CAAC,IAAI,CAACnC,SAAS,CAACQ,QAAQ,CAAC,CAAC4B,OAAO,CAACC,GAAG,IAAG;MACjD,MAAMC,OAAO,GAAG,IAAI,CAACtC,SAAS,CAACuC,GAAG,CAACF,GAAG,CAAC;MACvCC,OAAO,EAAEE,aAAa,EAAE;IAC1B,CAAC,CAAC;EACJ;EAEA;;;EAGAC,QAAQA,CAACC,SAAiB,EAAEC,SAAiB;IAC3C,MAAMC,KAAK,GAAG,IAAI,CAAC5C,SAAS,CAACuC,GAAG,CAACG,SAAS,CAAC;IAC3C,OAAO,CAAC,EAAEE,KAAK,EAAEH,QAAQ,CAACE,SAAS,CAAC,IAAIC,KAAK,EAAEC,OAAO,CAAC;EACzD;EAEA;;;EAGAC,eAAeA,CAACJ,SAAiB;IAC/B,MAAME,KAAK,GAAG,IAAI,CAAC5C,SAAS,CAACuC,GAAG,CAACG,SAAS,CAAC;IAE3C,IAAIE,KAAK,EAAEH,QAAQ,CAAC,UAAU,CAAC,EAAE;MAC/B,OAAO,GAAG,IAAI,CAACM,mBAAmB,CAACL,SAAS,CAAC,aAAa;;IAG5D,IAAIE,KAAK,EAAEH,QAAQ,CAAC,WAAW,CAAC,EAAE;MAChC,MAAMO,cAAc,GAAGJ,KAAK,CAACK,MAAM,GAAG,WAAW,CAAC,EAAED,cAAc;MAClE,OAAO,GAAG,IAAI,CAACD,mBAAmB,CAACL,SAAS,CAAC,2BAA2BM,cAAc,aAAa;;IAGrG,OAAO,EAAE;EACX;EAEA;;;EAGQD,mBAAmBA,CAACL,SAAiB;IAC3C,MAAMQ,YAAY,GAA8B;MAC9ChD,MAAM,EAAE,gBAAgB;MACxBG,IAAI,EAAE,qBAAqB;MAC3BC,QAAQ,EAAE;KACX;IAED,OAAO4C,YAAY,CAACR,SAAS,CAAC,IAAIA,SAAS;EAC7C;EAEA;;;EAGAS,UAAUA,CAAA;IACR,IAAI,CAACzD,YAAY,GAAG,EAAE;EACxB;;;;;;;;;;;AA1JWN,cAAc,GAAAgE,UAAA,EAL1BrE,SAAS,CAAC;EACTsE,QAAQ,EAAE,WAAW;EACrBC,QAAA,EAAAC,oBAAqC;;CAEtC,CAAC,C,EACWnE,cAAc,CA2J1B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}