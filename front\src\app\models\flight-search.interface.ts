/**
 * Interfaces pour les requêtes de recherche de vols
 * Correspondant aux modèles du backend Spring Boot
 */

// Interfaces communes
export interface Location {
  id: string;
  type: number;
  provider?: number;
}

export interface Passenger {
  type: number;
  count: number;
}

export interface CorporateRule {
  Airline: string;
  Supplier: string;
}

export interface CorporateCode {
  Code: string;
  Rule: CorporateRule;
}

export interface GetOptionsParameters {
  flightBaggageGetOption: number;
}

export interface AdditionalParameters {
  getOptionsParameters?: GetOptionsParameters;
  CorporateCodes?: CorporateCode[];
}

// Interface pour les requêtes One Way
export interface OneWayRequest {
  ProductType: number;
  ServiceTypes: string[];
  CheckIn: string;
  DepartureLocations: Location[];
  ArrivalLocations: Location[];
  Passengers: Passenger[];
  showOnlyNonStopFlight: boolean;
  additionalParameters?: AdditionalParameters;
  acceptPendingProviders: boolean;
  forceFlightBundlePackage: boolean;
  disablePackageOfferTotalPrice: boolean;
  calculateFlightFees: boolean;
  flightClasses?: number[];
  Culture: string;
  Currency: string;
}

// Interface pour les requêtes Round Trip
export interface RoundTripRequest {
  ProductType: number;
  ServiceTypes: string[];
  DepartureLocations: Location[];
  ArrivalLocations: Location[];
  CheckIn: string;
  Night: number;
  Passengers: Passenger[];
  acceptPendingProviders: boolean;
  forceFlightBundlePackage: boolean;
  disablePackageOfferTotalPrice: boolean;
  supportedFlightReponseListTypes?: number[];
  showOnlyNonStopFlight: boolean;
  additionalParameters?: AdditionalParameters;
  calculateFlightFees: boolean;
  Culture: string;
  Currency: string;
}

// Interface pour les requêtes Multi-city
export interface MulticityRequest {
  serviceTypes: string[];
  productType: number;
  arrivalLocations: Location[];
  departureLocations: Location[];
  passengers: Passenger[];
  checkIns: string[];
  calculateFlightFees: boolean;
  acceptPendingProviders: boolean;
  additionalParameters?: AdditionalParameters;
  forceFlightBundlePackage: boolean;
  disablePackageOfferTotalPrice: boolean;
  showOnlyNonStopFlight: boolean;
  supportedFlightReponseListTypes?: number[];
  culture: string;
  currency: string;
}

// Interfaces pour les réponses (structure de base)
export interface FlightSearchHeader {
  requestId: string;
  success: boolean;
  messages: FlightSearchMessage[];
}

export interface FlightSearchMessage {
  id: number;
  code: string;
  messageType: number;
  message: string;
}

export interface FlightSearchResponse {
  header: FlightSearchHeader;
  body: any; // À définir selon la structure exacte de votre API
}

// Types pour les réponses spécifiques
export interface OneWayResponse extends FlightSearchResponse {}
export interface RoundTripResponse extends FlightSearchResponse {}
export interface MulticityResponse extends FlightSearchResponse {}

// Enums et constantes
export enum PassengerType {
  ADULT = 1,
  CHILD = 2,
  INFANT = 3
}

export enum LocationType {
  AIRPORT = 1,
  CITY = 2,
  COUNTRY = 3
}

export enum FlightClass {
  ECONOMY = 1,
  PREMIUM_ECONOMY = 2,
  BUSINESS = 3,
  FIRST = 4
}

export enum ProductType {
  FLIGHT = 2
}

// Interface pour le formulaire de recherche
export interface FlightSearchForm {
  tripType: 'oneWay' | 'roundTrip' | 'multiCity';
  departureLocation: string;
  arrivalLocation: string;
  departureDate: string;
  returnDate?: string;
  passengers: {
    adults: number;
    children: number;
    infants: number;
  };
  flightClass: FlightClass;
  directFlightsOnly: boolean;
  preferredAirline?: string;
}
