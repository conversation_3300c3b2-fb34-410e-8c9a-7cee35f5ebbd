/**
 * Interfaces correspondant aux classes AuthResponse du backend Spring Boot
 */
export interface AuthResponse {
  header: Header;
  body: Body;
}

export interface Header {
  requestId: string;
  success: boolean;
  messages: Message[];
}

export interface Message {
  id: number;
  code: string;
  messageType: number;
  message: string;
}

export interface Body {
  token: string;
  expiresOn: string; // ZonedDateTime sera sérialisé en string ISO
  tokenId: number;
  userInfo: UserInfo;
}

export interface UserInfo {
  code: string;
  name: string;
  mainAgency: Agency;
  agency: Agency;
  office: Office;
  operator: Operator;
  market: Market;
}

export interface Agency {
  code: string;
  name: string;
  registerCode: string;
}

export interface Office {
  code: string;
  name: string;
}

export interface Operator {
  code: string;
  name: string;
  thumbnail: string;
}

export interface Market {
  code: string;
  name: string;
  favicon: string;
}
