{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/auth.service\";\nimport * as i2 from \"@angular/router\";\nexport class DashboardComponent {\n  constructor(authService, router) {\n    this.authService = authService;\n    this.router = router;\n  }\n  ngOnInit() {\n    this.userInfo = this.authService.getUserInfo();\n  }\n  logout() {\n    this.authService.logout();\n    this.router.navigate(['/login']);\n  }\n  static {\n    this.ɵfac = function DashboardComponent_Factory(t) {\n      return new (t || DashboardComponent)(i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i2.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: DashboardComponent,\n      selectors: [[\"app-dashboard\"]],\n      decls: 126,\n      vars: 0,\n      consts: [[1, \"dashboard-container\"], [1, \"dashboard-header\"], [1, \"header-content\"], [1, \"logo-section\"], [1, \"logo\"], [\"viewBox\", \"0 0 24 24\", \"fill\", \"currentColor\", 1, \"logo-icon\"], [\"d\", \"M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5\"], [1, \"logo-text\"], [1, \"demo-info\"], [1, \"demo-id\"], [1, \"demo-details\"], [1, \"header-contact\"], [1, \"contact-item\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"contact-icon\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M15 11a3 3 0 11-6 0 3 3 0 016 0z\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"], [1, \"header-nav\"], [\"href\", \"#\", 1, \"nav-item\", \"active\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"nav-icon\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6\"], [\"href\", \"#\", 1, \"nav-item\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129\"], [1, \"logout-btn\", 3, \"click\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1\"], [1, \"dashboard-main\"], [1, \"dashboard-grid\"], [1, \"sidebar\"], [1, \"sidebar-section\"], [1, \"sidebar-item\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"sidebar-icon\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-5 0a4 4 0 11-8 0 4 4 0 018 0z\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2\"], [1, \"main-content\"], [1, \"content-grid\"], [1, \"service-card\", \"flights-card\"], [1, \"card-icon\"], [\"viewBox\", \"0 0 24 24\", \"fill\", \"currentColor\"], [\"d\", \"M21 16v-2l-8-5V3.5c0-.83-.67-1.5-1.5-1.5S10 2.67 10 3.5V9l-8 5v2l8-2.5V19l-2 1.5V22l3.5-1 3.5 1v-1.5L13 19v-5.5l8 2.5z\"]],\n      template: function DashboardComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"header\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(5, \"svg\", 5);\n          i0.ɵɵelement(6, \"path\", 6);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(7, \"span\", 7);\n          i0.ɵɵtext(8, \"BLOCK TO BOOK\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(9, \"div\", 8)(10, \"span\", 9);\n          i0.ɵɵtext(11, \"223730 - Workfront for Demo\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"span\", 10);\n          i0.ɵɵtext(13, \"47988 - demo demo\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(14, \"div\", 11)(15, \"div\", 12);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(16, \"svg\", 13);\n          i0.ɵɵelement(17, \"path\", 14);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(18, \"span\");\n          i0.ɵɵtext(19, \"00962-79923031\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(20, \"div\", 12);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(21, \"svg\", 13);\n          i0.ɵɵelement(22, \"path\", 15);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(23, \"span\");\n          i0.ɵɵtext(24, \"<EMAIL>\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(25, \"div\", 12);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(26, \"svg\", 13);\n          i0.ɵɵelement(27, \"path\", 16)(28, \"path\", 17);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(29, \"span\");\n          i0.ɵɵtext(30, \"Bahrain: 8630 48 TND\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(31, \"div\", 12);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(32, \"svg\", 13);\n          i0.ɵɵelement(33, \"path\", 18);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(34, \"span\");\n          i0.ɵɵtext(35, \"UTC: +3468 52 TND\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(36, \"nav\", 19)(37, \"a\", 20);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(38, \"svg\", 21);\n          i0.ɵɵelement(39, \"path\", 22);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(40, \" Home \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(41, \"a\", 23);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(42, \"svg\", 21);\n          i0.ɵɵelement(43, \"path\", 24);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(44, \" News \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(45, \"a\", 23);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(46, \"svg\", 21);\n          i0.ɵɵelement(47, \"path\", 25)(48, \"path\", 26);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(49, \" Tools \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(50, \"a\", 23);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(51, \"svg\", 21);\n          i0.ɵɵelement(52, \"path\", 27);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(53, \" Languages \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(54, \"button\", 28);\n          i0.ɵɵlistener(\"click\", function DashboardComponent_Template_button_click_54_listener() {\n            return ctx.logout();\n          });\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(55, \"svg\", 21);\n          i0.ɵɵelement(56, \"path\", 29);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(57, \" Logout \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(58, \"main\", 30)(59, \"div\", 31)(60, \"aside\", 32)(61, \"div\", 33)(62, \"div\", 34);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(63, \"svg\", 35);\n          i0.ɵɵelement(64, \"path\", 36);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(65, \"span\");\n          i0.ɵɵtext(66, \"Booking Queue\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(67, \"div\", 34);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(68, \"svg\", 35);\n          i0.ɵɵelement(69, \"path\", 37);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(70, \"span\");\n          i0.ɵɵtext(71, \"Support\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(72, \"div\", 34);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(73, \"svg\", 35);\n          i0.ɵɵelement(74, \"path\", 38);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(75, \"span\");\n          i0.ɵɵtext(76, \"Helpdesk\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(77, \"div\", 34);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(78, \"svg\", 35);\n          i0.ɵɵelement(79, \"path\", 39);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(80, \"span\");\n          i0.ɵɵtext(81, \"Finance\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(82, \"div\", 34);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(83, \"svg\", 35);\n          i0.ɵɵelement(84, \"path\", 40);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(85, \"span\");\n          i0.ɵɵtext(86, \"Passengers\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(87, \"div\", 33)(88, \"div\", 34);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(89, \"svg\", 35);\n          i0.ɵɵelement(90, \"path\", 40);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(91, \"span\");\n          i0.ɵɵtext(92, \"Commissions\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(93, \"div\", 34);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(94, \"svg\", 35);\n          i0.ɵɵelement(95, \"path\", 41);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(96, \"span\");\n          i0.ɵɵtext(97, \"Staff Agent\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(98, \"div\", 34);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(99, \"svg\", 35);\n          i0.ɵɵelement(100, \"path\", 42);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(101, \"span\");\n          i0.ɵɵtext(102, \"Package\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(103, \"div\", 34);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(104, \"svg\", 35);\n          i0.ɵɵelement(105, \"path\", 43);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(106, \"span\");\n          i0.ɵɵtext(107, \"Flight Info\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(108, \"div\", 34);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(109, \"svg\", 35);\n          i0.ɵɵelement(110, \"path\", 44);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(111, \"span\");\n          i0.ɵɵtext(112, \"Agency Profile\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(113, \"div\", 34);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(114, \"svg\", 35);\n          i0.ɵɵelement(115, \"path\", 45);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(116, \"span\");\n          i0.ɵɵtext(117, \"Credit request\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(118, \"div\", 46)(119, \"div\", 47)(120, \"div\", 48)(121, \"div\", 49);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(122, \"svg\", 50);\n          i0.ɵɵelement(123, \"path\", 51);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(124, \"h3\");\n          i0.ɵɵtext(125, \"Flights\");\n          i0.ɵɵelementEnd()()()()()()();\n        }\n      },\n      styles: [\"\\n (()[_ngcontent-%COMP%]   =[_ngcontent-%COMP%] >  { // webpackBootstrap\\n\\n \\t\\\"use strict\\\";\\n\\n \\t\\n\\n \\t\\n\\n })()[_ngcontent-%COMP%]\\n;\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["DashboardComponent", "constructor", "authService", "router", "ngOnInit", "userInfo", "getUserInfo", "logout", "navigate", "i0", "ɵɵdirectiveInject", "i1", "AuthService", "i2", "Router", "selectors", "decls", "vars", "consts", "template", "DashboardComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵnamespaceSVG", "ɵɵelement", "ɵɵelementEnd", "ɵɵnamespaceHTML", "ɵɵtext", "ɵɵlistener", "DashboardComponent_Template_button_click_54_listener"], "sources": ["C:\\Users\\<USER>\\Desktop\\angular\\front\\src\\app\\components\\dashboard\\dashboard.component.ts", "C:\\Users\\<USER>\\Desktop\\angular\\front\\src\\app\\components\\dashboard\\dashboard.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { AuthService } from '../../services/auth.service';\n\n@Component({\n  selector: 'app-dashboard',\n  templateUrl: './dashboard.component.html',\n  styleUrls: ['./dashboard.component.css']\n})\nexport class DashboardComponent implements OnInit {\n  userInfo: any;\n\n  constructor(\n    private authService: AuthService,\n    private router: Router\n  ) {}\n\n  ngOnInit(): void {\n    this.userInfo = this.authService.getUserInfo();\n  }\n\n  logout(): void {\n    this.authService.logout();\n    this.router.navigate(['/login']);\n  }\n}\n", "<div class=\"dashboard-container\">\n  <!-- Header avec navigation -->\n  <header class=\"dashboard-header\">\n    <div class=\"header-content\">\n      <div class=\"logo-section\">\n        <div class=\"logo\">\n          <svg class=\"logo-icon\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n            <path d=\"M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5\"/>\n          </svg>\n          <span class=\"logo-text\">BLOCK TO BOOK</span>\n        </div>\n        <div class=\"demo-info\">\n          <span class=\"demo-id\">223730 - Workfront for Demo</span>\n          <span class=\"demo-details\">47988 - demo demo</span>\n        </div>\n      </div>\n\n      <div class=\"header-contact\">\n        <div class=\"contact-item\">\n          <svg class=\"contact-icon\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z\"/>\n          </svg>\n          <span>00962-79923031</span>\n        </div>\n        <div class=\"contact-item\">\n          <svg class=\"contact-icon\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\"/>\n          </svg>\n          <span>info&#64;blocktobook.com</span>\n        </div>\n        <div class=\"contact-item\">\n          <svg class=\"contact-icon\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\"/>\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M15 11a3 3 0 11-6 0 3 3 0 016 0z\"/>\n          </svg>\n          <span>Bahrain: 8630 48 TND</span>\n        </div>\n        <div class=\"contact-item\">\n          <svg class=\"contact-icon\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"/>\n          </svg>\n          <span>UTC: +3468 52 TND</span>\n        </div>\n      </div>\n\n      <nav class=\"header-nav\">\n        <a href=\"#\" class=\"nav-item active\">\n          <svg class=\"nav-icon\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6\"/>\n          </svg>\n          Home\n        </a>\n        <a href=\"#\" class=\"nav-item\">\n          <svg class=\"nav-icon\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z\"/>\n          </svg>\n          News\n        </a>\n        <a href=\"#\" class=\"nav-item\">\n          <svg class=\"nav-icon\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\"/>\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"/>\n          </svg>\n          Tools\n        </a>\n        <a href=\"#\" class=\"nav-item\">\n          <svg class=\"nav-icon\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129\"/>\n          </svg>\n          Languages\n        </a>\n        <button class=\"logout-btn\" (click)=\"logout()\">\n          <svg class=\"nav-icon\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1\"/>\n          </svg>\n          Logout\n        </button>\n      </nav>\n    </div>\n  </header>\n\n  <!-- Main Dashboard Content -->\n  <main class=\"dashboard-main\">\n    <div class=\"dashboard-grid\">\n      <!-- Left Sidebar -->\n      <aside class=\"sidebar\">\n        <div class=\"sidebar-section\">\n          <div class=\"sidebar-item\">\n            <svg class=\"sidebar-icon\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\"/>\n            </svg>\n            <span>Booking Queue</span>\n          </div>\n\n          <div class=\"sidebar-item\">\n            <svg class=\"sidebar-icon\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"/>\n            </svg>\n            <span>Support</span>\n          </div>\n\n          <div class=\"sidebar-item\">\n            <svg class=\"sidebar-icon\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-5 0a4 4 0 11-8 0 4 4 0 018 0z\"/>\n            </svg>\n            <span>Helpdesk</span>\n          </div>\n\n          <div class=\"sidebar-item\">\n            <svg class=\"sidebar-icon\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1\"/>\n            </svg>\n            <span>Finance</span>\n          </div>\n\n          <div class=\"sidebar-item\">\n            <svg class=\"sidebar-icon\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"/>\n            </svg>\n            <span>Passengers</span>\n          </div>\n        </div>\n\n        <div class=\"sidebar-section\">\n          <div class=\"sidebar-item\">\n            <svg class=\"sidebar-icon\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"/>\n            </svg>\n            <span>Commissions</span>\n          </div>\n\n          <div class=\"sidebar-item\">\n            <svg class=\"sidebar-icon\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\"/>\n            </svg>\n            <span>Staff Agent</span>\n          </div>\n\n          <div class=\"sidebar-item\">\n            <svg class=\"sidebar-icon\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\"/>\n            </svg>\n            <span>Package</span>\n          </div>\n\n          <div class=\"sidebar-item\">\n            <svg class=\"sidebar-icon\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"/>\n            </svg>\n            <span>Flight Info</span>\n          </div>\n\n          <div class=\"sidebar-item\">\n            <svg class=\"sidebar-icon\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\"/>\n            </svg>\n            <span>Agency Profile</span>\n          </div>\n\n          <div class=\"sidebar-item\">\n            <svg class=\"sidebar-icon\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2\"/>\n            </svg>\n            <span>Credit request</span>\n          </div>\n        </div>\n      </aside>\n\n      <!-- Main Content Area -->\n      <div class=\"main-content\">\n        <div class=\"content-grid\">\n          <!-- Flights Card -->\n          <div class=\"service-card flights-card\">\n            <div class=\"card-icon\">\n              <svg viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                <path d=\"M21 16v-2l-8-5V3.5c0-.83-.67-1.5-1.5-1.5S10 2.67 10 3.5V9l-8 5v2l8-2.5V19l-2 1.5V22l3.5-1 3.5 1v-1.5L13 19v-5.5l8 2.5z\"/>\n              </svg>\n            </div>\n            <h3>Flights</h3>\n          </div>\n        </div>\n      </div>\n    </div>\n  </main>\n</div>\n"], "mappings": ";;;AASA,OAAM,MAAOA,kBAAkB;EAG7BC,YACUC,WAAwB,EACxBC,MAAc;IADd,KAAAD,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;EACb;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,QAAQ,GAAG,IAAI,CAACH,WAAW,CAACI,WAAW,EAAE;EAChD;EAEAC,MAAMA,CAAA;IACJ,IAAI,CAACL,WAAW,CAACK,MAAM,EAAE;IACzB,IAAI,CAACJ,MAAM,CAACK,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;EAClC;;;uBAfWR,kBAAkB,EAAAS,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAlBd,kBAAkB;MAAAe,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,4BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCT/BZ,EAAA,CAAAc,cAAA,aAAiC;UAMvBd,EAAA,CAAAe,cAAA,EAA+D;UAA/Df,EAAA,CAAAc,cAAA,aAA+D;UAC7Dd,EAAA,CAAAgB,SAAA,cAAmE;UACrEhB,EAAA,CAAAiB,YAAA,EAAM;UACNjB,EAAA,CAAAkB,eAAA,EAAwB;UAAxBlB,EAAA,CAAAc,cAAA,cAAwB;UAAAd,EAAA,CAAAmB,MAAA,oBAAa;UAAAnB,EAAA,CAAAiB,YAAA,EAAO;UAE9CjB,EAAA,CAAAc,cAAA,aAAuB;UACCd,EAAA,CAAAmB,MAAA,mCAA2B;UAAAnB,EAAA,CAAAiB,YAAA,EAAO;UACxDjB,EAAA,CAAAc,cAAA,gBAA2B;UAAAd,EAAA,CAAAmB,MAAA,yBAAiB;UAAAnB,EAAA,CAAAiB,YAAA,EAAO;UAIvDjB,EAAA,CAAAc,cAAA,eAA4B;UAExBd,EAAA,CAAAe,cAAA,EAAgF;UAAhFf,EAAA,CAAAc,cAAA,eAAgF;UAC9Ed,EAAA,CAAAgB,SAAA,gBAAiS;UACnShB,EAAA,CAAAiB,YAAA,EAAM;UACNjB,EAAA,CAAAkB,eAAA,EAAM;UAANlB,EAAA,CAAAc,cAAA,YAAM;UAAAd,EAAA,CAAAmB,MAAA,sBAAc;UAAAnB,EAAA,CAAAiB,YAAA,EAAO;UAE7BjB,EAAA,CAAAc,cAAA,eAA0B;UACxBd,EAAA,CAAAe,cAAA,EAAgF;UAAhFf,EAAA,CAAAc,cAAA,eAAgF;UAC9Ed,EAAA,CAAAgB,SAAA,gBAAgL;UAClLhB,EAAA,CAAAiB,YAAA,EAAM;UACNjB,EAAA,CAAAkB,eAAA,EAAM;UAANlB,EAAA,CAAAc,cAAA,YAAM;UAAAd,EAAA,CAAAmB,MAAA,4BAAwB;UAAAnB,EAAA,CAAAiB,YAAA,EAAO;UAEvCjB,EAAA,CAAAc,cAAA,eAA0B;UACxBd,EAAA,CAAAe,cAAA,EAAgF;UAAhFf,EAAA,CAAAc,cAAA,eAAgF;UAC9Ed,EAAA,CAAAgB,SAAA,gBAA8J;UAEhKhB,EAAA,CAAAiB,YAAA,EAAM;UACNjB,EAAA,CAAAkB,eAAA,EAAM;UAANlB,EAAA,CAAAc,cAAA,YAAM;UAAAd,EAAA,CAAAmB,MAAA,4BAAoB;UAAAnB,EAAA,CAAAiB,YAAA,EAAO;UAEnCjB,EAAA,CAAAc,cAAA,eAA0B;UACxBd,EAAA,CAAAe,cAAA,EAAgF;UAAhFf,EAAA,CAAAc,cAAA,eAAgF;UAC9Ed,EAAA,CAAAgB,SAAA,gBAAuH;UACzHhB,EAAA,CAAAiB,YAAA,EAAM;UACNjB,EAAA,CAAAkB,eAAA,EAAM;UAANlB,EAAA,CAAAc,cAAA,YAAM;UAAAd,EAAA,CAAAmB,MAAA,yBAAiB;UAAAnB,EAAA,CAAAiB,YAAA,EAAO;UAIlCjB,EAAA,CAAAc,cAAA,eAAwB;UAEpBd,EAAA,CAAAe,cAAA,EAA4E;UAA5Ef,EAAA,CAAAc,cAAA,eAA4E;UAC1Ed,EAAA,CAAAgB,SAAA,gBAA4N;UAC9NhB,EAAA,CAAAiB,YAAA,EAAM;UACNjB,EAAA,CAAAmB,MAAA,cACF;UAAAnB,EAAA,CAAAiB,YAAA,EAAI;UACJjB,EAAA,CAAAkB,eAAA,EAA6B;UAA7BlB,EAAA,CAAAc,cAAA,aAA6B;UAC3Bd,EAAA,CAAAe,cAAA,EAA4E;UAA5Ef,EAAA,CAAAc,cAAA,eAA4E;UAC1Ed,EAAA,CAAAgB,SAAA,gBAAkN;UACpNhB,EAAA,CAAAiB,YAAA,EAAM;UACNjB,EAAA,CAAAmB,MAAA,cACF;UAAAnB,EAAA,CAAAiB,YAAA,EAAI;UACJjB,EAAA,CAAAkB,eAAA,EAA6B;UAA7BlB,EAAA,CAAAc,cAAA,aAA6B;UAC3Bd,EAAA,CAAAe,cAAA,EAA4E;UAA5Ef,EAAA,CAAAc,cAAA,eAA4E;UAC1Ed,EAAA,CAAAgB,SAAA,gBAA+iB;UAEjjBhB,EAAA,CAAAiB,YAAA,EAAM;UACNjB,EAAA,CAAAmB,MAAA,eACF;UAAAnB,EAAA,CAAAiB,YAAA,EAAI;UACJjB,EAAA,CAAAkB,eAAA,EAA6B;UAA7BlB,EAAA,CAAAc,cAAA,aAA6B;UAC3Bd,EAAA,CAAAe,cAAA,EAA4E;UAA5Ef,EAAA,CAAAc,cAAA,eAA4E;UAC1Ed,EAAA,CAAAgB,SAAA,gBAAiM;UACnMhB,EAAA,CAAAiB,YAAA,EAAM;UACNjB,EAAA,CAAAmB,MAAA,mBACF;UAAAnB,EAAA,CAAAiB,YAAA,EAAI;UACJjB,EAAA,CAAAkB,eAAA,EAA8C;UAA9ClB,EAAA,CAAAc,cAAA,kBAA8C;UAAnBd,EAAA,CAAAoB,UAAA,mBAAAC,qDAAA;YAAA,OAASR,GAAA,CAAAf,MAAA,EAAQ;UAAA,EAAC;UAC3CE,EAAA,CAAAe,cAAA,EAA4E;UAA5Ef,EAAA,CAAAc,cAAA,eAA4E;UAC1Ed,EAAA,CAAAgB,SAAA,gBAAqK;UACvKhB,EAAA,CAAAiB,YAAA,EAAM;UACNjB,EAAA,CAAAmB,MAAA,gBACF;UAAAnB,EAAA,CAAAiB,YAAA,EAAS;UAMfjB,EAAA,CAAAkB,eAAA,EAA6B;UAA7BlB,EAAA,CAAAc,cAAA,gBAA6B;UAMnBd,EAAA,CAAAe,cAAA,EAAgF;UAAhFf,EAAA,CAAAc,cAAA,eAAgF;UAC9Ed,EAAA,CAAAgB,SAAA,gBAAkK;UACpKhB,EAAA,CAAAiB,YAAA,EAAM;UACNjB,EAAA,CAAAkB,eAAA,EAAM;UAANlB,EAAA,CAAAc,cAAA,YAAM;UAAAd,EAAA,CAAAmB,MAAA,qBAAa;UAAAnB,EAAA,CAAAiB,YAAA,EAAO;UAG5BjB,EAAA,CAAAc,cAAA,eAA0B;UACxBd,EAAA,CAAAe,cAAA,EAAgF;UAAhFf,EAAA,CAAAc,cAAA,eAAgF;UAC9Ed,EAAA,CAAAgB,SAAA,gBAAqO;UACvOhB,EAAA,CAAAiB,YAAA,EAAM;UACNjB,EAAA,CAAAkB,eAAA,EAAM;UAANlB,EAAA,CAAAc,cAAA,YAAM;UAAAd,EAAA,CAAAmB,MAAA,eAAO;UAAAnB,EAAA,CAAAiB,YAAA,EAAO;UAGtBjB,EAAA,CAAAc,cAAA,eAA0B;UACxBd,EAAA,CAAAe,cAAA,EAAgF;UAAhFf,EAAA,CAAAc,cAAA,eAAgF;UAC9Ed,EAAA,CAAAgB,SAAA,gBAA4O;UAC9OhB,EAAA,CAAAiB,YAAA,EAAM;UACNjB,EAAA,CAAAkB,eAAA,EAAM;UAANlB,EAAA,CAAAc,cAAA,YAAM;UAAAd,EAAA,CAAAmB,MAAA,gBAAQ;UAAAnB,EAAA,CAAAiB,YAAA,EAAO;UAGvBjB,EAAA,CAAAc,cAAA,eAA0B;UACxBd,EAAA,CAAAe,cAAA,EAAgF;UAAhFf,EAAA,CAAAc,cAAA,eAAgF;UAC9Ed,EAAA,CAAAgB,SAAA,gBAAqN;UACvNhB,EAAA,CAAAiB,YAAA,EAAM;UACNjB,EAAA,CAAAkB,eAAA,EAAM;UAANlB,EAAA,CAAAc,cAAA,YAAM;UAAAd,EAAA,CAAAmB,MAAA,eAAO;UAAAnB,EAAA,CAAAiB,YAAA,EAAO;UAGtBjB,EAAA,CAAAc,cAAA,eAA0B;UACxBd,EAAA,CAAAe,cAAA,EAAgF;UAAhFf,EAAA,CAAAc,cAAA,eAAgF;UAC9Ed,EAAA,CAAAgB,SAAA,gBAAgM;UAClMhB,EAAA,CAAAiB,YAAA,EAAM;UACNjB,EAAA,CAAAkB,eAAA,EAAM;UAANlB,EAAA,CAAAc,cAAA,YAAM;UAAAd,EAAA,CAAAmB,MAAA,kBAAU;UAAAnB,EAAA,CAAAiB,YAAA,EAAO;UAI3BjB,EAAA,CAAAc,cAAA,eAA6B;UAEzBd,EAAA,CAAAe,cAAA,EAAgF;UAAhFf,EAAA,CAAAc,cAAA,eAAgF;UAC9Ed,EAAA,CAAAgB,SAAA,gBAAgM;UAClMhB,EAAA,CAAAiB,YAAA,EAAM;UACNjB,EAAA,CAAAkB,eAAA,EAAM;UAANlB,EAAA,CAAAc,cAAA,YAAM;UAAAd,EAAA,CAAAmB,MAAA,mBAAW;UAAAnB,EAAA,CAAAiB,YAAA,EAAO;UAG1BjB,EAAA,CAAAc,cAAA,eAA0B;UACxBd,EAAA,CAAAe,cAAA,EAAgF;UAAhFf,EAAA,CAAAc,cAAA,eAAgF;UAC9Ed,EAAA,CAAAgB,SAAA,gBAA+I;UACjJhB,EAAA,CAAAiB,YAAA,EAAM;UACNjB,EAAA,CAAAkB,eAAA,EAAM;UAANlB,EAAA,CAAAc,cAAA,YAAM;UAAAd,EAAA,CAAAmB,MAAA,mBAAW;UAAAnB,EAAA,CAAAiB,YAAA,EAAO;UAG1BjB,EAAA,CAAAc,cAAA,eAA0B;UACxBd,EAAA,CAAAe,cAAA,EAAgF;UAAhFf,EAAA,CAAAc,cAAA,eAAgF;UAC9Ed,EAAA,CAAAgB,SAAA,iBAAgR;UAClRhB,EAAA,CAAAiB,YAAA,EAAM;UACNjB,EAAA,CAAAkB,eAAA,EAAM;UAANlB,EAAA,CAAAc,cAAA,aAAM;UAAAd,EAAA,CAAAmB,MAAA,gBAAO;UAAAnB,EAAA,CAAAiB,YAAA,EAAO;UAGtBjB,EAAA,CAAAc,cAAA,gBAA0B;UACxBd,EAAA,CAAAe,cAAA,EAAgF;UAAhFf,EAAA,CAAAc,cAAA,gBAAgF;UAC9Ed,EAAA,CAAAgB,SAAA,iBAAqI;UACvIhB,EAAA,CAAAiB,YAAA,EAAM;UACNjB,EAAA,CAAAkB,eAAA,EAAM;UAANlB,EAAA,CAAAc,cAAA,aAAM;UAAAd,EAAA,CAAAmB,MAAA,oBAAW;UAAAnB,EAAA,CAAAiB,YAAA,EAAO;UAG1BjB,EAAA,CAAAc,cAAA,gBAA0B;UACxBd,EAAA,CAAAe,cAAA,EAAgF;UAAhFf,EAAA,CAAAc,cAAA,gBAAgF;UAC9Ed,EAAA,CAAAgB,SAAA,iBAAkV;UACpVhB,EAAA,CAAAiB,YAAA,EAAM;UACNjB,EAAA,CAAAkB,eAAA,EAAM;UAANlB,EAAA,CAAAc,cAAA,aAAM;UAAAd,EAAA,CAAAmB,MAAA,uBAAc;UAAAnB,EAAA,CAAAiB,YAAA,EAAO;UAG7BjB,EAAA,CAAAc,cAAA,gBAA0B;UACxBd,EAAA,CAAAe,cAAA,EAAgF;UAAhFf,EAAA,CAAAc,cAAA,gBAAgF;UAC9Ed,EAAA,CAAAgB,SAAA,iBAA0M;UAC5MhB,EAAA,CAAAiB,YAAA,EAAM;UACNjB,EAAA,CAAAkB,eAAA,EAAM;UAANlB,EAAA,CAAAc,cAAA,aAAM;UAAAd,EAAA,CAAAmB,MAAA,uBAAc;UAAAnB,EAAA,CAAAiB,YAAA,EAAO;UAMjCjB,EAAA,CAAAc,cAAA,gBAA0B;UAKlBd,EAAA,CAAAe,cAAA,EAA6C;UAA7Cf,EAAA,CAAAc,cAAA,gBAA6C;UAC3Cd,EAAA,CAAAgB,SAAA,iBAAkI;UACpIhB,EAAA,CAAAiB,YAAA,EAAM;UAERjB,EAAA,CAAAkB,eAAA,EAAI;UAAJlB,EAAA,CAAAc,cAAA,WAAI;UAAAd,EAAA,CAAAmB,MAAA,gBAAO;UAAAnB,EAAA,CAAAiB,YAAA,EAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}