# Guide d'Intégration - Composant SearchFlight

## Vue d'ensemble

Ce guide explique comment intégrer complètement le composant de recherche de vols avec votre backend Spring Boot existant qui utilise l'API Paximum.

## Architecture

```
Frontend Angular          Backend Spring Boot         API Paximum
┌─────────────────┐       ┌─────────────────────┐     ┌─────────────────┐
│ SearchFlight    │────→  │ FlightController    │────→│ ProductService  │
│ Component       │       │                     │     │ API             │
└─────────────────┘       └─────────────────────┘     └─────────────────┘
```

## 1. Backend - Contrôleur de Vols

### Créer le FlightController

Créez un nouveau contrôleur `FlightController.java` dans votre package `controllers` :

```java
package com.paximum.demo.controllers;

import org.springframework.web.bind.annotation.*;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;

import com.paximum.demo.models.OneWayRequest;
import com.paximum.demo.models.OneWayResponse;
import com.paximum.demo.models.RoundTripRequest;
import com.paximum.demo.models.RoundTripResponse;
import com.paximum.demo.models.MulticityRequest;
import com.paximum.demo.models.MulticityResponse;
import com.paximum.demo.services.ProductService;

import reactor.core.publisher.Mono;

@RestController
@RequestMapping("/api/flights")
@CrossOrigin(origins = "http://localhost:4200")
public class FlightController {
    
    private final ProductService productService;

    public FlightController(ProductService productService) {
        this.productService = productService;
    }

    @PostMapping("/search/oneway")
    public Mono<ResponseEntity<OneWayResponse>> searchOneWayFlights(
            @RequestBody OneWayRequest oneWayRequest,
            Authentication authentication) {
        
        String token = extractTokenFromAuthentication(authentication);
        
        return productService.searchOneWayFlights(oneWayRequest, token)
            .map(response -> ResponseEntity.ok(response))
            .onErrorReturn(ResponseEntity.internalServerError().build());
    }

    @PostMapping("/search/roundtrip")
    public Mono<ResponseEntity<RoundTripResponse>> searchRoundTripFlights(
            @RequestBody RoundTripRequest roundTripRequest,
            Authentication authentication) {
        
        String token = extractTokenFromAuthentication(authentication);
        
        return productService.searchRoundTripFlights(roundTripRequest, token)
            .map(response -> ResponseEntity.ok(response))
            .onErrorReturn(ResponseEntity.internalServerError().build());
    }

    @PostMapping("/search/multicity")
    public Mono<ResponseEntity<MulticityResponse>> searchMulticityFlights(
            @RequestBody MulticityRequest multicityRequest,
            Authentication authentication) {
        
        String token = extractTokenFromAuthentication(authentication);
        
        return productService.searchMulticityFlights(multicityRequest, token)
            .map(response -> ResponseEntity.ok(response))
            .onErrorReturn(ResponseEntity.internalServerError().build());
    }

    private String extractTokenFromAuthentication(Authentication authentication) {
        // Adaptez selon votre implémentation de sécurité
        // Voir les exemples ci-dessous
    }
}
```

### Gestion de l'Authentification

Selon votre implémentation de sécurité, adaptez la méthode `extractTokenFromAuthentication` :

#### Option 1 : Token dans les détails
```java
private String extractTokenFromAuthentication(Authentication authentication) {
    if (authentication != null && authentication.getDetails() instanceof String) {
        return (String) authentication.getDetails();
    }
    throw new RuntimeException("Token non trouvé");
}
```

#### Option 2 : JWT avec Spring Security
```java
@Autowired
private JwtTokenProvider jwtTokenProvider;

private String extractTokenFromAuthentication(Authentication authentication) {
    return jwtTokenProvider.getTokenFromAuthentication(authentication);
}
```

#### Option 3 : Token depuis le SecurityContext
```java
private String extractTokenFromAuthentication(Authentication authentication) {
    return SecurityContextHolder.getContext()
        .getAuthentication()
        .getCredentials()
        .toString();
}
```

## 2. Configuration CORS

Assurez-vous que votre configuration CORS permet les requêtes depuis Angular :

```java
@Configuration
@EnableWebMvc
public class WebConfig implements WebMvcConfigurer {

    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/api/**")
                .allowedOrigins("http://localhost:4200")
                .allowedMethods("GET", "POST", "PUT", "DELETE", "OPTIONS")
                .allowedHeaders("*")
                .allowCredentials(true);
    }
}
```

## 3. Frontend - Configuration

### Variables d'Environnement

Vérifiez que vos variables d'environnement pointent vers les bons endpoints :

```typescript
// src/environments/environment.ts
export const environment = {
  production: false,
  apiUrl: 'http://localhost:8080',
  authEndpoint: '/auth/login'
};
```

### Service FlightService

Le service est déjà configuré pour utiliser les endpoints corrects :

```typescript
// Les endpoints utilisés :
POST /api/flights/search/oneway
POST /api/flights/search/roundtrip  
POST /api/flights/search/multicity
```

## 4. Navigation et Routing

### Depuis le Dashboard

Le dashboard a déjà un lien vers la recherche de vols :

```typescript
// dashboard.component.ts
navigateToFlightSearch(): void {
  this.router.navigate(['/search-flights']);
}
```

### Route Protégée

La route est protégée par l'AuthGuard :

```typescript
// app-routing.module.ts
{
  path: 'search-flights',
  component: SearchFlightComponent,
  canActivate: [AuthGuard]
}
```

## 5. Test de l'Intégration

### 1. Démarrer le Backend
```bash
./mvnw spring-boot:run
```

### 2. Démarrer le Frontend
```bash
cd front
ng serve
```

### 3. Tester la Connectivité

1. Connectez-vous via `/login`
2. Accédez au dashboard `/dashboard`
3. Cliquez sur la carte "Flights"
4. Remplissez le formulaire de recherche
5. Soumettez la recherche

### 4. Vérifier les Logs

Backend :
- Vérifiez que les requêtes arrivent sur `/api/flights/search/*`
- Vérifiez que le token est correctement extrait
- Vérifiez les appels vers l'API Paximum

Frontend :
- Ouvrez les DevTools (F12)
- Onglet Network : vérifiez les requêtes HTTP
- Onglet Console : vérifiez les logs et erreurs

## 6. Gestion des Erreurs

### Backend

Ajoutez une gestion d'erreurs plus robuste :

```java
@PostMapping("/search/oneway")
public Mono<ResponseEntity<OneWayResponse>> searchOneWayFlights(
        @RequestBody OneWayRequest oneWayRequest,
        Authentication authentication) {
    
    try {
        String token = extractTokenFromAuthentication(authentication);
        
        return productService.searchOneWayFlights(oneWayRequest, token)
            .map(response -> ResponseEntity.ok(response))
            .onErrorResume(error -> {
                log.error("Erreur lors de la recherche de vols: ", error);
                return Mono.just(ResponseEntity.internalServerError().build());
            });
    } catch (Exception e) {
        log.error("Erreur d'authentification: ", e);
        return Mono.just(ResponseEntity.status(HttpStatus.UNAUTHORIZED).build());
    }
}
```

### Frontend

Le service FlightService gère déjà les erreurs HTTP courantes.

## 7. Améliorations Optionnelles

### Validation des Données

Ajoutez des validations côté backend :

```java
@PostMapping("/search/oneway")
public Mono<ResponseEntity<OneWayResponse>> searchOneWayFlights(
        @Valid @RequestBody OneWayRequest oneWayRequest,
        Authentication authentication) {
    // ...
}
```

### Logs et Monitoring

Ajoutez des logs pour le monitoring :

```java
@PostMapping("/search/oneway")
public Mono<ResponseEntity<OneWayResponse>> searchOneWayFlights(
        @RequestBody OneWayRequest oneWayRequest,
        Authentication authentication) {
    
    log.info("Recherche de vols aller simple: {} -> {}", 
        oneWayRequest.getDepartureLocations(), 
        oneWayRequest.getArrivalLocations());
    
    // ...
}
```

### Cache (Optionnel)

Pour améliorer les performances, considérez l'ajout d'un cache :

```java
@Cacheable(value = "flightSearches", key = "#oneWayRequest.hashCode()")
@PostMapping("/search/oneway")
public Mono<ResponseEntity<OneWayResponse>> searchOneWayFlights(
        @RequestBody OneWayRequest oneWayRequest,
        Authentication authentication) {
    // ...
}
```

## 8. Déploiement

### Production

Pour la production, mettez à jour :

1. **Frontend** : `environment.prod.ts`
```typescript
export const environment = {
  production: true,
  apiUrl: 'https://your-production-api.com',
  authEndpoint: '/auth/login'
};
```

2. **Backend** : Configuration CORS pour le domaine de production

3. **HTTPS** : Assurez-vous que les deux services utilisent HTTPS

## Résumé

L'intégration est maintenant complète :

✅ **Frontend** : Composant SearchFlight avec design fidèle à l'image
✅ **Backend** : FlightController utilisant votre ProductService existant  
✅ **API** : Communication avec l'API Paximum via votre client existant
✅ **Sécurité** : Authentification JWT intégrée
✅ **Navigation** : Lien depuis le dashboard
✅ **Tests** : Tests unitaires inclus

Le composant est prêt à être utilisé et peut être étendu selon vos besoins spécifiques !
