{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport { FlightClass } from '../../models/flight-search.interface';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../services/flight.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/common\";\nfunction SearchFlightComponent_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 72);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 73);\n    i0.ɵɵelement(2, \"path\", 74);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 75);\n    i0.ɵɵlistener(\"click\", function SearchFlightComponent_div_18_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r12 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r12.clearError());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(6, \"svg\", 76);\n    i0.ɵɵelement(7, \"path\", 77);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r0.errorMessage);\n  }\n}\nfunction SearchFlightComponent_div_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(0, \"div\", 78);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getErrorMessage(\"departureLocation\"), \" \");\n  }\n}\nfunction SearchFlightComponent_div_53_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(0, \"div\", 78);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.getErrorMessage(\"arrivalLocation\"), \" \");\n  }\n}\nfunction SearchFlightComponent_div_59_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 78);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.getErrorMessage(\"departureDate\"), \" \");\n  }\n}\nfunction SearchFlightComponent_div_60_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 78);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r14 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r14.getErrorMessage(\"returnDate\"), \" \");\n  }\n}\nfunction SearchFlightComponent_div_60_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35)(1, \"label\", 36);\n    i0.ɵɵtext(2, \"To\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"input\", 79);\n    i0.ɵɵtemplate(4, SearchFlightComponent_div_60_div_4_Template, 2, 1, \"div\", 28);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"error\", ctx_r4.hasError(\"returnDate\", \"required\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.formControls[\"returnDate\"].touched && ctx_r4.formControls[\"returnDate\"].errors);\n  }\n}\nfunction SearchFlightComponent_option_71_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 80);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const count_r15 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", count_r15);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(count_r15);\n  }\n}\nfunction SearchFlightComponent_option_76_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 80);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const count_r16 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", count_r16);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(count_r16);\n  }\n}\nfunction SearchFlightComponent_option_81_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 80);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const count_r17 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", count_r17);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(count_r17);\n  }\n}\nfunction SearchFlightComponent_option_86_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 80);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const flightClass_r18 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", flightClass_r18.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(flightClass_r18.label);\n  }\n}\nfunction SearchFlightComponent_option_93_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 80);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const airline_r19 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", airline_r19);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(airline_r19);\n  }\n}\nfunction SearchFlightComponent_span_113_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"SEARCH NOW\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SearchFlightComponent_span_114_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 81);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 82);\n    i0.ɵɵelement(2, \"circle\", 83)(3, \"path\", 84);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" Searching... \");\n    i0.ɵɵelementEnd();\n  }\n}\nexport class SearchFlightComponent {\n  constructor(formBuilder, flightService, router) {\n    this.formBuilder = formBuilder;\n    this.flightService = flightService;\n    this.router = router;\n    this.isLoading = false;\n    this.errorMessage = '';\n    // Options pour les sélecteurs\n    this.flightClasses = [{\n      value: FlightClass.ECONOMY,\n      label: 'Economy'\n    }, {\n      value: FlightClass.PREMIUM_ECONOMY,\n      label: 'Premium Economy'\n    }, {\n      value: FlightClass.BUSINESS,\n      label: 'Business'\n    }, {\n      value: FlightClass.FIRST,\n      label: 'First Class'\n    }];\n    // Données pour les passagers\n    this.passengerCounts = {\n      adults: Array.from({\n        length: 9\n      }, (_, i) => i + 1),\n      children: Array.from({\n        length: 8\n      }, (_, i) => i),\n      infants: Array.from({\n        length: 4\n      }, (_, i) => i)\n    };\n    // Compagnies aériennes préférées (exemple)\n    this.preferredAirlines = ['Preferred Airline', 'Turkish Airlines', 'Emirates', 'Qatar Airways', 'Lufthansa', 'Air France', 'British Airways'];\n  }\n  ngOnInit() {\n    this.initializeForm();\n  }\n  /**\n   * Initialise le formulaire de recherche\n   */\n  initializeForm() {\n    this.searchForm = this.formBuilder.group({\n      tripType: ['oneWay', Validators.required],\n      departureLocation: ['', [Validators.required, Validators.minLength(3)]],\n      arrivalLocation: ['', [Validators.required, Validators.minLength(3)]],\n      departureDate: ['', Validators.required],\n      returnDate: [''],\n      adults: [1, [Validators.required, Validators.min(1)]],\n      children: [0, [Validators.required, Validators.min(0)]],\n      infants: [0, [Validators.required, Validators.min(0)]],\n      flightClass: [FlightClass.ECONOMY, Validators.required],\n      directFlightsOnly: [false],\n      preferredAirline: ['']\n    });\n    // Validation conditionnelle pour la date de retour\n    this.searchForm.get('tripType')?.valueChanges.subscribe(tripType => {\n      const returnDateControl = this.searchForm.get('returnDate');\n      if (tripType === 'roundTrip') {\n        returnDateControl?.setValidators([Validators.required]);\n      } else {\n        returnDateControl?.clearValidators();\n      }\n      returnDateControl?.updateValueAndValidity();\n    });\n  }\n  /**\n   * Getter pour accéder facilement aux contrôles du formulaire\n   */\n  get formControls() {\n    return this.searchForm.controls;\n  }\n  /**\n   * Échange les aéroports de départ et d'arrivée\n   */\n  swapAirports() {\n    const departure = this.searchForm.get('departureLocation')?.value;\n    const arrival = this.searchForm.get('arrivalLocation')?.value;\n    this.searchForm.patchValue({\n      departureLocation: arrival,\n      arrivalLocation: departure\n    });\n  }\n  /**\n   * Obtient le nombre total de passagers\n   */\n  getTotalPassengers() {\n    const adults = this.searchForm.get('adults')?.value || 0;\n    const children = this.searchForm.get('children')?.value || 0;\n    const infants = this.searchForm.get('infants')?.value || 0;\n    return adults + children + infants;\n  }\n  /**\n   * Obtient le texte d'affichage pour les passagers\n   */\n  getPassengerText() {\n    const adults = this.searchForm.get('adults')?.value || 0;\n    const children = this.searchForm.get('children')?.value || 0;\n    const infants = this.searchForm.get('infants')?.value || 0;\n    let text = `${adults} Adult${adults > 1 ? 's' : ''}`;\n    if (children > 0) text += `, ${children} Child${children > 1 ? 'ren' : ''}`;\n    if (infants > 0) text += `, ${infants} Infant${infants > 1 ? 's' : ''}`;\n    return text;\n  }\n  /**\n   * Obtient le nombre de jours entre les dates\n   */\n  getDaysBetweenDates() {\n    const departureDate = this.searchForm.get('departureDate')?.value;\n    const returnDate = this.searchForm.get('returnDate')?.value;\n    if (!departureDate || !returnDate) return '';\n    const start = new Date(departureDate);\n    const end = new Date(returnDate);\n    const diffTime = Math.abs(end.getTime() - start.getTime());\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n    return `+/- ${diffDays} Days`;\n  }\n  /**\n   * Soumet le formulaire de recherche\n   */\n  onSubmit() {\n    if (this.searchForm.valid && !this.isLoading) {\n      this.isLoading = true;\n      this.errorMessage = '';\n      const searchData = {\n        tripType: this.searchForm.value.tripType,\n        departureLocation: this.searchForm.value.departureLocation.trim(),\n        arrivalLocation: this.searchForm.value.arrivalLocation.trim(),\n        departureDate: this.searchForm.value.departureDate,\n        returnDate: this.searchForm.value.returnDate,\n        passengers: {\n          adults: this.searchForm.value.adults,\n          children: this.searchForm.value.children,\n          infants: this.searchForm.value.infants\n        },\n        flightClass: this.searchForm.value.flightClass,\n        directFlightsOnly: this.searchForm.value.directFlightsOnly,\n        preferredAirline: this.searchForm.value.preferredAirline\n      };\n      // Appel du service selon le type de voyage\n      let searchObservable;\n      switch (searchData.tripType) {\n        case 'oneWay':\n          searchObservable = this.flightService.searchOneWayFlights(searchData);\n          break;\n        case 'roundTrip':\n          searchObservable = this.flightService.searchRoundTripFlights(searchData);\n          break;\n        case 'multiCity':\n          searchObservable = this.flightService.searchMulticityFlights(searchData);\n          break;\n        default:\n          this.isLoading = false;\n          this.errorMessage = 'Type de voyage non valide';\n          return;\n      }\n      searchObservable.subscribe({\n        next: response => {\n          this.isLoading = false;\n          if (response.header.success) {\n            console.log('Recherche réussie:', response);\n            // Rediriger vers la page de résultats ou traiter les résultats\n            // this.router.navigate(['/flight-results'], { state: { results: response } });\n          } else {\n            this.handleApiError(response);\n          }\n        },\n        error: error => {\n          this.isLoading = false;\n          this.errorMessage = error.message || 'Une erreur est survenue lors de la recherche';\n          console.error('Erreur de recherche:', error);\n        }\n      });\n    } else {\n      this.markFormGroupTouched();\n    }\n  }\n  /**\n   * Gère les erreurs retournées par l'API\n   */\n  handleApiError(response) {\n    if (response.header.messages && response.header.messages.length > 0) {\n      this.errorMessage = response.header.messages[0].message;\n    } else {\n      this.errorMessage = 'Aucun vol trouvé pour ces critères';\n    }\n  }\n  /**\n   * Marque tous les champs du formulaire comme touchés\n   */\n  markFormGroupTouched() {\n    Object.keys(this.searchForm.controls).forEach(key => {\n      const control = this.searchForm.get(key);\n      control?.markAsTouched();\n    });\n  }\n  /**\n   * Vérifie si un champ a une erreur et a été touché\n   */\n  hasError(fieldName, errorType) {\n    const field = this.searchForm.get(fieldName);\n    return !!(field?.hasError(errorType) && field?.touched);\n  }\n  /**\n   * Récupère le message d'erreur pour un champ\n   */\n  getErrorMessage(fieldName) {\n    const field = this.searchForm.get(fieldName);\n    if (field?.hasError('required')) {\n      return `Ce champ est requis`;\n    }\n    if (field?.hasError('minlength')) {\n      const requiredLength = field.errors?.['minlength']?.requiredLength;\n      return `Minimum ${requiredLength} caractères requis`;\n    }\n    if (field?.hasError('min')) {\n      const min = field.errors?.['min']?.min;\n      return `La valeur minimum est ${min}`;\n    }\n    return '';\n  }\n  /**\n   * Nettoie le message d'erreur\n   */\n  clearError() {\n    this.errorMessage = '';\n  }\n  static {\n    this.ɵfac = function SearchFlightComponent_Factory(t) {\n      return new (t || SearchFlightComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.FlightService), i0.ɵɵdirectiveInject(i3.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SearchFlightComponent,\n      selectors: [[\"app-search-flight\"]],\n      decls: 119,\n      vars: 29,\n      consts: [[1, \"search-flight-container\"], [1, \"search-header\"], [1, \"search-title-section\"], [1, \"search-icon\"], [\"viewBox\", \"0 0 24 24\", \"fill\", \"currentColor\"], [\"cx\", \"12\", \"cy\", \"12\", \"r\", \"10\"], [1, \"search-title-content\"], [1, \"search-title\"], [1, \"search-subtitle\"], [1, \"latest-searches\"], [1, \"latest-title\"], [1, \"latest-subtitle\"], [1, \"search-content\"], [1, \"search-form-container\"], [\"class\", \"error-message\", \"role\", \"alert\", 4, \"ngIf\"], [\"novalidate\", \"\", 1, \"search-form\", 3, \"formGroup\", \"ngSubmit\"], [1, \"trip-type-tabs\"], [\"type\", \"button\", 1, \"tab-button\", 3, \"click\"], [1, \"location-row\"], [1, \"location-field\"], [1, \"location-label\"], [1, \"location-input-container\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"location-icon\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M12 19l9 2-9-18-9 18 9-2zm0 0v-8\"], [\"type\", \"text\", \"formControlName\", \"departureLocation\", \"placeholder\", \"IST - Istanbul Airport\", 1, \"location-input\"], [\"type\", \"button\", 1, \"location-button\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M19 9l-7 7-7-7\"], [\"class\", \"field-error\", 4, \"ngIf\"], [\"type\", \"button\", 1, \"swap-button\", 3, \"click\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M15 11a3 3 0 11-6 0 3 3 0 016 0z\"], [\"type\", \"text\", \"formControlName\", \"arrivalLocation\", \"placeholder\", \"TUN - Carthage Arpt\", 1, \"location-input\"], [1, \"date-row\"], [1, \"date-field\"], [1, \"date-label\"], [\"type\", \"date\", \"formControlName\", \"departureDate\", 1, \"date-input\"], [\"class\", \"date-field\", 4, \"ngIf\"], [1, \"passenger-class-row\"], [1, \"passenger-field\"], [1, \"passenger-label\"], [1, \"passenger-display\"], [1, \"passenger-icons\"], [1, \"passenger-group\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"passenger-icon\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\"], [\"formControlName\", \"adults\", 1, \"passenger-select\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\"], [\"formControlName\", \"children\", 1, \"passenger-select\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z\"], [\"formControlName\", \"infants\", 1, \"passenger-select\"], [1, \"class-group\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"class-icon\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"], [\"formControlName\", \"flightClass\", 1, \"class-select\"], [1, \"airline-field\"], [1, \"airline-label\"], [\"formControlName\", \"preferredAirline\", 1, \"airline-select\"], [\"value\", \"\"], [1, \"options-row\"], [1, \"option-group\"], [1, \"option-label\"], [1, \"option-select\"], [1, \"calendar-display\"], [\"type\", \"submit\", 1, \"search-button\", 3, \"disabled\"], [4, \"ngIf\"], [\"class\", \"loading-content\", 4, \"ngIf\"], [1, \"latest-searches-sidebar\"], [1, \"latest-searches-content\"], [1, \"no-searches\"], [\"role\", \"alert\", 1, \"error-message\"], [\"fill\", \"currentColor\", \"viewBox\", \"0 0 20 20\", 1, \"error-icon\"], [\"fill-rule\", \"evenodd\", \"d\", \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z\", \"clip-rule\", \"evenodd\"], [\"type\", \"button\", 1, \"error-close\", 3, \"click\"], [\"fill\", \"currentColor\", \"viewBox\", \"0 0 20 20\"], [\"fill-rule\", \"evenodd\", \"d\", \"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z\", \"clip-rule\", \"evenodd\"], [1, \"field-error\"], [\"type\", \"date\", \"formControlName\", \"returnDate\", 1, \"date-input\"], [3, \"value\"], [1, \"loading-content\"], [\"viewBox\", \"0 0 24 24\", 1, \"loading-spinner\"], [\"cx\", \"12\", \"cy\", \"12\", \"r\", \"10\", \"stroke\", \"currentColor\", \"stroke-width\", \"4\", \"fill\", \"none\", \"opacity\", \"0.25\"], [\"fill\", \"currentColor\", \"d\", \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\", \"opacity\", \"0.75\"]],\n      template: function SearchFlightComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(4, \"svg\", 4);\n          i0.ɵɵelement(5, \"circle\", 5);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(6, \"div\", 6)(7, \"h1\", 7);\n          i0.ɵɵtext(8, \"Search and Book Flights\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"p\", 8);\n          i0.ɵɵtext(10, \"We're bringing you a new level of comfort\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(11, \"div\", 9)(12, \"h2\", 10);\n          i0.ɵɵtext(13, \"Latest Searches\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"p\", 11);\n          i0.ɵɵtext(15, \"We're bringing you a new level of comfort\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(16, \"div\", 12)(17, \"div\", 13);\n          i0.ɵɵtemplate(18, SearchFlightComponent_div_18_Template, 8, 1, \"div\", 14);\n          i0.ɵɵelementStart(19, \"form\", 15);\n          i0.ɵɵlistener(\"ngSubmit\", function SearchFlightComponent_Template_form_ngSubmit_19_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵelementStart(20, \"div\", 16)(21, \"button\", 17);\n          i0.ɵɵlistener(\"click\", function SearchFlightComponent_Template_button_click_21_listener() {\n            return ctx.searchForm.patchValue({\n              tripType: \"oneWay\"\n            });\n          });\n          i0.ɵɵtext(22, \" One way \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(23, \"button\", 17);\n          i0.ɵɵlistener(\"click\", function SearchFlightComponent_Template_button_click_23_listener() {\n            return ctx.searchForm.patchValue({\n              tripType: \"roundTrip\"\n            });\n          });\n          i0.ɵɵtext(24, \" Round Trip \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(25, \"button\", 17);\n          i0.ɵɵlistener(\"click\", function SearchFlightComponent_Template_button_click_25_listener() {\n            return ctx.searchForm.patchValue({\n              tripType: \"multiCity\"\n            });\n          });\n          i0.ɵɵtext(26, \" Multi-City/Stop-Overs \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(27, \"div\", 18)(28, \"div\", 19)(29, \"label\", 20);\n          i0.ɵɵtext(30, \"From\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(31, \"div\", 21);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(32, \"svg\", 22);\n          i0.ɵɵelement(33, \"path\", 23);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelement(34, \"input\", 24);\n          i0.ɵɵelementStart(35, \"button\", 25);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(36, \"svg\", 26);\n          i0.ɵɵelement(37, \"path\", 27);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(38, SearchFlightComponent_div_38_Template, 2, 1, \"div\", 28);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(39, \"button\", 29);\n          i0.ɵɵlistener(\"click\", function SearchFlightComponent_Template_button_click_39_listener() {\n            return ctx.swapAirports();\n          });\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(40, \"svg\", 26);\n          i0.ɵɵelement(41, \"path\", 30);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(42, \"div\", 19)(43, \"label\", 20);\n          i0.ɵɵtext(44, \"To\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(45, \"div\", 21);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(46, \"svg\", 22);\n          i0.ɵɵelement(47, \"path\", 31)(48, \"path\", 32);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelement(49, \"input\", 33);\n          i0.ɵɵelementStart(50, \"button\", 25);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(51, \"svg\", 26);\n          i0.ɵɵelement(52, \"path\", 27);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(53, SearchFlightComponent_div_53_Template, 2, 1, \"div\", 28);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(54, \"div\", 34)(55, \"div\", 35)(56, \"label\", 36);\n          i0.ɵɵtext(57, \"From\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(58, \"input\", 37);\n          i0.ɵɵtemplate(59, SearchFlightComponent_div_59_Template, 2, 1, \"div\", 28);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(60, SearchFlightComponent_div_60_Template, 5, 3, \"div\", 38);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(61, \"div\", 39)(62, \"div\", 40)(63, \"label\", 41);\n          i0.ɵɵtext(64, \"Passenger & Class of travel\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(65, \"div\", 42)(66, \"div\", 43)(67, \"div\", 44);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(68, \"svg\", 45);\n          i0.ɵɵelement(69, \"path\", 46);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(70, \"select\", 47);\n          i0.ɵɵtemplate(71, SearchFlightComponent_option_71_Template, 2, 2, \"option\", 48);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(72, \"div\", 44);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(73, \"svg\", 45);\n          i0.ɵɵelement(74, \"path\", 49);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(75, \"select\", 50);\n          i0.ɵɵtemplate(76, SearchFlightComponent_option_76_Template, 2, 2, \"option\", 48);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(77, \"div\", 44);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(78, \"svg\", 45);\n          i0.ɵɵelement(79, \"path\", 51);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(80, \"select\", 52);\n          i0.ɵɵtemplate(81, SearchFlightComponent_option_81_Template, 2, 2, \"option\", 48);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(82, \"div\", 53);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(83, \"svg\", 54);\n          i0.ɵɵelement(84, \"path\", 55);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(85, \"select\", 56);\n          i0.ɵɵtemplate(86, SearchFlightComponent_option_86_Template, 2, 2, \"option\", 48);\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(87, \"div\", 57)(88, \"label\", 58);\n          i0.ɵɵtext(89, \"Preferred Airline\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(90, \"select\", 59)(91, \"option\", 60);\n          i0.ɵɵtext(92, \"Preferred Airline\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(93, SearchFlightComponent_option_93_Template, 2, 2, \"option\", 48);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(94, \"div\", 61)(95, \"div\", 62)(96, \"label\", 63);\n          i0.ɵɵtext(97, \"Refundable fares\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(98, \"select\", 64)(99, \"option\");\n          i0.ɵɵtext(100, \"--All--\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(101, \"div\", 62)(102, \"label\", 63);\n          i0.ɵɵtext(103, \"Baggage\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(104, \"select\", 64)(105, \"option\");\n          i0.ɵɵtext(106, \"--All--\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(107, \"div\", 62)(108, \"label\", 63);\n          i0.ɵɵtext(109, \"Calendar\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(110, \"div\", 65);\n          i0.ɵɵtext(111);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(112, \"button\", 66);\n          i0.ɵɵtemplate(113, SearchFlightComponent_span_113_Template, 2, 0, \"span\", 67);\n          i0.ɵɵtemplate(114, SearchFlightComponent_span_114_Template, 5, 0, \"span\", 68);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(115, \"div\", 69)(116, \"div\", 70)(117, \"p\", 71);\n          i0.ɵɵtext(118, \"No recent searches\");\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(18);\n          i0.ɵɵproperty(\"ngIf\", ctx.errorMessage);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"formGroup\", ctx.searchForm);\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"active\", ctx.formControls[\"tripType\"].value === \"oneWay\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"active\", ctx.formControls[\"tripType\"].value === \"roundTrip\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"active\", ctx.formControls[\"tripType\"].value === \"multiCity\");\n          i0.ɵɵadvance(9);\n          i0.ɵɵclassProp(\"error\", ctx.hasError(\"departureLocation\", \"required\") || ctx.hasError(\"departureLocation\", \"minlength\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.formControls[\"departureLocation\"].touched && ctx.formControls[\"departureLocation\"].errors);\n          i0.ɵɵadvance(11);\n          i0.ɵɵclassProp(\"error\", ctx.hasError(\"arrivalLocation\", \"required\") || ctx.hasError(\"arrivalLocation\", \"minlength\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.formControls[\"arrivalLocation\"].touched && ctx.formControls[\"arrivalLocation\"].errors);\n          i0.ɵɵadvance(5);\n          i0.ɵɵclassProp(\"error\", ctx.hasError(\"departureDate\", \"required\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.formControls[\"departureDate\"].touched && ctx.formControls[\"departureDate\"].errors);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.formControls[\"tripType\"].value === \"roundTrip\");\n          i0.ɵɵadvance(11);\n          i0.ɵɵproperty(\"ngForOf\", ctx.passengerCounts.adults);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngForOf\", ctx.passengerCounts.children);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngForOf\", ctx.passengerCounts.infants);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngForOf\", ctx.flightClasses);\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngForOf\", ctx.preferredAirlines);\n          i0.ɵɵadvance(18);\n          i0.ɵɵtextInterpolate(ctx.getDaysBetweenDates() || \"+/- 3 Days\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵclassProp(\"loading\", ctx.isLoading);\n          i0.ɵɵproperty(\"disabled\", ctx.isLoading || ctx.searchForm.invalid);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n        }\n      },\n      dependencies: [i4.NgForOf, i4.NgIf, i1.ɵNgNoValidate, i1.NgSelectOption, i1.ɵNgSelectMultipleOption, i1.DefaultValueAccessor, i1.SelectControlValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName],\n      styles: [\"/* Styles pour le composant de recherche de vols */\\n.search-flight-container {\\n  min-height: 100vh;\\n  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);\\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;\\n  padding: 2rem;\\n}\\n\\n/* Header Section */\\n.search-header {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: flex-start;\\n  margin-bottom: 2rem;\\n  gap: 2rem;\\n  background: #ffffff;\\n  padding: 1.5rem 2rem;\\n  border-radius: 8px;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n}\\n\\n.search-title-section {\\n  display: flex;\\n  align-items: center;\\n  gap: 1rem;\\n}\\n\\n.search-icon {\\n  width: 3rem;\\n  height: 3rem;\\n  background: #4a6fa5;\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  color: #ffffff;\\n  box-shadow: 0 2px 8px rgba(74, 111, 165, 0.3);\\n}\\n\\n.search-icon svg {\\n  width: 1.5rem;\\n  height: 1.5rem;\\n}\\n\\n.search-title-content h1 {\\n  font-size: 1.75rem;\\n  font-weight: 600;\\n  color: #343a40;\\n  margin: 0 0 0.25rem 0;\\n}\\n\\n.search-title-content p {\\n  font-size: 0.875rem;\\n  color: #6c757d;\\n  margin: 0;\\n}\\n\\n.latest-searches {\\n  text-align: right;\\n}\\n\\n.latest-title {\\n  font-size: 1.25rem;\\n  font-weight: 600;\\n  color: #343a40;\\n  margin: 0 0 0.25rem 0;\\n}\\n\\n.latest-subtitle {\\n  font-size: 0.875rem;\\n  color: #6c757d;\\n  margin: 0;\\n}\\n\\n/* Main Content */\\n.search-content {\\n  display: grid;\\n  grid-template-columns: 2fr 1fr;\\n  gap: 2rem;\\n  max-width: 1400px;\\n  margin: 0 auto;\\n}\\n\\n/* Form Container */\\n.search-form-container {\\n  background: #ffffff;\\n  border-radius: 8px;\\n  padding: 2rem;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n}\\n\\n/* Error Message */\\n.error-message {\\n  background-color: var(--error-bg);\\n  border: 1px solid #feb2b2;\\n  color: var(--error-color);\\n  padding: 1rem;\\n  border-radius: var(--border-radius);\\n  font-size: 0.875rem;\\n  display: flex;\\n  align-items: flex-start;\\n  gap: 0.75rem;\\n  margin-bottom: 1.5rem;\\n  position: relative;\\n}\\n\\n.error-icon {\\n  width: 1.25rem;\\n  height: 1.25rem;\\n  flex-shrink: 0;\\n  margin-top: 0.125rem;\\n}\\n\\n.error-close {\\n  position: absolute;\\n  top: 0.75rem;\\n  right: 0.75rem;\\n  background: none;\\n  border: none;\\n  color: var(--error-color);\\n  cursor: pointer;\\n  padding: 0.25rem;\\n  border-radius: 0.25rem;\\n  transition: var(--transition);\\n}\\n\\n.error-close:hover {\\n  background-color: rgba(229, 62, 62, 0.1);\\n}\\n\\n.error-close svg {\\n  width: 1rem;\\n  height: 1rem;\\n}\\n\\n/* Trip Type Tabs */\\n.trip-type-tabs {\\n  display: flex;\\n  margin-bottom: 2rem;\\n  border-radius: var(--border-radius);\\n  overflow: hidden;\\n  background: var(--light-gray);\\n  border: 1px solid var(--border-color);\\n}\\n\\n.tab-button {\\n  flex: 1;\\n  padding: 1rem 1.5rem;\\n  background: transparent;\\n  border: none;\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  color: var(--medium-gray);\\n  cursor: pointer;\\n  transition: var(--transition);\\n  position: relative;\\n}\\n\\n.tab-button.active {\\n  background: var(--dark-blue);\\n  color: var(--white);\\n  font-weight: 600;\\n}\\n\\n.tab-button:hover:not(.active) {\\n  background: rgba(74, 111, 165, 0.1);\\n  color: var(--primary-blue);\\n}\\n\\n/* Location Row */\\n.location-row {\\n  display: flex;\\n  align-items: flex-end;\\n  gap: 1rem;\\n  margin-bottom: 1.5rem;\\n}\\n\\n.location-field {\\n  flex: 1;\\n}\\n\\n.location-label {\\n  display: block;\\n  font-size: 0.875rem;\\n  font-weight: 600;\\n  color: var(--dark-gray);\\n  margin-bottom: 0.5rem;\\n}\\n\\n.location-input-container {\\n  position: relative;\\n  display: flex;\\n  align-items: center;\\n  background: var(--white);\\n  border: 2px solid var(--border-color);\\n  border-radius: var(--border-radius);\\n  transition: var(--transition);\\n}\\n\\n.location-input-container:focus-within {\\n  border-color: var(--primary-blue);\\n  box-shadow: 0 0 0 3px rgba(74, 111, 165, 0.1);\\n}\\n\\n.location-icon {\\n  position: absolute;\\n  left: 1rem;\\n  width: 1.25rem;\\n  height: 1.25rem;\\n  color: var(--primary-blue);\\n  z-index: 1;\\n}\\n\\n.location-input {\\n  width: 100%;\\n  padding: 1rem 3rem 1rem 3rem;\\n  border: none;\\n  border-radius: var(--border-radius);\\n  font-size: 0.875rem;\\n  background: transparent;\\n  transition: var(--transition);\\n  font-weight: 500;\\n}\\n\\n.location-input:focus {\\n  outline: none;\\n}\\n\\n.location-input.error {\\n  border-color: var(--error-color);\\n}\\n\\n.location-button {\\n  position: absolute;\\n  right: 0.5rem;\\n  background: none;\\n  border: none;\\n  padding: 0.5rem;\\n  cursor: pointer;\\n  color: var(--medium-gray);\\n  border-radius: 0.25rem;\\n  transition: var(--transition);\\n}\\n\\n.location-button:hover {\\n  background: var(--light-gray);\\n  color: var(--primary-blue);\\n}\\n\\n.location-button svg {\\n  width: 1rem;\\n  height: 1rem;\\n}\\n\\n/* Swap Button */\\n.swap-button {\\n  background: var(--white);\\n  border: 2px solid var(--primary-blue);\\n  border-radius: 50%;\\n  width: 2.5rem;\\n  height: 2.5rem;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  color: var(--primary-blue);\\n  cursor: pointer;\\n  transition: var(--transition);\\n  margin-bottom: 1.5rem;\\n  box-shadow: 0 2px 8px rgba(74, 111, 165, 0.2);\\n}\\n\\n.swap-button:hover {\\n  background: var(--primary-blue);\\n  color: var(--white);\\n  transform: rotate(180deg);\\n  box-shadow: 0 4px 12px rgba(74, 111, 165, 0.3);\\n}\\n\\n.swap-button svg {\\n  width: 1.25rem;\\n  height: 1.25rem;\\n}\\n\\n/* Date Row */\\n.date-row {\\n  display: flex;\\n  gap: 1rem;\\n  margin-bottom: 1.5rem;\\n}\\n\\n.date-field {\\n  flex: 1;\\n}\\n\\n.date-label {\\n  display: block;\\n  font-size: 0.875rem;\\n  font-weight: 600;\\n  color: var(--dark-gray);\\n  margin-bottom: 0.5rem;\\n}\\n\\n.date-input {\\n  width: 100%;\\n  padding: 1rem;\\n  border: 2px solid var(--border-color);\\n  border-radius: var(--border-radius);\\n  font-size: 0.875rem;\\n  background: var(--white);\\n  transition: var(--transition);\\n  font-weight: 500;\\n}\\n\\n.date-input:focus {\\n  outline: none;\\n  border-color: var(--primary-blue);\\n  box-shadow: 0 0 0 3px rgba(74, 111, 165, 0.1);\\n}\\n\\n.date-input.error {\\n  border-color: var(--error-color);\\n}\\n\\n/* Passenger & Class Row */\\n.passenger-class-row {\\n  display: flex;\\n  gap: 1rem;\\n  margin-bottom: 1.5rem;\\n}\\n\\n.passenger-field {\\n  flex: 2;\\n}\\n\\n.passenger-label {\\n  display: block;\\n  font-size: 0.875rem;\\n  font-weight: 600;\\n  color: var(--dark-gray);\\n  margin-bottom: 0.5rem;\\n}\\n\\n.passenger-display {\\n  border: 2px solid var(--border-color);\\n  border-radius: var(--border-radius);\\n  padding: 1rem;\\n  background: var(--white);\\n  transition: var(--transition);\\n}\\n\\n.passenger-display:focus-within {\\n  border-color: var(--primary-blue);\\n  box-shadow: 0 0 0 3px rgba(74, 111, 165, 0.1);\\n}\\n\\n.passenger-icons {\\n  display: flex;\\n  align-items: center;\\n  gap: 1.5rem;\\n  flex-wrap: wrap;\\n}\\n\\n.passenger-group,\\n.class-group {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n}\\n\\n.passenger-icon,\\n.class-icon {\\n  width: 1.25rem;\\n  height: 1.25rem;\\n  color: var(--primary-blue);\\n}\\n\\n.passenger-select,\\n.class-select {\\n  border: 1px solid var(--border-color);\\n  border-radius: 0.375rem;\\n  padding: 0.375rem 0.75rem;\\n  font-size: 0.875rem;\\n  background: var(--white);\\n  min-width: 3.5rem;\\n  font-weight: 500;\\n  transition: var(--transition);\\n}\\n\\n.passenger-select:focus,\\n.class-select:focus {\\n  outline: none;\\n  border-color: var(--primary-blue);\\n}\\n\\n/* Airline Field */\\n.airline-field {\\n  flex: 1;\\n}\\n\\n.airline-label {\\n  display: block;\\n  font-size: 0.875rem;\\n  font-weight: 600;\\n  color: var(--dark-gray);\\n  margin-bottom: 0.5rem;\\n}\\n\\n.airline-select {\\n  width: 100%;\\n  padding: 1rem;\\n  border: 2px solid var(--border-color);\\n  border-radius: var(--border-radius);\\n  font-size: 0.875rem;\\n  background: var(--white);\\n  transition: var(--transition);\\n  font-weight: 500;\\n}\\n\\n.airline-select:focus {\\n  outline: none;\\n  border-color: var(--primary-blue);\\n  box-shadow: 0 0 0 3px rgba(74, 111, 165, 0.1);\\n}\\n\\n/* Options Row */\\n.options-row {\\n  display: flex;\\n  gap: 1rem;\\n  margin-bottom: 2rem;\\n}\\n\\n.option-group {\\n  flex: 1;\\n}\\n\\n.option-label {\\n  display: block;\\n  font-size: 0.875rem;\\n  font-weight: 600;\\n  color: var(--dark-gray);\\n  margin-bottom: 0.5rem;\\n}\\n\\n.option-select {\\n  width: 100%;\\n  padding: 1rem;\\n  border: 2px solid var(--border-color);\\n  border-radius: var(--border-radius);\\n  font-size: 0.875rem;\\n  background: var(--white);\\n  transition: var(--transition);\\n  font-weight: 500;\\n}\\n\\n.option-select:focus {\\n  outline: none;\\n  border-color: var(--primary-blue);\\n  box-shadow: 0 0 0 3px rgba(74, 111, 165, 0.1);\\n}\\n\\n.calendar-display {\\n  padding: 1rem;\\n  border: 2px solid var(--border-color);\\n  border-radius: var(--border-radius);\\n  font-size: 0.875rem;\\n  background: var(--light-gray);\\n  color: var(--primary-blue);\\n  text-align: center;\\n  font-weight: 600;\\n}\\n\\n/* Search Button */\\n.search-button {\\n  width: 100%;\\n  background: linear-gradient(135deg, var(--success-color) 0%, #20c997 100%);\\n  color: var(--white);\\n  border: none;\\n  padding: 1.25rem 2rem;\\n  border-radius: var(--border-radius);\\n  font-size: 1rem;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: var(--transition);\\n  min-height: 3.5rem;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n  box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);\\n}\\n\\n.search-button:hover:not(:disabled) {\\n  background: linear-gradient(135deg, #218838 0%, #1e7e34 100%);\\n  transform: translateY(-2px);\\n  box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);\\n}\\n\\n.search-button:disabled {\\n  opacity: 0.6;\\n  cursor: not-allowed;\\n  transform: none;\\n  box-shadow: none;\\n}\\n\\n.loading-content {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n}\\n\\n.loading-spinner {\\n  width: 1.25rem;\\n  height: 1.25rem;\\n  animation: spin 1s linear infinite;\\n}\\n\\n@keyframes spin {\\n  from { transform: rotate(0deg); }\\n  to { transform: rotate(360deg); }\\n}\\n\\n/* Latest Searches Sidebar */\\n.latest-searches-sidebar {\\n  background: var(--white);\\n  border-radius: var(--border-radius);\\n  padding: 2rem;\\n  box-shadow: var(--shadow);\\n  height: -moz-fit-content;\\n  height: fit-content;\\n  border: 1px solid var(--border-color);\\n}\\n\\n.latest-searches-content {\\n  text-align: center;\\n  padding: 2rem 0;\\n}\\n\\n.no-searches {\\n  color: var(--medium-gray);\\n  font-style: italic;\\n  font-size: 0.875rem;\\n}\\n\\n/* Field Errors */\\n.field-error {\\n  color: var(--error-color);\\n  font-size: 0.75rem;\\n  margin-top: 0.25rem;\\n  font-weight: 500;\\n}\\n\\n/* Responsive Design */\\n@media (max-width: 1024px) {\\n  .search-content {\\n    grid-template-columns: 1fr;\\n  }\\n  \\n  .latest-searches-sidebar {\\n    order: -1;\\n  }\\n}\\n\\n@media (max-width: 768px) {\\n  .search-flight-container {\\n    padding: 1rem;\\n  }\\n  \\n  .search-header {\\n    flex-direction: column;\\n    text-align: center;\\n  }\\n  \\n  .location-row {\\n    flex-direction: column;\\n  }\\n  \\n  .swap-button {\\n    align-self: center;\\n    margin: 0.5rem 0;\\n  }\\n  \\n  .passenger-class-row {\\n    flex-direction: column;\\n  }\\n  \\n  .options-row {\\n    flex-direction: column;\\n  }\\n  \\n  .passenger-icons {\\n    flex-wrap: wrap;\\n    gap: 0.5rem;\\n  }\\n}\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"],\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "FlightClass", "i0", "ɵɵelementStart", "ɵɵnamespaceSVG", "ɵɵelement", "ɵɵelementEnd", "ɵɵnamespaceHTML", "ɵɵtext", "ɵɵlistener", "SearchFlightComponent_div_18_Template_button_click_5_listener", "ɵɵrestoreView", "_r13", "ctx_r12", "ɵɵnextContext", "ɵɵresetView", "clearError", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r0", "errorMessage", "ɵɵtextInterpolate1", "ctx_r1", "getErrorMessage", "ctx_r2", "ctx_r3", "ctx_r14", "ɵɵtemplate", "SearchFlightComponent_div_60_div_4_Template", "ɵɵclassProp", "ctx_r4", "<PERSON><PERSON><PERSON><PERSON>", "ɵɵproperty", "formControls", "touched", "errors", "count_r15", "count_r16", "count_r17", "flightClass_r18", "value", "label", "airline_r19", "SearchFlightComponent", "constructor", "formBuilder", "flightService", "router", "isLoading", "flightClasses", "ECONOMY", "PREMIUM_ECONOMY", "BUSINESS", "FIRST", "passengerCounts", "adults", "Array", "from", "length", "_", "i", "children", "infants", "preferredAirlines", "ngOnInit", "initializeForm", "searchForm", "group", "tripType", "required", "departureLocation", "<PERSON><PERSON><PERSON><PERSON>", "arrivalLocation", "departureDate", "returnDate", "min", "flightClass", "directFlightsOnly", "preferredAirline", "get", "valueChanges", "subscribe", "returnDateControl", "setValidators", "clearValidators", "updateValueAndValidity", "controls", "swapAirports", "departure", "arrival", "patchValue", "getTotalPassengers", "getPassengerText", "text", "getDaysBetweenDates", "start", "Date", "end", "diffTime", "Math", "abs", "getTime", "diffDays", "ceil", "onSubmit", "valid", "searchData", "trim", "passengers", "searchObservable", "searchOneWayFlights", "searchRoundTripFlights", "searchMulticityFlights", "next", "response", "header", "success", "console", "log", "handleApiError", "error", "message", "markFormGroupTouched", "messages", "Object", "keys", "for<PERSON>ach", "key", "control", "<PERSON><PERSON><PERSON><PERSON>ched", "fieldName", "errorType", "field", "<PERSON><PERSON><PERSON><PERSON>", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "FlightService", "i3", "Router", "selectors", "decls", "vars", "consts", "template", "SearchFlightComponent_Template", "rf", "ctx", "SearchFlightComponent_div_18_Template", "SearchFlightComponent_Template_form_ngSubmit_19_listener", "SearchFlightComponent_Template_button_click_21_listener", "SearchFlightComponent_Template_button_click_23_listener", "SearchFlightComponent_Template_button_click_25_listener", "SearchFlightComponent_div_38_Template", "SearchFlightComponent_Template_button_click_39_listener", "SearchFlightComponent_div_53_Template", "SearchFlightComponent_div_59_Template", "SearchFlightComponent_div_60_Template", "SearchFlightComponent_option_71_Template", "SearchFlightComponent_option_76_Template", "SearchFlightComponent_option_81_Template", "SearchFlightComponent_option_86_Template", "SearchFlightComponent_option_93_Template", "SearchFlightComponent_span_113_Template", "SearchFlightComponent_span_114_Template", "invalid"], "sources": ["C:\\Users\\<USER>\\Desktop\\angular\\front\\src\\app\\components\\search-flight\\search-flight.component.ts", "C:\\Users\\<USER>\\Desktop\\angular\\front\\src\\app\\components\\search-flight\\search-flight.component.html"], "sourcesContent": ["import { Component, OnInit, ViewEncapsulation } from '@angular/core';\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { Router } from '@angular/router';\n\nimport { FlightService } from '../../services/flight.service';\nimport { FlightSearchForm, FlightClass } from '../../models/flight-search.interface';\n\n@Component({\n  selector: 'app-search-flight',\n  templateUrl: './search-flight.component.html',\n  styleUrls: ['./search-flight.component.css'],\n  encapsulation: ViewEncapsulation.None\n})\nexport class SearchFlightComponent implements OnInit {\n  searchForm!: FormGroup;\n  isLoading = false;\n  errorMessage = '';\n  \n  // Options pour les sélecteurs\n  flightClasses = [\n    { value: FlightClass.ECONOMY, label: 'Economy' },\n    { value: FlightClass.PREMIUM_ECONOMY, label: 'Premium Economy' },\n    { value: FlightClass.BUSINESS, label: 'Business' },\n    { value: FlightClass.FIRST, label: 'First Class' }\n  ];\n\n  // Données pour les passagers\n  passengerCounts = {\n    adults: Array.from({length: 9}, (_, i) => i + 1),\n    children: Array.from({length: 8}, (_, i) => i),\n    infants: Array.from({length: 4}, (_, i) => i)\n  };\n\n  // Compagnies aériennes préférées (exemple)\n  preferredAirlines = [\n    'Preferred Airline',\n    'Turkish Airlines',\n    'Emirates',\n    'Qatar Airways',\n    'Lufthansa',\n    'Air France',\n    'British Airways'\n  ];\n\n  constructor(\n    private formBuilder: FormBuilder,\n    private flightService: FlightService,\n    private router: Router\n  ) {}\n\n  ngOnInit(): void {\n    this.initializeForm();\n  }\n\n  /**\n   * Initialise le formulaire de recherche\n   */\n  private initializeForm(): void {\n    this.searchForm = this.formBuilder.group({\n      tripType: ['oneWay', Validators.required],\n      departureLocation: ['', [Validators.required, Validators.minLength(3)]],\n      arrivalLocation: ['', [Validators.required, Validators.minLength(3)]],\n      departureDate: ['', Validators.required],\n      returnDate: [''],\n      adults: [1, [Validators.required, Validators.min(1)]],\n      children: [0, [Validators.required, Validators.min(0)]],\n      infants: [0, [Validators.required, Validators.min(0)]],\n      flightClass: [FlightClass.ECONOMY, Validators.required],\n      directFlightsOnly: [false],\n      preferredAirline: ['']\n    });\n\n    // Validation conditionnelle pour la date de retour\n    this.searchForm.get('tripType')?.valueChanges.subscribe(tripType => {\n      const returnDateControl = this.searchForm.get('returnDate');\n      if (tripType === 'roundTrip') {\n        returnDateControl?.setValidators([Validators.required]);\n      } else {\n        returnDateControl?.clearValidators();\n      }\n      returnDateControl?.updateValueAndValidity();\n    });\n  }\n\n  /**\n   * Getter pour accéder facilement aux contrôles du formulaire\n   */\n  get formControls() {\n    return this.searchForm.controls;\n  }\n\n  /**\n   * Échange les aéroports de départ et d'arrivée\n   */\n  swapAirports(): void {\n    const departure = this.searchForm.get('departureLocation')?.value;\n    const arrival = this.searchForm.get('arrivalLocation')?.value;\n    \n    this.searchForm.patchValue({\n      departureLocation: arrival,\n      arrivalLocation: departure\n    });\n  }\n\n  /**\n   * Obtient le nombre total de passagers\n   */\n  getTotalPassengers(): number {\n    const adults = this.searchForm.get('adults')?.value || 0;\n    const children = this.searchForm.get('children')?.value || 0;\n    const infants = this.searchForm.get('infants')?.value || 0;\n    return adults + children + infants;\n  }\n\n  /**\n   * Obtient le texte d'affichage pour les passagers\n   */\n  getPassengerText(): string {\n    const adults = this.searchForm.get('adults')?.value || 0;\n    const children = this.searchForm.get('children')?.value || 0;\n    const infants = this.searchForm.get('infants')?.value || 0;\n    \n    let text = `${adults} Adult${adults > 1 ? 's' : ''}`;\n    if (children > 0) text += `, ${children} Child${children > 1 ? 'ren' : ''}`;\n    if (infants > 0) text += `, ${infants} Infant${infants > 1 ? 's' : ''}`;\n    \n    return text;\n  }\n\n  /**\n   * Obtient le nombre de jours entre les dates\n   */\n  getDaysBetweenDates(): string {\n    const departureDate = this.searchForm.get('departureDate')?.value;\n    const returnDate = this.searchForm.get('returnDate')?.value;\n    \n    if (!departureDate || !returnDate) return '';\n    \n    const start = new Date(departureDate);\n    const end = new Date(returnDate);\n    const diffTime = Math.abs(end.getTime() - start.getTime());\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n    \n    return `+/- ${diffDays} Days`;\n  }\n\n  /**\n   * Soumet le formulaire de recherche\n   */\n  onSubmit(): void {\n    if (this.searchForm.valid && !this.isLoading) {\n      this.isLoading = true;\n      this.errorMessage = '';\n\n      const searchData: FlightSearchForm = {\n        tripType: this.searchForm.value.tripType,\n        departureLocation: this.searchForm.value.departureLocation.trim(),\n        arrivalLocation: this.searchForm.value.arrivalLocation.trim(),\n        departureDate: this.searchForm.value.departureDate,\n        returnDate: this.searchForm.value.returnDate,\n        passengers: {\n          adults: this.searchForm.value.adults,\n          children: this.searchForm.value.children,\n          infants: this.searchForm.value.infants\n        },\n        flightClass: this.searchForm.value.flightClass,\n        directFlightsOnly: this.searchForm.value.directFlightsOnly,\n        preferredAirline: this.searchForm.value.preferredAirline\n      };\n\n      // Appel du service selon le type de voyage\n      let searchObservable;\n      switch (searchData.tripType) {\n        case 'oneWay':\n          searchObservable = this.flightService.searchOneWayFlights(searchData);\n          break;\n        case 'roundTrip':\n          searchObservable = this.flightService.searchRoundTripFlights(searchData);\n          break;\n        case 'multiCity':\n          searchObservable = this.flightService.searchMulticityFlights(searchData);\n          break;\n        default:\n          this.isLoading = false;\n          this.errorMessage = 'Type de voyage non valide';\n          return;\n      }\n\n      searchObservable.subscribe({\n        next: (response) => {\n          this.isLoading = false;\n          if (response.header.success) {\n            console.log('Recherche réussie:', response);\n            // Rediriger vers la page de résultats ou traiter les résultats\n            // this.router.navigate(['/flight-results'], { state: { results: response } });\n          } else {\n            this.handleApiError(response);\n          }\n        },\n        error: (error) => {\n          this.isLoading = false;\n          this.errorMessage = error.message || 'Une erreur est survenue lors de la recherche';\n          console.error('Erreur de recherche:', error);\n        }\n      });\n    } else {\n      this.markFormGroupTouched();\n    }\n  }\n\n  /**\n   * Gère les erreurs retournées par l'API\n   */\n  private handleApiError(response: any): void {\n    if (response.header.messages && response.header.messages.length > 0) {\n      this.errorMessage = response.header.messages[0].message;\n    } else {\n      this.errorMessage = 'Aucun vol trouvé pour ces critères';\n    }\n  }\n\n  /**\n   * Marque tous les champs du formulaire comme touchés\n   */\n  private markFormGroupTouched(): void {\n    Object.keys(this.searchForm.controls).forEach(key => {\n      const control = this.searchForm.get(key);\n      control?.markAsTouched();\n    });\n  }\n\n  /**\n   * Vérifie si un champ a une erreur et a été touché\n   */\n  hasError(fieldName: string, errorType: string): boolean {\n    const field = this.searchForm.get(fieldName);\n    return !!(field?.hasError(errorType) && field?.touched);\n  }\n\n  /**\n   * Récupère le message d'erreur pour un champ\n   */\n  getErrorMessage(fieldName: string): string {\n    const field = this.searchForm.get(fieldName);\n    \n    if (field?.hasError('required')) {\n      return `Ce champ est requis`;\n    }\n    \n    if (field?.hasError('minlength')) {\n      const requiredLength = field.errors?.['minlength']?.requiredLength;\n      return `Minimum ${requiredLength} caractères requis`;\n    }\n    \n    if (field?.hasError('min')) {\n      const min = field.errors?.['min']?.min;\n      return `La valeur minimum est ${min}`;\n    }\n    \n    return '';\n  }\n\n  /**\n   * Nettoie le message d'erreur\n   */\n  clearError(): void {\n    this.errorMessage = '';\n  }\n}\n", "<div class=\"search-flight-container\">\n  <!-- Header Section -->\n  <div class=\"search-header\">\n    <div class=\"search-title-section\">\n      <div class=\"search-icon\">\n        <svg viewBox=\"0 0 24 24\" fill=\"currentColor\">\n          <circle cx=\"12\" cy=\"12\" r=\"10\"/>\n        </svg>\n      </div>\n      <div class=\"search-title-content\">\n        <h1 class=\"search-title\">Search and Book Flights</h1>\n        <p class=\"search-subtitle\">We're bringing you a new level of comfort</p>\n      </div>\n    </div>\n    \n    <div class=\"latest-searches\">\n      <h2 class=\"latest-title\">Latest Searches</h2>\n      <p class=\"latest-subtitle\">We're bringing you a new level of comfort</p>\n    </div>\n  </div>\n\n  <!-- Main Search Form -->\n  <div class=\"search-content\">\n    <div class=\"search-form-container\">\n      <!-- Message d'erreur global -->\n      <div *ngIf=\"errorMessage\" class=\"error-message\" role=\"alert\">\n        <svg class=\"error-icon\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n          <path fill-rule=\"evenodd\" d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z\" clip-rule=\"evenodd\"></path>\n        </svg>\n        <span>{{ errorMessage }}</span>\n        <button type=\"button\" class=\"error-close\" (click)=\"clearError()\">\n          <svg fill=\"currentColor\" viewBox=\"0 0 20 20\">\n            <path fill-rule=\"evenodd\" d=\"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z\" clip-rule=\"evenodd\"></path>\n          </svg>\n        </button>\n      </div>\n\n      <form [formGroup]=\"searchForm\" (ngSubmit)=\"onSubmit()\" class=\"search-form\" novalidate>\n        \n        <!-- Trip Type Tabs -->\n        <div class=\"trip-type-tabs\">\n          <button type=\"button\" \n                  class=\"tab-button\" \n                  [class.active]=\"formControls['tripType'].value === 'oneWay'\"\n                  (click)=\"searchForm.patchValue({tripType: 'oneWay'})\">\n            One way\n          </button>\n          <button type=\"button\" \n                  class=\"tab-button\" \n                  [class.active]=\"formControls['tripType'].value === 'roundTrip'\"\n                  (click)=\"searchForm.patchValue({tripType: 'roundTrip'})\">\n            Round Trip\n          </button>\n          <button type=\"button\" \n                  class=\"tab-button\" \n                  [class.active]=\"formControls['tripType'].value === 'multiCity'\"\n                  (click)=\"searchForm.patchValue({tripType: 'multiCity'})\">\n            Multi-City/Stop-Overs\n          </button>\n        </div>\n\n        <!-- Location Fields -->\n        <div class=\"location-row\">\n          <div class=\"location-field\">\n            <label class=\"location-label\">From</label>\n            <div class=\"location-input-container\">\n              <svg class=\"location-icon\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 19l9 2-9-18-9 18 9-2zm0 0v-8\"/>\n              </svg>\n              <input type=\"text\" \n                     formControlName=\"departureLocation\"\n                     class=\"location-input\"\n                     [class.error]=\"hasError('departureLocation', 'required') || hasError('departureLocation', 'minlength')\"\n                     placeholder=\"IST - Istanbul Airport\">\n              <button type=\"button\" class=\"location-button\">\n                <svg fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M19 9l-7 7-7-7\"/>\n                </svg>\n              </button>\n            </div>\n            <div *ngIf=\"formControls['departureLocation'].touched && formControls['departureLocation'].errors\" class=\"field-error\">\n              {{ getErrorMessage('departureLocation') }}\n            </div>\n          </div>\n\n          <!-- Swap Button -->\n          <button type=\"button\" class=\"swap-button\" (click)=\"swapAirports()\">\n            <svg fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4\"/>\n            </svg>\n          </button>\n\n          <div class=\"location-field\">\n            <label class=\"location-label\">To</label>\n            <div class=\"location-input-container\">\n              <svg class=\"location-icon\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\"/>\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M15 11a3 3 0 11-6 0 3 3 0 016 0z\"/>\n              </svg>\n              <input type=\"text\" \n                     formControlName=\"arrivalLocation\"\n                     class=\"location-input\"\n                     [class.error]=\"hasError('arrivalLocation', 'required') || hasError('arrivalLocation', 'minlength')\"\n                     placeholder=\"TUN - Carthage Arpt\">\n              <button type=\"button\" class=\"location-button\">\n                <svg fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M19 9l-7 7-7-7\"/>\n                </svg>\n              </button>\n            </div>\n            <div *ngIf=\"formControls['arrivalLocation'].touched && formControls['arrivalLocation'].errors\" class=\"field-error\">\n              {{ getErrorMessage('arrivalLocation') }}\n            </div>\n          </div>\n        </div>\n\n        <!-- Date Field -->\n        <div class=\"date-row\">\n          <div class=\"date-field\">\n            <label class=\"date-label\">From</label>\n            <input type=\"date\" \n                   formControlName=\"departureDate\"\n                   class=\"date-input\"\n                   [class.error]=\"hasError('departureDate', 'required')\">\n            <div *ngIf=\"formControls['departureDate'].touched && formControls['departureDate'].errors\" class=\"field-error\">\n              {{ getErrorMessage('departureDate') }}\n            </div>\n          </div>\n\n          <div class=\"date-field\" *ngIf=\"formControls['tripType'].value === 'roundTrip'\">\n            <label class=\"date-label\">To</label>\n            <input type=\"date\" \n                   formControlName=\"returnDate\"\n                   class=\"date-input\"\n                   [class.error]=\"hasError('returnDate', 'required')\">\n            <div *ngIf=\"formControls['returnDate'].touched && formControls['returnDate'].errors\" class=\"field-error\">\n              {{ getErrorMessage('returnDate') }}\n            </div>\n          </div>\n        </div>\n\n        <!-- Passenger & Class Row -->\n        <div class=\"passenger-class-row\">\n          <div class=\"passenger-field\">\n            <label class=\"passenger-label\">Passenger & Class of travel</label>\n            <div class=\"passenger-display\">\n              <div class=\"passenger-icons\">\n                <div class=\"passenger-group\">\n                  <svg class=\"passenger-icon\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\"/>\n                  </svg>\n                  <select formControlName=\"adults\" class=\"passenger-select\">\n                    <option *ngFor=\"let count of passengerCounts.adults\" [value]=\"count\">{{ count }}</option>\n                  </select>\n                </div>\n                <div class=\"passenger-group\">\n                  <svg class=\"passenger-icon\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\"/>\n                  </svg>\n                  <select formControlName=\"children\" class=\"passenger-select\">\n                    <option *ngFor=\"let count of passengerCounts.children\" [value]=\"count\">{{ count }}</option>\n                  </select>\n                </div>\n                <div class=\"passenger-group\">\n                  <svg class=\"passenger-icon\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z\"/>\n                  </svg>\n                  <select formControlName=\"infants\" class=\"passenger-select\">\n                    <option *ngFor=\"let count of passengerCounts.infants\" [value]=\"count\">{{ count }}</option>\n                  </select>\n                </div>\n                <div class=\"class-group\">\n                  <svg class=\"class-icon\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"/>\n                  </svg>\n                  <select formControlName=\"flightClass\" class=\"class-select\">\n                    <option *ngFor=\"let flightClass of flightClasses\" [value]=\"flightClass.value\">{{ flightClass.label }}</option>\n                  </select>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <div class=\"airline-field\">\n            <label class=\"airline-label\">Preferred Airline</label>\n            <select formControlName=\"preferredAirline\" class=\"airline-select\">\n              <option value=\"\">Preferred Airline</option>\n              <option *ngFor=\"let airline of preferredAirlines\" [value]=\"airline\">{{ airline }}</option>\n            </select>\n          </div>\n        </div>\n\n        <!-- Options Row -->\n        <div class=\"options-row\">\n          <div class=\"option-group\">\n            <label class=\"option-label\">Refundable fares</label>\n            <select class=\"option-select\">\n              <option>--All--</option>\n            </select>\n          </div>\n\n          <div class=\"option-group\">\n            <label class=\"option-label\">Baggage</label>\n            <select class=\"option-select\">\n              <option>--All--</option>\n            </select>\n          </div>\n\n          <div class=\"option-group\">\n            <label class=\"option-label\">Calendar</label>\n            <div class=\"calendar-display\">{{ getDaysBetweenDates() || '+/- 3 Days' }}</div>\n          </div>\n        </div>\n\n        <!-- Search Button -->\n        <button type=\"submit\" \n                class=\"search-button\"\n                [disabled]=\"isLoading || searchForm.invalid\"\n                [class.loading]=\"isLoading\">\n          <span *ngIf=\"!isLoading\">SEARCH NOW</span>\n          <span *ngIf=\"isLoading\" class=\"loading-content\">\n            <svg class=\"loading-spinner\" viewBox=\"0 0 24 24\">\n              <circle cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" stroke-width=\"4\" fill=\"none\" opacity=\"0.25\"></circle>\n              <path fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\" opacity=\"0.75\"></path>\n            </svg>\n            Searching...\n          </span>\n        </button>\n      </form>\n    </div>\n\n    <!-- Latest Searches Sidebar -->\n    <div class=\"latest-searches-sidebar\">\n      <div class=\"latest-searches-content\">\n        <!-- Contenu des dernières recherches -->\n        <p class=\"no-searches\">No recent searches</p>\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": "AACA,SAAiCA,UAAU,QAAQ,gBAAgB;AAInE,SAA2BC,WAAW,QAAQ,sCAAsC;;;;;;;;;ICoB9EC,EAAA,CAAAC,cAAA,cAA6D;IAC3DD,EAAA,CAAAE,cAAA,EAAgE;IAAhEF,EAAA,CAAAC,cAAA,cAAgE;IAC9DD,EAAA,CAAAG,SAAA,eAA2K;IAC7KH,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAK,eAAA,EAAM;IAANL,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAM,MAAA,GAAkB;IAAAN,EAAA,CAAAI,YAAA,EAAO;IAC/BJ,EAAA,CAAAC,cAAA,iBAAiE;IAAvBD,EAAA,CAAAO,UAAA,mBAAAC,8DAAA;MAAAR,EAAA,CAAAS,aAAA,CAAAC,IAAA;MAAA,MAAAC,OAAA,GAAAX,EAAA,CAAAY,aAAA;MAAA,OAASZ,EAAA,CAAAa,WAAA,CAAAF,OAAA,CAAAG,UAAA,EAAY;IAAA,EAAC;IAC9Dd,EAAA,CAAAE,cAAA,EAA6C;IAA7CF,EAAA,CAAAC,cAAA,cAA6C;IAC3CD,EAAA,CAAAG,SAAA,eAA4P;IAC9PH,EAAA,CAAAI,YAAA,EAAM;;;;IAJFJ,EAAA,CAAAe,SAAA,GAAkB;IAAlBf,EAAA,CAAAgB,iBAAA,CAAAC,MAAA,CAAAC,YAAA,CAAkB;;;;;;IAmDpBlB,EAAA,CAAAK,eAAA,EAAuH;IAAvHL,EAAA,CAAAC,cAAA,cAAuH;IACrHD,EAAA,CAAAM,MAAA,GACF;IAAAN,EAAA,CAAAI,YAAA,EAAM;;;;IADJJ,EAAA,CAAAe,SAAA,GACF;IADEf,EAAA,CAAAmB,kBAAA,MAAAC,MAAA,CAAAC,eAAA,2BACF;;;;;;IA4BArB,EAAA,CAAAK,eAAA,EAAmH;IAAnHL,EAAA,CAAAC,cAAA,cAAmH;IACjHD,EAAA,CAAAM,MAAA,GACF;IAAAN,EAAA,CAAAI,YAAA,EAAM;;;;IADJJ,EAAA,CAAAe,SAAA,GACF;IADEf,EAAA,CAAAmB,kBAAA,MAAAG,MAAA,CAAAD,eAAA,yBACF;;;;;IAYArB,EAAA,CAAAC,cAAA,cAA+G;IAC7GD,EAAA,CAAAM,MAAA,GACF;IAAAN,EAAA,CAAAI,YAAA,EAAM;;;;IADJJ,EAAA,CAAAe,SAAA,GACF;IADEf,EAAA,CAAAmB,kBAAA,MAAAI,MAAA,CAAAF,eAAA,uBACF;;;;;IASArB,EAAA,CAAAC,cAAA,cAAyG;IACvGD,EAAA,CAAAM,MAAA,GACF;IAAAN,EAAA,CAAAI,YAAA,EAAM;;;;IADJJ,EAAA,CAAAe,SAAA,GACF;IADEf,EAAA,CAAAmB,kBAAA,MAAAK,OAAA,CAAAH,eAAA,oBACF;;;;;IARFrB,EAAA,CAAAC,cAAA,cAA+E;IACnDD,EAAA,CAAAM,MAAA,SAAE;IAAAN,EAAA,CAAAI,YAAA,EAAQ;IACpCJ,EAAA,CAAAG,SAAA,gBAG0D;IAC1DH,EAAA,CAAAyB,UAAA,IAAAC,2CAAA,kBAEM;IACR1B,EAAA,CAAAI,YAAA,EAAM;;;;IAJGJ,EAAA,CAAAe,SAAA,GAAkD;IAAlDf,EAAA,CAAA2B,WAAA,UAAAC,MAAA,CAAAC,QAAA,2BAAkD;IACnD7B,EAAA,CAAAe,SAAA,GAA6E;IAA7Ef,EAAA,CAAA8B,UAAA,SAAAF,MAAA,CAAAG,YAAA,eAAAC,OAAA,IAAAJ,MAAA,CAAAG,YAAA,eAAAE,MAAA,CAA6E;;;;;IAiB3EjC,EAAA,CAAAC,cAAA,iBAAqE;IAAAD,EAAA,CAAAM,MAAA,GAAW;IAAAN,EAAA,CAAAI,YAAA,EAAS;;;;IAApCJ,EAAA,CAAA8B,UAAA,UAAAI,SAAA,CAAe;IAAClC,EAAA,CAAAe,SAAA,GAAW;IAAXf,EAAA,CAAAgB,iBAAA,CAAAkB,SAAA,CAAW;;;;;IAQhFlC,EAAA,CAAAC,cAAA,iBAAuE;IAAAD,EAAA,CAAAM,MAAA,GAAW;IAAAN,EAAA,CAAAI,YAAA,EAAS;;;;IAApCJ,EAAA,CAAA8B,UAAA,UAAAK,SAAA,CAAe;IAACnC,EAAA,CAAAe,SAAA,GAAW;IAAXf,EAAA,CAAAgB,iBAAA,CAAAmB,SAAA,CAAW;;;;;IAQlFnC,EAAA,CAAAC,cAAA,iBAAsE;IAAAD,EAAA,CAAAM,MAAA,GAAW;IAAAN,EAAA,CAAAI,YAAA,EAAS;;;;IAApCJ,EAAA,CAAA8B,UAAA,UAAAM,SAAA,CAAe;IAACpC,EAAA,CAAAe,SAAA,GAAW;IAAXf,EAAA,CAAAgB,iBAAA,CAAAoB,SAAA,CAAW;;;;;IAQjFpC,EAAA,CAAAC,cAAA,iBAA8E;IAAAD,EAAA,CAAAM,MAAA,GAAuB;IAAAN,EAAA,CAAAI,YAAA,EAAS;;;;IAA5DJ,EAAA,CAAA8B,UAAA,UAAAO,eAAA,CAAAC,KAAA,CAA2B;IAACtC,EAAA,CAAAe,SAAA,GAAuB;IAAvBf,EAAA,CAAAgB,iBAAA,CAAAqB,eAAA,CAAAE,KAAA,CAAuB;;;;;IAW3GvC,EAAA,CAAAC,cAAA,iBAAoE;IAAAD,EAAA,CAAAM,MAAA,GAAa;IAAAN,EAAA,CAAAI,YAAA,EAAS;;;;IAAxCJ,EAAA,CAAA8B,UAAA,UAAAU,WAAA,CAAiB;IAACxC,EAAA,CAAAe,SAAA,GAAa;IAAbf,EAAA,CAAAgB,iBAAA,CAAAwB,WAAA,CAAa;;;;;IAgCrFxC,EAAA,CAAAC,cAAA,WAAyB;IAAAD,EAAA,CAAAM,MAAA,iBAAU;IAAAN,EAAA,CAAAI,YAAA,EAAO;;;;;IAC1CJ,EAAA,CAAAC,cAAA,eAAgD;IAC9CD,EAAA,CAAAE,cAAA,EAAiD;IAAjDF,EAAA,CAAAC,cAAA,cAAiD;IAC/CD,EAAA,CAAAG,SAAA,iBAA0G;IAE5GH,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAM,MAAA,qBACF;IAAAN,EAAA,CAAAI,YAAA,EAAO;;;ADrNjB,OAAM,MAAOqC,qBAAqB;EA+BhCC,YACUC,WAAwB,EACxBC,aAA4B,EAC5BC,MAAc;IAFd,KAAAF,WAAW,GAAXA,WAAW;IACX,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,MAAM,GAANA,MAAM;IAhChB,KAAAC,SAAS,GAAG,KAAK;IACjB,KAAA5B,YAAY,GAAG,EAAE;IAEjB;IACA,KAAA6B,aAAa,GAAG,CACd;MAAET,KAAK,EAAEvC,WAAW,CAACiD,OAAO;MAAET,KAAK,EAAE;IAAS,CAAE,EAChD;MAAED,KAAK,EAAEvC,WAAW,CAACkD,eAAe;MAAEV,KAAK,EAAE;IAAiB,CAAE,EAChE;MAAED,KAAK,EAAEvC,WAAW,CAACmD,QAAQ;MAAEX,KAAK,EAAE;IAAU,CAAE,EAClD;MAAED,KAAK,EAAEvC,WAAW,CAACoD,KAAK;MAAEZ,KAAK,EAAE;IAAa,CAAE,CACnD;IAED;IACA,KAAAa,eAAe,GAAG;MAChBC,MAAM,EAAEC,KAAK,CAACC,IAAI,CAAC;QAACC,MAAM,EAAE;MAAC,CAAC,EAAE,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,GAAG,CAAC,CAAC;MAChDC,QAAQ,EAAEL,KAAK,CAACC,IAAI,CAAC;QAACC,MAAM,EAAE;MAAC,CAAC,EAAE,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAAC;MAC9CE,OAAO,EAAEN,KAAK,CAACC,IAAI,CAAC;QAACC,MAAM,EAAE;MAAC,CAAC,EAAE,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC;KAC7C;IAED;IACA,KAAAG,iBAAiB,GAAG,CAClB,mBAAmB,EACnB,kBAAkB,EAClB,UAAU,EACV,eAAe,EACf,WAAW,EACX,YAAY,EACZ,iBAAiB,CAClB;EAME;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,cAAc,EAAE;EACvB;EAEA;;;EAGQA,cAAcA,CAAA;IACpB,IAAI,CAACC,UAAU,GAAG,IAAI,CAACrB,WAAW,CAACsB,KAAK,CAAC;MACvCC,QAAQ,EAAE,CAAC,QAAQ,EAAEpE,UAAU,CAACqE,QAAQ,CAAC;MACzCC,iBAAiB,EAAE,CAAC,EAAE,EAAE,CAACtE,UAAU,CAACqE,QAAQ,EAAErE,UAAU,CAACuE,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MACvEC,eAAe,EAAE,CAAC,EAAE,EAAE,CAACxE,UAAU,CAACqE,QAAQ,EAAErE,UAAU,CAACuE,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MACrEE,aAAa,EAAE,CAAC,EAAE,EAAEzE,UAAU,CAACqE,QAAQ,CAAC;MACxCK,UAAU,EAAE,CAAC,EAAE,CAAC;MAChBnB,MAAM,EAAE,CAAC,CAAC,EAAE,CAACvD,UAAU,CAACqE,QAAQ,EAAErE,UAAU,CAAC2E,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;MACrDd,QAAQ,EAAE,CAAC,CAAC,EAAE,CAAC7D,UAAU,CAACqE,QAAQ,EAAErE,UAAU,CAAC2E,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;MACvDb,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC9D,UAAU,CAACqE,QAAQ,EAAErE,UAAU,CAAC2E,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;MACtDC,WAAW,EAAE,CAAC3E,WAAW,CAACiD,OAAO,EAAElD,UAAU,CAACqE,QAAQ,CAAC;MACvDQ,iBAAiB,EAAE,CAAC,KAAK,CAAC;MAC1BC,gBAAgB,EAAE,CAAC,EAAE;KACtB,CAAC;IAEF;IACA,IAAI,CAACZ,UAAU,CAACa,GAAG,CAAC,UAAU,CAAC,EAAEC,YAAY,CAACC,SAAS,CAACb,QAAQ,IAAG;MACjE,MAAMc,iBAAiB,GAAG,IAAI,CAAChB,UAAU,CAACa,GAAG,CAAC,YAAY,CAAC;MAC3D,IAAIX,QAAQ,KAAK,WAAW,EAAE;QAC5Bc,iBAAiB,EAAEC,aAAa,CAAC,CAACnF,UAAU,CAACqE,QAAQ,CAAC,CAAC;OACxD,MAAM;QACLa,iBAAiB,EAAEE,eAAe,EAAE;;MAEtCF,iBAAiB,EAAEG,sBAAsB,EAAE;IAC7C,CAAC,CAAC;EACJ;EAEA;;;EAGA,IAAIpD,YAAYA,CAAA;IACd,OAAO,IAAI,CAACiC,UAAU,CAACoB,QAAQ;EACjC;EAEA;;;EAGAC,YAAYA,CAAA;IACV,MAAMC,SAAS,GAAG,IAAI,CAACtB,UAAU,CAACa,GAAG,CAAC,mBAAmB,CAAC,EAAEvC,KAAK;IACjE,MAAMiD,OAAO,GAAG,IAAI,CAACvB,UAAU,CAACa,GAAG,CAAC,iBAAiB,CAAC,EAAEvC,KAAK;IAE7D,IAAI,CAAC0B,UAAU,CAACwB,UAAU,CAAC;MACzBpB,iBAAiB,EAAEmB,OAAO;MAC1BjB,eAAe,EAAEgB;KAClB,CAAC;EACJ;EAEA;;;EAGAG,kBAAkBA,CAAA;IAChB,MAAMpC,MAAM,GAAG,IAAI,CAACW,UAAU,CAACa,GAAG,CAAC,QAAQ,CAAC,EAAEvC,KAAK,IAAI,CAAC;IACxD,MAAMqB,QAAQ,GAAG,IAAI,CAACK,UAAU,CAACa,GAAG,CAAC,UAAU,CAAC,EAAEvC,KAAK,IAAI,CAAC;IAC5D,MAAMsB,OAAO,GAAG,IAAI,CAACI,UAAU,CAACa,GAAG,CAAC,SAAS,CAAC,EAAEvC,KAAK,IAAI,CAAC;IAC1D,OAAOe,MAAM,GAAGM,QAAQ,GAAGC,OAAO;EACpC;EAEA;;;EAGA8B,gBAAgBA,CAAA;IACd,MAAMrC,MAAM,GAAG,IAAI,CAACW,UAAU,CAACa,GAAG,CAAC,QAAQ,CAAC,EAAEvC,KAAK,IAAI,CAAC;IACxD,MAAMqB,QAAQ,GAAG,IAAI,CAACK,UAAU,CAACa,GAAG,CAAC,UAAU,CAAC,EAAEvC,KAAK,IAAI,CAAC;IAC5D,MAAMsB,OAAO,GAAG,IAAI,CAACI,UAAU,CAACa,GAAG,CAAC,SAAS,CAAC,EAAEvC,KAAK,IAAI,CAAC;IAE1D,IAAIqD,IAAI,GAAG,GAAGtC,MAAM,SAASA,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE;IACpD,IAAIM,QAAQ,GAAG,CAAC,EAAEgC,IAAI,IAAI,KAAKhC,QAAQ,SAASA,QAAQ,GAAG,CAAC,GAAG,KAAK,GAAG,EAAE,EAAE;IAC3E,IAAIC,OAAO,GAAG,CAAC,EAAE+B,IAAI,IAAI,KAAK/B,OAAO,UAAUA,OAAO,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE;IAEvE,OAAO+B,IAAI;EACb;EAEA;;;EAGAC,mBAAmBA,CAAA;IACjB,MAAMrB,aAAa,GAAG,IAAI,CAACP,UAAU,CAACa,GAAG,CAAC,eAAe,CAAC,EAAEvC,KAAK;IACjE,MAAMkC,UAAU,GAAG,IAAI,CAACR,UAAU,CAACa,GAAG,CAAC,YAAY,CAAC,EAAEvC,KAAK;IAE3D,IAAI,CAACiC,aAAa,IAAI,CAACC,UAAU,EAAE,OAAO,EAAE;IAE5C,MAAMqB,KAAK,GAAG,IAAIC,IAAI,CAACvB,aAAa,CAAC;IACrC,MAAMwB,GAAG,GAAG,IAAID,IAAI,CAACtB,UAAU,CAAC;IAChC,MAAMwB,QAAQ,GAAGC,IAAI,CAACC,GAAG,CAACH,GAAG,CAACI,OAAO,EAAE,GAAGN,KAAK,CAACM,OAAO,EAAE,CAAC;IAC1D,MAAMC,QAAQ,GAAGH,IAAI,CAACI,IAAI,CAACL,QAAQ,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IAE5D,OAAO,OAAOI,QAAQ,OAAO;EAC/B;EAEA;;;EAGAE,QAAQA,CAAA;IACN,IAAI,IAAI,CAACtC,UAAU,CAACuC,KAAK,IAAI,CAAC,IAAI,CAACzD,SAAS,EAAE;MAC5C,IAAI,CAACA,SAAS,GAAG,IAAI;MACrB,IAAI,CAAC5B,YAAY,GAAG,EAAE;MAEtB,MAAMsF,UAAU,GAAqB;QACnCtC,QAAQ,EAAE,IAAI,CAACF,UAAU,CAAC1B,KAAK,CAAC4B,QAAQ;QACxCE,iBAAiB,EAAE,IAAI,CAACJ,UAAU,CAAC1B,KAAK,CAAC8B,iBAAiB,CAACqC,IAAI,EAAE;QACjEnC,eAAe,EAAE,IAAI,CAACN,UAAU,CAAC1B,KAAK,CAACgC,eAAe,CAACmC,IAAI,EAAE;QAC7DlC,aAAa,EAAE,IAAI,CAACP,UAAU,CAAC1B,KAAK,CAACiC,aAAa;QAClDC,UAAU,EAAE,IAAI,CAACR,UAAU,CAAC1B,KAAK,CAACkC,UAAU;QAC5CkC,UAAU,EAAE;UACVrD,MAAM,EAAE,IAAI,CAACW,UAAU,CAAC1B,KAAK,CAACe,MAAM;UACpCM,QAAQ,EAAE,IAAI,CAACK,UAAU,CAAC1B,KAAK,CAACqB,QAAQ;UACxCC,OAAO,EAAE,IAAI,CAACI,UAAU,CAAC1B,KAAK,CAACsB;SAChC;QACDc,WAAW,EAAE,IAAI,CAACV,UAAU,CAAC1B,KAAK,CAACoC,WAAW;QAC9CC,iBAAiB,EAAE,IAAI,CAACX,UAAU,CAAC1B,KAAK,CAACqC,iBAAiB;QAC1DC,gBAAgB,EAAE,IAAI,CAACZ,UAAU,CAAC1B,KAAK,CAACsC;OACzC;MAED;MACA,IAAI+B,gBAAgB;MACpB,QAAQH,UAAU,CAACtC,QAAQ;QACzB,KAAK,QAAQ;UACXyC,gBAAgB,GAAG,IAAI,CAAC/D,aAAa,CAACgE,mBAAmB,CAACJ,UAAU,CAAC;UACrE;QACF,KAAK,WAAW;UACdG,gBAAgB,GAAG,IAAI,CAAC/D,aAAa,CAACiE,sBAAsB,CAACL,UAAU,CAAC;UACxE;QACF,KAAK,WAAW;UACdG,gBAAgB,GAAG,IAAI,CAAC/D,aAAa,CAACkE,sBAAsB,CAACN,UAAU,CAAC;UACxE;QACF;UACE,IAAI,CAAC1D,SAAS,GAAG,KAAK;UACtB,IAAI,CAAC5B,YAAY,GAAG,2BAA2B;UAC/C;;MAGJyF,gBAAgB,CAAC5B,SAAS,CAAC;QACzBgC,IAAI,EAAGC,QAAQ,IAAI;UACjB,IAAI,CAAClE,SAAS,GAAG,KAAK;UACtB,IAAIkE,QAAQ,CAACC,MAAM,CAACC,OAAO,EAAE;YAC3BC,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEJ,QAAQ,CAAC;YAC3C;YACA;WACD,MAAM;YACL,IAAI,CAACK,cAAc,CAACL,QAAQ,CAAC;;QAEjC,CAAC;QACDM,KAAK,EAAGA,KAAK,IAAI;UACf,IAAI,CAACxE,SAAS,GAAG,KAAK;UACtB,IAAI,CAAC5B,YAAY,GAAGoG,KAAK,CAACC,OAAO,IAAI,8CAA8C;UACnFJ,OAAO,CAACG,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;QAC9C;OACD,CAAC;KACH,MAAM;MACL,IAAI,CAACE,oBAAoB,EAAE;;EAE/B;EAEA;;;EAGQH,cAAcA,CAACL,QAAa;IAClC,IAAIA,QAAQ,CAACC,MAAM,CAACQ,QAAQ,IAAIT,QAAQ,CAACC,MAAM,CAACQ,QAAQ,CAACjE,MAAM,GAAG,CAAC,EAAE;MACnE,IAAI,CAACtC,YAAY,GAAG8F,QAAQ,CAACC,MAAM,CAACQ,QAAQ,CAAC,CAAC,CAAC,CAACF,OAAO;KACxD,MAAM;MACL,IAAI,CAACrG,YAAY,GAAG,oCAAoC;;EAE5D;EAEA;;;EAGQsG,oBAAoBA,CAAA;IAC1BE,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC3D,UAAU,CAACoB,QAAQ,CAAC,CAACwC,OAAO,CAACC,GAAG,IAAG;MAClD,MAAMC,OAAO,GAAG,IAAI,CAAC9D,UAAU,CAACa,GAAG,CAACgD,GAAG,CAAC;MACxCC,OAAO,EAAEC,aAAa,EAAE;IAC1B,CAAC,CAAC;EACJ;EAEA;;;EAGAlG,QAAQA,CAACmG,SAAiB,EAAEC,SAAiB;IAC3C,MAAMC,KAAK,GAAG,IAAI,CAAClE,UAAU,CAACa,GAAG,CAACmD,SAAS,CAAC;IAC5C,OAAO,CAAC,EAAEE,KAAK,EAAErG,QAAQ,CAACoG,SAAS,CAAC,IAAIC,KAAK,EAAElG,OAAO,CAAC;EACzD;EAEA;;;EAGAX,eAAeA,CAAC2G,SAAiB;IAC/B,MAAME,KAAK,GAAG,IAAI,CAAClE,UAAU,CAACa,GAAG,CAACmD,SAAS,CAAC;IAE5C,IAAIE,KAAK,EAAErG,QAAQ,CAAC,UAAU,CAAC,EAAE;MAC/B,OAAO,qBAAqB;;IAG9B,IAAIqG,KAAK,EAAErG,QAAQ,CAAC,WAAW,CAAC,EAAE;MAChC,MAAMsG,cAAc,GAAGD,KAAK,CAACjG,MAAM,GAAG,WAAW,CAAC,EAAEkG,cAAc;MAClE,OAAO,WAAWA,cAAc,oBAAoB;;IAGtD,IAAID,KAAK,EAAErG,QAAQ,CAAC,KAAK,CAAC,EAAE;MAC1B,MAAM4C,GAAG,GAAGyD,KAAK,CAACjG,MAAM,GAAG,KAAK,CAAC,EAAEwC,GAAG;MACtC,OAAO,yBAAyBA,GAAG,EAAE;;IAGvC,OAAO,EAAE;EACX;EAEA;;;EAGA3D,UAAUA,CAAA;IACR,IAAI,CAACI,YAAY,GAAG,EAAE;EACxB;;;uBA9PWuB,qBAAqB,EAAAzC,EAAA,CAAAoI,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAtI,EAAA,CAAAoI,iBAAA,CAAAG,EAAA,CAAAC,aAAA,GAAAxI,EAAA,CAAAoI,iBAAA,CAAAK,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAArBjG,qBAAqB;MAAAkG,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCblCjJ,EAAA,CAAAC,cAAA,aAAqC;UAK7BD,EAAA,CAAAE,cAAA,EAA6C;UAA7CF,EAAA,CAAAC,cAAA,aAA6C;UAC3CD,EAAA,CAAAG,SAAA,gBAAgC;UAClCH,EAAA,CAAAI,YAAA,EAAM;UAERJ,EAAA,CAAAK,eAAA,EAAkC;UAAlCL,EAAA,CAAAC,cAAA,aAAkC;UACPD,EAAA,CAAAM,MAAA,8BAAuB;UAAAN,EAAA,CAAAI,YAAA,EAAK;UACrDJ,EAAA,CAAAC,cAAA,WAA2B;UAAAD,EAAA,CAAAM,MAAA,iDAAyC;UAAAN,EAAA,CAAAI,YAAA,EAAI;UAI5EJ,EAAA,CAAAC,cAAA,cAA6B;UACFD,EAAA,CAAAM,MAAA,uBAAe;UAAAN,EAAA,CAAAI,YAAA,EAAK;UAC7CJ,EAAA,CAAAC,cAAA,aAA2B;UAAAD,EAAA,CAAAM,MAAA,iDAAyC;UAAAN,EAAA,CAAAI,YAAA,EAAI;UAK5EJ,EAAA,CAAAC,cAAA,eAA4B;UAGxBD,EAAA,CAAAyB,UAAA,KAAA0H,qCAAA,kBAUM;UAENnJ,EAAA,CAAAC,cAAA,gBAAsF;UAAvDD,EAAA,CAAAO,UAAA,sBAAA6I,yDAAA;YAAA,OAAYF,GAAA,CAAA5C,QAAA,EAAU;UAAA,EAAC;UAGpDtG,EAAA,CAAAC,cAAA,eAA4B;UAIlBD,EAAA,CAAAO,UAAA,mBAAA8I,wDAAA;YAAA,OAASH,GAAA,CAAAlF,UAAA,CAAAwB,UAAA;cAAAtB,QAAA,EAAiC;YAAQ,EAAE;UAAA,EAAC;UAC3DlE,EAAA,CAAAM,MAAA,iBACF;UAAAN,EAAA,CAAAI,YAAA,EAAS;UACTJ,EAAA,CAAAC,cAAA,kBAGiE;UAAzDD,EAAA,CAAAO,UAAA,mBAAA+I,wDAAA;YAAA,OAASJ,GAAA,CAAAlF,UAAA,CAAAwB,UAAA;cAAAtB,QAAA,EAAiC;YAAW,EAAE;UAAA,EAAC;UAC9DlE,EAAA,CAAAM,MAAA,oBACF;UAAAN,EAAA,CAAAI,YAAA,EAAS;UACTJ,EAAA,CAAAC,cAAA,kBAGiE;UAAzDD,EAAA,CAAAO,UAAA,mBAAAgJ,wDAAA;YAAA,OAASL,GAAA,CAAAlF,UAAA,CAAAwB,UAAA;cAAAtB,QAAA,EAAiC;YAAW,EAAE;UAAA,EAAC;UAC9DlE,EAAA,CAAAM,MAAA,+BACF;UAAAN,EAAA,CAAAI,YAAA,EAAS;UAIXJ,EAAA,CAAAC,cAAA,eAA0B;UAEQD,EAAA,CAAAM,MAAA,YAAI;UAAAN,EAAA,CAAAI,YAAA,EAAQ;UAC1CJ,EAAA,CAAAC,cAAA,eAAsC;UACpCD,EAAA,CAAAE,cAAA,EAAiF;UAAjFF,EAAA,CAAAC,cAAA,eAAiF;UAC/ED,EAAA,CAAAG,SAAA,gBAA4G;UAC9GH,EAAA,CAAAI,YAAA,EAAM;UACNJ,EAAA,CAAAK,eAAA,EAI4C;UAJ5CL,EAAA,CAAAG,SAAA,iBAI4C;UAC5CH,EAAA,CAAAC,cAAA,kBAA8C;UAC5CD,EAAA,CAAAE,cAAA,EAA2D;UAA3DF,EAAA,CAAAC,cAAA,eAA2D;UACzDD,EAAA,CAAAG,SAAA,gBAA0F;UAC5FH,EAAA,CAAAI,YAAA,EAAM;UAGVJ,EAAA,CAAAyB,UAAA,KAAA+H,qCAAA,kBAEM;UACRxJ,EAAA,CAAAI,YAAA,EAAM;UAGNJ,EAAA,CAAAK,eAAA,EAAmE;UAAnEL,EAAA,CAAAC,cAAA,kBAAmE;UAAzBD,EAAA,CAAAO,UAAA,mBAAAkJ,wDAAA;YAAA,OAASP,GAAA,CAAA7D,YAAA,EAAc;UAAA,EAAC;UAChErF,EAAA,CAAAE,cAAA,EAA2D;UAA3DF,EAAA,CAAAC,cAAA,eAA2D;UACzDD,EAAA,CAAAG,SAAA,gBAA4H;UAC9HH,EAAA,CAAAI,YAAA,EAAM;UAGRJ,EAAA,CAAAK,eAAA,EAA4B;UAA5BL,EAAA,CAAAC,cAAA,eAA4B;UACID,EAAA,CAAAM,MAAA,UAAE;UAAAN,EAAA,CAAAI,YAAA,EAAQ;UACxCJ,EAAA,CAAAC,cAAA,eAAsC;UACpCD,EAAA,CAAAE,cAAA,EAAiF;UAAjFF,EAAA,CAAAC,cAAA,eAAiF;UAC/ED,EAAA,CAAAG,SAAA,gBAA8J;UAEhKH,EAAA,CAAAI,YAAA,EAAM;UACNJ,EAAA,CAAAK,eAAA,EAIyC;UAJzCL,EAAA,CAAAG,SAAA,iBAIyC;UACzCH,EAAA,CAAAC,cAAA,kBAA8C;UAC5CD,EAAA,CAAAE,cAAA,EAA2D;UAA3DF,EAAA,CAAAC,cAAA,eAA2D;UACzDD,EAAA,CAAAG,SAAA,gBAA0F;UAC5FH,EAAA,CAAAI,YAAA,EAAM;UAGVJ,EAAA,CAAAyB,UAAA,KAAAiI,qCAAA,kBAEM;UACR1J,EAAA,CAAAI,YAAA,EAAM;UAIRJ,EAAA,CAAAK,eAAA,EAAsB;UAAtBL,EAAA,CAAAC,cAAA,eAAsB;UAEQD,EAAA,CAAAM,MAAA,YAAI;UAAAN,EAAA,CAAAI,YAAA,EAAQ;UACtCJ,EAAA,CAAAG,SAAA,iBAG6D;UAC7DH,EAAA,CAAAyB,UAAA,KAAAkI,qCAAA,kBAEM;UACR3J,EAAA,CAAAI,YAAA,EAAM;UAENJ,EAAA,CAAAyB,UAAA,KAAAmI,qCAAA,kBASM;UACR5J,EAAA,CAAAI,YAAA,EAAM;UAGNJ,EAAA,CAAAC,cAAA,eAAiC;UAEED,EAAA,CAAAM,MAAA,mCAA2B;UAAAN,EAAA,CAAAI,YAAA,EAAQ;UAClEJ,EAAA,CAAAC,cAAA,eAA+B;UAGzBD,EAAA,CAAAE,cAAA,EAAkF;UAAlFF,EAAA,CAAAC,cAAA,eAAkF;UAChFD,EAAA,CAAAG,SAAA,gBAA+I;UACjJH,EAAA,CAAAI,YAAA,EAAM;UACNJ,EAAA,CAAAK,eAAA,EAA0D;UAA1DL,EAAA,CAAAC,cAAA,kBAA0D;UACxDD,EAAA,CAAAyB,UAAA,KAAAoI,wCAAA,qBAAyF;UAC3F7J,EAAA,CAAAI,YAAA,EAAS;UAEXJ,EAAA,CAAAC,cAAA,eAA6B;UAC3BD,EAAA,CAAAE,cAAA,EAAkF;UAAlFF,EAAA,CAAAC,cAAA,eAAkF;UAChFD,EAAA,CAAAG,SAAA,gBAAkV;UACpVH,EAAA,CAAAI,YAAA,EAAM;UACNJ,EAAA,CAAAK,eAAA,EAA4D;UAA5DL,EAAA,CAAAC,cAAA,kBAA4D;UAC1DD,EAAA,CAAAyB,UAAA,KAAAqI,wCAAA,qBAA2F;UAC7F9J,EAAA,CAAAI,YAAA,EAAS;UAEXJ,EAAA,CAAAC,cAAA,eAA6B;UAC3BD,EAAA,CAAAE,cAAA,EAAkF;UAAlFF,EAAA,CAAAC,cAAA,eAAkF;UAChFD,EAAA,CAAAG,SAAA,gBAAuM;UACzMH,EAAA,CAAAI,YAAA,EAAM;UACNJ,EAAA,CAAAK,eAAA,EAA2D;UAA3DL,EAAA,CAAAC,cAAA,kBAA2D;UACzDD,EAAA,CAAAyB,UAAA,KAAAsI,wCAAA,qBAA0F;UAC5F/J,EAAA,CAAAI,YAAA,EAAS;UAEXJ,EAAA,CAAAC,cAAA,eAAyB;UACvBD,EAAA,CAAAE,cAAA,EAA8E;UAA9EF,EAAA,CAAAC,cAAA,eAA8E;UAC5ED,EAAA,CAAAG,SAAA,gBAAuH;UACzHH,EAAA,CAAAI,YAAA,EAAM;UACNJ,EAAA,CAAAK,eAAA,EAA2D;UAA3DL,EAAA,CAAAC,cAAA,kBAA2D;UACzDD,EAAA,CAAAyB,UAAA,KAAAuI,wCAAA,qBAA8G;UAChHhK,EAAA,CAAAI,YAAA,EAAS;UAMjBJ,EAAA,CAAAC,cAAA,eAA2B;UACID,EAAA,CAAAM,MAAA,yBAAiB;UAAAN,EAAA,CAAAI,YAAA,EAAQ;UACtDJ,EAAA,CAAAC,cAAA,kBAAkE;UAC/CD,EAAA,CAAAM,MAAA,yBAAiB;UAAAN,EAAA,CAAAI,YAAA,EAAS;UAC3CJ,EAAA,CAAAyB,UAAA,KAAAwI,wCAAA,qBAA0F;UAC5FjK,EAAA,CAAAI,YAAA,EAAS;UAKbJ,EAAA,CAAAC,cAAA,eAAyB;UAEOD,EAAA,CAAAM,MAAA,wBAAgB;UAAAN,EAAA,CAAAI,YAAA,EAAQ;UACpDJ,EAAA,CAAAC,cAAA,kBAA8B;UACpBD,EAAA,CAAAM,MAAA,gBAAO;UAAAN,EAAA,CAAAI,YAAA,EAAS;UAI5BJ,EAAA,CAAAC,cAAA,gBAA0B;UACID,EAAA,CAAAM,MAAA,gBAAO;UAAAN,EAAA,CAAAI,YAAA,EAAQ;UAC3CJ,EAAA,CAAAC,cAAA,mBAA8B;UACpBD,EAAA,CAAAM,MAAA,gBAAO;UAAAN,EAAA,CAAAI,YAAA,EAAS;UAI5BJ,EAAA,CAAAC,cAAA,gBAA0B;UACID,EAAA,CAAAM,MAAA,iBAAQ;UAAAN,EAAA,CAAAI,YAAA,EAAQ;UAC5CJ,EAAA,CAAAC,cAAA,gBAA8B;UAAAD,EAAA,CAAAM,MAAA,KAA2C;UAAAN,EAAA,CAAAI,YAAA,EAAM;UAKnFJ,EAAA,CAAAC,cAAA,mBAGoC;UAClCD,EAAA,CAAAyB,UAAA,MAAAyI,uCAAA,mBAA0C;UAC1ClK,EAAA,CAAAyB,UAAA,MAAA0I,uCAAA,mBAMO;UACTnK,EAAA,CAAAI,YAAA,EAAS;UAKbJ,EAAA,CAAAC,cAAA,gBAAqC;UAGVD,EAAA,CAAAM,MAAA,2BAAkB;UAAAN,EAAA,CAAAI,YAAA,EAAI;;;UAlNzCJ,EAAA,CAAAe,SAAA,IAAkB;UAAlBf,EAAA,CAAA8B,UAAA,SAAAoH,GAAA,CAAAhI,YAAA,CAAkB;UAYlBlB,EAAA,CAAAe,SAAA,GAAwB;UAAxBf,EAAA,CAAA8B,UAAA,cAAAoH,GAAA,CAAAlF,UAAA,CAAwB;UAMlBhE,EAAA,CAAAe,SAAA,GAA4D;UAA5Df,EAAA,CAAA2B,WAAA,WAAAuH,GAAA,CAAAnH,YAAA,aAAAO,KAAA,cAA4D;UAM5DtC,EAAA,CAAAe,SAAA,GAA+D;UAA/Df,EAAA,CAAA2B,WAAA,WAAAuH,GAAA,CAAAnH,YAAA,aAAAO,KAAA,iBAA+D;UAM/DtC,EAAA,CAAAe,SAAA,GAA+D;UAA/Df,EAAA,CAAA2B,WAAA,WAAAuH,GAAA,CAAAnH,YAAA,aAAAO,KAAA,iBAA+D;UAiB5DtC,EAAA,CAAAe,SAAA,GAAuG;UAAvGf,EAAA,CAAA2B,WAAA,UAAAuH,GAAA,CAAArH,QAAA,qCAAAqH,GAAA,CAAArH,QAAA,mCAAuG;UAQ1G7B,EAAA,CAAAe,SAAA,GAA2F;UAA3Ff,EAAA,CAAA8B,UAAA,SAAAoH,GAAA,CAAAnH,YAAA,sBAAAC,OAAA,IAAAkH,GAAA,CAAAnH,YAAA,sBAAAE,MAAA,CAA2F;UAsBxFjC,EAAA,CAAAe,SAAA,IAAmG;UAAnGf,EAAA,CAAA2B,WAAA,UAAAuH,GAAA,CAAArH,QAAA,mCAAAqH,GAAA,CAAArH,QAAA,iCAAmG;UAQtG7B,EAAA,CAAAe,SAAA,GAAuF;UAAvFf,EAAA,CAAA8B,UAAA,SAAAoH,GAAA,CAAAnH,YAAA,oBAAAC,OAAA,IAAAkH,GAAA,CAAAnH,YAAA,oBAAAE,MAAA,CAAuF;UAatFjC,EAAA,CAAAe,SAAA,GAAqD;UAArDf,EAAA,CAAA2B,WAAA,UAAAuH,GAAA,CAAArH,QAAA,8BAAqD;UACtD7B,EAAA,CAAAe,SAAA,GAAmF;UAAnFf,EAAA,CAAA8B,UAAA,SAAAoH,GAAA,CAAAnH,YAAA,kBAAAC,OAAA,IAAAkH,GAAA,CAAAnH,YAAA,kBAAAE,MAAA,CAAmF;UAKlEjC,EAAA,CAAAe,SAAA,GAAoD;UAApDf,EAAA,CAAA8B,UAAA,SAAAoH,GAAA,CAAAnH,YAAA,aAAAO,KAAA,iBAAoD;UAuBzCtC,EAAA,CAAAe,SAAA,IAAyB;UAAzBf,EAAA,CAAA8B,UAAA,YAAAoH,GAAA,CAAA9F,eAAA,CAAAC,MAAA,CAAyB;UAQzBrD,EAAA,CAAAe,SAAA,GAA2B;UAA3Bf,EAAA,CAAA8B,UAAA,YAAAoH,GAAA,CAAA9F,eAAA,CAAAO,QAAA,CAA2B;UAQ3B3D,EAAA,CAAAe,SAAA,GAA0B;UAA1Bf,EAAA,CAAA8B,UAAA,YAAAoH,GAAA,CAAA9F,eAAA,CAAAQ,OAAA,CAA0B;UAQpB5D,EAAA,CAAAe,SAAA,GAAgB;UAAhBf,EAAA,CAAA8B,UAAA,YAAAoH,GAAA,CAAAnG,aAAA,CAAgB;UAW1B/C,EAAA,CAAAe,SAAA,GAAoB;UAApBf,EAAA,CAAA8B,UAAA,YAAAoH,GAAA,CAAArF,iBAAA,CAAoB;UAuBpB7D,EAAA,CAAAe,SAAA,IAA2C;UAA3Cf,EAAA,CAAAgB,iBAAA,CAAAkI,GAAA,CAAAtD,mBAAA,mBAA2C;UAQrE5F,EAAA,CAAAe,SAAA,GAA2B;UAA3Bf,EAAA,CAAA2B,WAAA,YAAAuH,GAAA,CAAApG,SAAA,CAA2B;UAD3B9C,EAAA,CAAA8B,UAAA,aAAAoH,GAAA,CAAApG,SAAA,IAAAoG,GAAA,CAAAlF,UAAA,CAAAoG,OAAA,CAA4C;UAE3CpK,EAAA,CAAAe,SAAA,GAAgB;UAAhBf,EAAA,CAAA8B,UAAA,UAAAoH,GAAA,CAAApG,SAAA,CAAgB;UAChB9C,EAAA,CAAAe,SAAA,GAAe;UAAff,EAAA,CAAA8B,UAAA,SAAAoH,GAAA,CAAApG,SAAA,CAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}