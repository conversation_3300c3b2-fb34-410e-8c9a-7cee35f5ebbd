{"ast": null, "code": "/**\n * @license Angular v16.2.12\n * (c) 2010-2022 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport { CompilerConfig, ResourceLoader } from '@angular/compiler';\nimport * as i0 from '@angular/core';\nimport { Compiler, ViewEncapsulation, MissingTranslationStrategy, Injector, createPlatformFactory, platformCore, COMPILER_OPTIONS, CompilerFactory, Injectable, PLATFORM_ID, ɵglobal, Version } from '@angular/core';\nimport { ɵPLATFORM_BROWSER_ID } from '@angular/common';\nimport { ɵINTERNAL_BROWSER_PLATFORM_PROVIDERS } from '@angular/platform-browser';\nconst COMPILER_PROVIDERS = [{\n  provide: Compiler,\n  useFactory: () => new Compiler()\n}];\n/**\n * @publicApi\n *\n * @deprecated\n * Ivy JIT mode doesn't require accessing this symbol.\n * See [JIT API changes due to ViewEngine deprecation](guide/deprecations#jit-api-changes) for\n * additional context.\n */\nclass JitCompilerFactory {\n  /** @internal */\n  constructor(defaultOptions) {\n    const compilerOptions = {\n      useJit: true,\n      defaultEncapsulation: ViewEncapsulation.Emulated,\n      missingTranslation: MissingTranslationStrategy.Warning\n    };\n    this._defaultOptions = [compilerOptions, ...defaultOptions];\n  }\n  createCompiler(options = []) {\n    const opts = _mergeOptions(this._defaultOptions.concat(options));\n    const injector = Injector.create({\n      providers: [COMPILER_PROVIDERS, {\n        provide: CompilerConfig,\n        useFactory: () => {\n          return new CompilerConfig({\n            // let explicit values from the compiler options overwrite options\n            // from the app providers\n            useJit: opts.useJit,\n            // let explicit values from the compiler options overwrite options\n            // from the app providers\n            defaultEncapsulation: opts.defaultEncapsulation,\n            missingTranslation: opts.missingTranslation,\n            preserveWhitespaces: opts.preserveWhitespaces\n          });\n        },\n        deps: []\n      }, opts.providers]\n    });\n    return injector.get(Compiler);\n  }\n}\nfunction _mergeOptions(optionsArr) {\n  return {\n    useJit: _lastDefined(optionsArr.map(options => options.useJit)),\n    defaultEncapsulation: _lastDefined(optionsArr.map(options => options.defaultEncapsulation)),\n    providers: _mergeArrays(optionsArr.map(options => options.providers)),\n    missingTranslation: _lastDefined(optionsArr.map(options => options.missingTranslation)),\n    preserveWhitespaces: _lastDefined(optionsArr.map(options => options.preserveWhitespaces))\n  };\n}\nfunction _lastDefined(args) {\n  for (let i = args.length - 1; i >= 0; i--) {\n    if (args[i] !== undefined) {\n      return args[i];\n    }\n  }\n  return undefined;\n}\nfunction _mergeArrays(parts) {\n  const result = [];\n  parts.forEach(part => part && result.push(...part));\n  return result;\n}\n\n/**\n * A platform that included corePlatform and the compiler.\n *\n * @publicApi\n */\nconst platformCoreDynamic = createPlatformFactory(platformCore, 'coreDynamic', [{\n  provide: COMPILER_OPTIONS,\n  useValue: {},\n  multi: true\n}, {\n  provide: CompilerFactory,\n  useClass: JitCompilerFactory,\n  deps: [COMPILER_OPTIONS]\n}]);\nclass ResourceLoaderImpl extends ResourceLoader {\n  get(url) {\n    let resolve;\n    let reject;\n    const promise = new Promise((res, rej) => {\n      resolve = res;\n      reject = rej;\n    });\n    const xhr = new XMLHttpRequest();\n    xhr.open('GET', url, true);\n    xhr.responseType = 'text';\n    xhr.onload = function () {\n      const response = xhr.response;\n      let status = xhr.status;\n      // fix status code when it is 0 (0 status is undocumented).\n      // Occurs when accessing file resources or on Android 4.1 stock browser\n      // while retrieving files from application cache.\n      if (status === 0) {\n        status = response ? 200 : 0;\n      }\n      if (200 <= status && status <= 300) {\n        resolve(response);\n      } else {\n        reject(`Failed to load ${url}`);\n      }\n    };\n    xhr.onerror = function () {\n      reject(`Failed to load ${url}`);\n    };\n    xhr.send();\n    return promise;\n  }\n  static {\n    this.ɵfac = /* @__PURE__ */function () {\n      let ɵResourceLoaderImpl_BaseFactory;\n      return function ResourceLoaderImpl_Factory(t) {\n        return (ɵResourceLoaderImpl_BaseFactory || (ɵResourceLoaderImpl_BaseFactory = i0.ɵɵgetInheritedFactory(ResourceLoaderImpl)))(t || ResourceLoaderImpl);\n      };\n    }();\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: ResourceLoaderImpl,\n      factory: ResourceLoaderImpl.ɵfac\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ResourceLoaderImpl, [{\n    type: Injectable\n  }], null, null);\n})();\n\n/**\n * @publicApi\n */\nconst INTERNAL_BROWSER_DYNAMIC_PLATFORM_PROVIDERS = [ɵINTERNAL_BROWSER_PLATFORM_PROVIDERS, {\n  provide: COMPILER_OPTIONS,\n  useValue: {\n    providers: [{\n      provide: ResourceLoader,\n      useClass: ResourceLoaderImpl,\n      deps: []\n    }]\n  },\n  multi: true\n}, {\n  provide: PLATFORM_ID,\n  useValue: ɵPLATFORM_BROWSER_ID\n}];\n\n/**\n * An implementation of ResourceLoader that uses a template cache to avoid doing an actual\n * ResourceLoader.\n *\n * The template cache needs to be built and loaded into window.$templateCache\n * via a separate mechanism.\n *\n * @publicApi\n *\n * @deprecated This was previously necessary in some cases to test AOT-compiled components with View\n *     Engine, but is no longer since Ivy.\n */\nclass CachedResourceLoader extends ResourceLoader {\n  constructor() {\n    super();\n    this._cache = ɵglobal.$templateCache;\n    if (this._cache == null) {\n      throw new Error('CachedResourceLoader: Template cache was not found in $templateCache.');\n    }\n  }\n  get(url) {\n    if (this._cache.hasOwnProperty(url)) {\n      return Promise.resolve(this._cache[url]);\n    } else {\n      return Promise.reject('CachedResourceLoader: Did not find cached template for ' + url);\n    }\n  }\n}\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of the platform-browser-dynamic package.\n */\n/**\n * @publicApi\n */\nconst VERSION = new Version('16.2.12');\n\n/**\n * @publicApi\n *\n * @deprecated This was previously necessary in some cases to test AOT-compiled components with View\n *     Engine, but is no longer since Ivy.\n\n */\nconst RESOURCE_CACHE_PROVIDER = [{\n  provide: ResourceLoader,\n  useClass: CachedResourceLoader,\n  deps: []\n}];\n/**\n * @publicApi\n */\nconst platformBrowserDynamic = createPlatformFactory(platformCoreDynamic, 'browserDynamic', INTERNAL_BROWSER_DYNAMIC_PLATFORM_PROVIDERS);\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of this package.\n */\n// This file only reexports content of the `src` folder. Keep it that way.\n\n// This file is not used to build this module. It is only used during editing\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { JitCompilerFactory, RESOURCE_CACHE_PROVIDER, VERSION, platformBrowserDynamic, INTERNAL_BROWSER_DYNAMIC_PLATFORM_PROVIDERS as ɵINTERNAL_BROWSER_DYNAMIC_PLATFORM_PROVIDERS, platformCoreDynamic as ɵplatformCoreDynamic };", "map": {"version": 3, "names": ["CompilerConfig", "Resource<PERSON><PERSON>der", "i0", "Compiler", "ViewEncapsulation", "MissingTranslationStrategy", "Injector", "createPlatformFactory", "platformCore", "COMPILER_OPTIONS", "CompilerFactory", "Injectable", "PLATFORM_ID", "ɵglobal", "Version", "ɵPLATFORM_BROWSER_ID", "ɵINTERNAL_BROWSER_PLATFORM_PROVIDERS", "COMPILER_PROVIDERS", "provide", "useFactory", "JitCompilerFactory", "constructor", "defaultOptions", "compilerOptions", "useJit", "defaultEncapsulation", "Emulated", "missingTranslation", "Warning", "_defaultOptions", "createCompiler", "options", "opts", "_mergeOptions", "concat", "injector", "create", "providers", "preserveWhitespaces", "deps", "get", "optionsArr", "_lastDefined", "map", "_mergeArrays", "args", "i", "length", "undefined", "parts", "result", "for<PERSON>ach", "part", "push", "platformCoreDynamic", "useValue", "multi", "useClass", "ResourceLoaderImpl", "url", "resolve", "reject", "promise", "Promise", "res", "rej", "xhr", "XMLHttpRequest", "open", "responseType", "onload", "response", "status", "onerror", "send", "ɵfac", "ɵResourceLoaderImpl_BaseFactory", "ResourceLoaderImpl_Factory", "t", "ɵɵgetInheritedFactory", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "ngDevMode", "ɵsetClassMetadata", "type", "INTERNAL_BROWSER_DYNAMIC_PLATFORM_PROVIDERS", "CachedResourceLoader", "_cache", "$templateCache", "Error", "hasOwnProperty", "VERSION", "RESOURCE_CACHE_PROVIDER", "platformBrowserDynamic", "ɵINTERNAL_BROWSER_DYNAMIC_PLATFORM_PROVIDERS", "ɵplatformCoreDynamic"], "sources": ["C:/Users/<USER>/Desktop/angular/front/node_modules/@angular/platform-browser-dynamic/fesm2022/platform-browser-dynamic.mjs"], "sourcesContent": ["/**\n * @license Angular v16.2.12\n * (c) 2010-2022 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport { CompilerConfig, ResourceLoader } from '@angular/compiler';\nimport * as i0 from '@angular/core';\nimport { Compiler, ViewEncapsulation, MissingTranslationStrategy, Injector, createPlatformFactory, platformCore, COMPILER_OPTIONS, CompilerFactory, Injectable, PLATFORM_ID, ɵglobal, Version } from '@angular/core';\nimport { ɵPLATFORM_BROWSER_ID } from '@angular/common';\nimport { ɵINTERNAL_BROWSER_PLATFORM_PROVIDERS } from '@angular/platform-browser';\n\nconst COMPILER_PROVIDERS = [{ provide: Compiler, useFactory: () => new Compiler() }];\n/**\n * @publicApi\n *\n * @deprecated\n * Ivy JIT mode doesn't require accessing this symbol.\n * See [JIT API changes due to ViewEngine deprecation](guide/deprecations#jit-api-changes) for\n * additional context.\n */\nclass JitCompilerFactory {\n    /** @internal */\n    constructor(defaultOptions) {\n        const compilerOptions = {\n            useJit: true,\n            defaultEncapsulation: ViewEncapsulation.Emulated,\n            missingTranslation: MissingTranslationStrategy.Warning,\n        };\n        this._defaultOptions = [compilerOptions, ...defaultOptions];\n    }\n    createCompiler(options = []) {\n        const opts = _mergeOptions(this._defaultOptions.concat(options));\n        const injector = Injector.create({\n            providers: [\n                COMPILER_PROVIDERS, {\n                    provide: CompilerConfig,\n                    useFactory: () => {\n                        return new CompilerConfig({\n                            // let explicit values from the compiler options overwrite options\n                            // from the app providers\n                            useJit: opts.useJit,\n                            // let explicit values from the compiler options overwrite options\n                            // from the app providers\n                            defaultEncapsulation: opts.defaultEncapsulation,\n                            missingTranslation: opts.missingTranslation,\n                            preserveWhitespaces: opts.preserveWhitespaces,\n                        });\n                    },\n                    deps: []\n                },\n                opts.providers\n            ]\n        });\n        return injector.get(Compiler);\n    }\n}\nfunction _mergeOptions(optionsArr) {\n    return {\n        useJit: _lastDefined(optionsArr.map(options => options.useJit)),\n        defaultEncapsulation: _lastDefined(optionsArr.map(options => options.defaultEncapsulation)),\n        providers: _mergeArrays(optionsArr.map(options => options.providers)),\n        missingTranslation: _lastDefined(optionsArr.map(options => options.missingTranslation)),\n        preserveWhitespaces: _lastDefined(optionsArr.map(options => options.preserveWhitespaces)),\n    };\n}\nfunction _lastDefined(args) {\n    for (let i = args.length - 1; i >= 0; i--) {\n        if (args[i] !== undefined) {\n            return args[i];\n        }\n    }\n    return undefined;\n}\nfunction _mergeArrays(parts) {\n    const result = [];\n    parts.forEach((part) => part && result.push(...part));\n    return result;\n}\n\n/**\n * A platform that included corePlatform and the compiler.\n *\n * @publicApi\n */\nconst platformCoreDynamic = createPlatformFactory(platformCore, 'coreDynamic', [\n    { provide: COMPILER_OPTIONS, useValue: {}, multi: true },\n    { provide: CompilerFactory, useClass: JitCompilerFactory, deps: [COMPILER_OPTIONS] },\n]);\n\nclass ResourceLoaderImpl extends ResourceLoader {\n    get(url) {\n        let resolve;\n        let reject;\n        const promise = new Promise((res, rej) => {\n            resolve = res;\n            reject = rej;\n        });\n        const xhr = new XMLHttpRequest();\n        xhr.open('GET', url, true);\n        xhr.responseType = 'text';\n        xhr.onload = function () {\n            const response = xhr.response;\n            let status = xhr.status;\n            // fix status code when it is 0 (0 status is undocumented).\n            // Occurs when accessing file resources or on Android 4.1 stock browser\n            // while retrieving files from application cache.\n            if (status === 0) {\n                status = response ? 200 : 0;\n            }\n            if (200 <= status && status <= 300) {\n                resolve(response);\n            }\n            else {\n                reject(`Failed to load ${url}`);\n            }\n        };\n        xhr.onerror = function () {\n            reject(`Failed to load ${url}`);\n        };\n        xhr.send();\n        return promise;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: ResourceLoaderImpl, deps: null, target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: ResourceLoaderImpl }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: ResourceLoaderImpl, decorators: [{\n            type: Injectable\n        }] });\n\n/**\n * @publicApi\n */\nconst INTERNAL_BROWSER_DYNAMIC_PLATFORM_PROVIDERS = [\n    ɵINTERNAL_BROWSER_PLATFORM_PROVIDERS,\n    {\n        provide: COMPILER_OPTIONS,\n        useValue: { providers: [{ provide: ResourceLoader, useClass: ResourceLoaderImpl, deps: [] }] },\n        multi: true\n    },\n    { provide: PLATFORM_ID, useValue: ɵPLATFORM_BROWSER_ID },\n];\n\n/**\n * An implementation of ResourceLoader that uses a template cache to avoid doing an actual\n * ResourceLoader.\n *\n * The template cache needs to be built and loaded into window.$templateCache\n * via a separate mechanism.\n *\n * @publicApi\n *\n * @deprecated This was previously necessary in some cases to test AOT-compiled components with View\n *     Engine, but is no longer since Ivy.\n */\nclass CachedResourceLoader extends ResourceLoader {\n    constructor() {\n        super();\n        this._cache = ɵglobal.$templateCache;\n        if (this._cache == null) {\n            throw new Error('CachedResourceLoader: Template cache was not found in $templateCache.');\n        }\n    }\n    get(url) {\n        if (this._cache.hasOwnProperty(url)) {\n            return Promise.resolve(this._cache[url]);\n        }\n        else {\n            return Promise.reject('CachedResourceLoader: Did not find cached template for ' + url);\n        }\n    }\n}\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of the platform-browser-dynamic package.\n */\n/**\n * @publicApi\n */\nconst VERSION = new Version('16.2.12');\n\n/**\n * @publicApi\n *\n * @deprecated This was previously necessary in some cases to test AOT-compiled components with View\n *     Engine, but is no longer since Ivy.\n\n */\nconst RESOURCE_CACHE_PROVIDER = [{ provide: ResourceLoader, useClass: CachedResourceLoader, deps: [] }];\n/**\n * @publicApi\n */\nconst platformBrowserDynamic = createPlatformFactory(platformCoreDynamic, 'browserDynamic', INTERNAL_BROWSER_DYNAMIC_PLATFORM_PROVIDERS);\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of this package.\n */\n// This file only reexports content of the `src` folder. Keep it that way.\n\n// This file is not used to build this module. It is only used during editing\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { JitCompilerFactory, RESOURCE_CACHE_PROVIDER, VERSION, platformBrowserDynamic, INTERNAL_BROWSER_DYNAMIC_PLATFORM_PROVIDERS as ɵINTERNAL_BROWSER_DYNAMIC_PLATFORM_PROVIDERS, platformCoreDynamic as ɵplatformCoreDynamic };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA,SAASA,cAAc,EAAEC,cAAc,QAAQ,mBAAmB;AAClE,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,QAAQ,EAAEC,iBAAiB,EAAEC,0BAA0B,EAAEC,QAAQ,EAAEC,qBAAqB,EAAEC,YAAY,EAAEC,gBAAgB,EAAEC,eAAe,EAAEC,UAAU,EAAEC,WAAW,EAAEC,OAAO,EAAEC,OAAO,QAAQ,eAAe;AACpN,SAASC,oBAAoB,QAAQ,iBAAiB;AACtD,SAASC,oCAAoC,QAAQ,2BAA2B;AAEhF,MAAMC,kBAAkB,GAAG,CAAC;EAAEC,OAAO,EAAEf,QAAQ;EAAEgB,UAAU,EAAEA,CAAA,KAAM,IAAIhB,QAAQ,CAAC;AAAE,CAAC,CAAC;AACpF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMiB,kBAAkB,CAAC;EACrB;EACAC,WAAWA,CAACC,cAAc,EAAE;IACxB,MAAMC,eAAe,GAAG;MACpBC,MAAM,EAAE,IAAI;MACZC,oBAAoB,EAAErB,iBAAiB,CAACsB,QAAQ;MAChDC,kBAAkB,EAAEtB,0BAA0B,CAACuB;IACnD,CAAC;IACD,IAAI,CAACC,eAAe,GAAG,CAACN,eAAe,EAAE,GAAGD,cAAc,CAAC;EAC/D;EACAQ,cAAcA,CAACC,OAAO,GAAG,EAAE,EAAE;IACzB,MAAMC,IAAI,GAAGC,aAAa,CAAC,IAAI,CAACJ,eAAe,CAACK,MAAM,CAACH,OAAO,CAAC,CAAC;IAChE,MAAMI,QAAQ,GAAG7B,QAAQ,CAAC8B,MAAM,CAAC;MAC7BC,SAAS,EAAE,CACPpB,kBAAkB,EAAE;QAChBC,OAAO,EAAElB,cAAc;QACvBmB,UAAU,EAAEA,CAAA,KAAM;UACd,OAAO,IAAInB,cAAc,CAAC;YACtB;YACA;YACAwB,MAAM,EAAEQ,IAAI,CAACR,MAAM;YACnB;YACA;YACAC,oBAAoB,EAAEO,IAAI,CAACP,oBAAoB;YAC/CE,kBAAkB,EAAEK,IAAI,CAACL,kBAAkB;YAC3CW,mBAAmB,EAAEN,IAAI,CAACM;UAC9B,CAAC,CAAC;QACN,CAAC;QACDC,IAAI,EAAE;MACV,CAAC,EACDP,IAAI,CAACK,SAAS;IAEtB,CAAC,CAAC;IACF,OAAOF,QAAQ,CAACK,GAAG,CAACrC,QAAQ,CAAC;EACjC;AACJ;AACA,SAAS8B,aAAaA,CAACQ,UAAU,EAAE;EAC/B,OAAO;IACHjB,MAAM,EAAEkB,YAAY,CAACD,UAAU,CAACE,GAAG,CAACZ,OAAO,IAAIA,OAAO,CAACP,MAAM,CAAC,CAAC;IAC/DC,oBAAoB,EAAEiB,YAAY,CAACD,UAAU,CAACE,GAAG,CAACZ,OAAO,IAAIA,OAAO,CAACN,oBAAoB,CAAC,CAAC;IAC3FY,SAAS,EAAEO,YAAY,CAACH,UAAU,CAACE,GAAG,CAACZ,OAAO,IAAIA,OAAO,CAACM,SAAS,CAAC,CAAC;IACrEV,kBAAkB,EAAEe,YAAY,CAACD,UAAU,CAACE,GAAG,CAACZ,OAAO,IAAIA,OAAO,CAACJ,kBAAkB,CAAC,CAAC;IACvFW,mBAAmB,EAAEI,YAAY,CAACD,UAAU,CAACE,GAAG,CAACZ,OAAO,IAAIA,OAAO,CAACO,mBAAmB,CAAC;EAC5F,CAAC;AACL;AACA,SAASI,YAAYA,CAACG,IAAI,EAAE;EACxB,KAAK,IAAIC,CAAC,GAAGD,IAAI,CAACE,MAAM,GAAG,CAAC,EAAED,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;IACvC,IAAID,IAAI,CAACC,CAAC,CAAC,KAAKE,SAAS,EAAE;MACvB,OAAOH,IAAI,CAACC,CAAC,CAAC;IAClB;EACJ;EACA,OAAOE,SAAS;AACpB;AACA,SAASJ,YAAYA,CAACK,KAAK,EAAE;EACzB,MAAMC,MAAM,GAAG,EAAE;EACjBD,KAAK,CAACE,OAAO,CAAEC,IAAI,IAAKA,IAAI,IAAIF,MAAM,CAACG,IAAI,CAAC,GAAGD,IAAI,CAAC,CAAC;EACrD,OAAOF,MAAM;AACjB;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAMI,mBAAmB,GAAG/C,qBAAqB,CAACC,YAAY,EAAE,aAAa,EAAE,CAC3E;EAAEU,OAAO,EAAET,gBAAgB;EAAE8C,QAAQ,EAAE,CAAC,CAAC;EAAEC,KAAK,EAAE;AAAK,CAAC,EACxD;EAAEtC,OAAO,EAAER,eAAe;EAAE+C,QAAQ,EAAErC,kBAAkB;EAAEmB,IAAI,EAAE,CAAC9B,gBAAgB;AAAE,CAAC,CACvF,CAAC;AAEF,MAAMiD,kBAAkB,SAASzD,cAAc,CAAC;EAC5CuC,GAAGA,CAACmB,GAAG,EAAE;IACL,IAAIC,OAAO;IACX,IAAIC,MAAM;IACV,MAAMC,OAAO,GAAG,IAAIC,OAAO,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAK;MACtCL,OAAO,GAAGI,GAAG;MACbH,MAAM,GAAGI,GAAG;IAChB,CAAC,CAAC;IACF,MAAMC,GAAG,GAAG,IAAIC,cAAc,CAAC,CAAC;IAChCD,GAAG,CAACE,IAAI,CAAC,KAAK,EAAET,GAAG,EAAE,IAAI,CAAC;IAC1BO,GAAG,CAACG,YAAY,GAAG,MAAM;IACzBH,GAAG,CAACI,MAAM,GAAG,YAAY;MACrB,MAAMC,QAAQ,GAAGL,GAAG,CAACK,QAAQ;MAC7B,IAAIC,MAAM,GAAGN,GAAG,CAACM,MAAM;MACvB;MACA;MACA;MACA,IAAIA,MAAM,KAAK,CAAC,EAAE;QACdA,MAAM,GAAGD,QAAQ,GAAG,GAAG,GAAG,CAAC;MAC/B;MACA,IAAI,GAAG,IAAIC,MAAM,IAAIA,MAAM,IAAI,GAAG,EAAE;QAChCZ,OAAO,CAACW,QAAQ,CAAC;MACrB,CAAC,MACI;QACDV,MAAM,CAAE,kBAAiBF,GAAI,EAAC,CAAC;MACnC;IACJ,CAAC;IACDO,GAAG,CAACO,OAAO,GAAG,YAAY;MACtBZ,MAAM,CAAE,kBAAiBF,GAAI,EAAC,CAAC;IACnC,CAAC;IACDO,GAAG,CAACQ,IAAI,CAAC,CAAC;IACV,OAAOZ,OAAO;EAClB;EACA;IAAS,IAAI,CAACa,IAAI;MAAA,IAAAC,+BAAA;MAAA,gBAAAC,2BAAAC,CAAA;QAAA,QAAAF,+BAAA,KAAAA,+BAAA,GAA+E1E,EAAE,CAAA6E,qBAAA,CAAQrB,kBAAkB,IAAAoB,CAAA,IAAlBpB,kBAAkB;MAAA;IAAA,GAAsD;EAAE;EACrL;IAAS,IAAI,CAACsB,KAAK,kBAD8E9E,EAAE,CAAA+E,kBAAA;MAAAC,KAAA,EACYxB,kBAAkB;MAAAyB,OAAA,EAAlBzB,kBAAkB,CAAAiB;IAAA,EAAG;EAAE;AAC1I;AACA;EAAA,QAAAS,SAAA,oBAAAA,SAAA,KAHqGlF,EAAE,CAAAmF,iBAAA,CAGX3B,kBAAkB,EAAc,CAAC;IACjH4B,IAAI,EAAE3E;EACV,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA,MAAM4E,2CAA2C,GAAG,CAChDvE,oCAAoC,EACpC;EACIE,OAAO,EAAET,gBAAgB;EACzB8C,QAAQ,EAAE;IAAElB,SAAS,EAAE,CAAC;MAAEnB,OAAO,EAAEjB,cAAc;MAAEwD,QAAQ,EAAEC,kBAAkB;MAAEnB,IAAI,EAAE;IAAG,CAAC;EAAE,CAAC;EAC9FiB,KAAK,EAAE;AACX,CAAC,EACD;EAAEtC,OAAO,EAAEN,WAAW;EAAE2C,QAAQ,EAAExC;AAAqB,CAAC,CAC3D;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMyE,oBAAoB,SAASvF,cAAc,CAAC;EAC9CoB,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,CAAC;IACP,IAAI,CAACoE,MAAM,GAAG5E,OAAO,CAAC6E,cAAc;IACpC,IAAI,IAAI,CAACD,MAAM,IAAI,IAAI,EAAE;MACrB,MAAM,IAAIE,KAAK,CAAC,uEAAuE,CAAC;IAC5F;EACJ;EACAnD,GAAGA,CAACmB,GAAG,EAAE;IACL,IAAI,IAAI,CAAC8B,MAAM,CAACG,cAAc,CAACjC,GAAG,CAAC,EAAE;MACjC,OAAOI,OAAO,CAACH,OAAO,CAAC,IAAI,CAAC6B,MAAM,CAAC9B,GAAG,CAAC,CAAC;IAC5C,CAAC,MACI;MACD,OAAOI,OAAO,CAACF,MAAM,CAAC,yDAAyD,GAAGF,GAAG,CAAC;IAC1F;EACJ;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMkC,OAAO,GAAG,IAAI/E,OAAO,CAAC,SAAS,CAAC;;AAEtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMgF,uBAAuB,GAAG,CAAC;EAAE5E,OAAO,EAAEjB,cAAc;EAAEwD,QAAQ,EAAE+B,oBAAoB;EAAEjD,IAAI,EAAE;AAAG,CAAC,CAAC;AACvG;AACA;AACA;AACA,MAAMwD,sBAAsB,GAAGxF,qBAAqB,CAAC+C,mBAAmB,EAAE,gBAAgB,EAAEiC,2CAA2C,CAAC;;AAExI;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA,SAASnE,kBAAkB,EAAE0E,uBAAuB,EAAED,OAAO,EAAEE,sBAAsB,EAAER,2CAA2C,IAAIS,4CAA4C,EAAE1C,mBAAmB,IAAI2C,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}