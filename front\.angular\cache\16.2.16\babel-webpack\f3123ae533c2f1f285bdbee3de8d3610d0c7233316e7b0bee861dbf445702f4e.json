{"ast": null, "code": "import { Observable } from '../Observable';\nimport { innerFrom } from './innerFrom';\nimport { argsOrArgArray } from '../util/argsOrArgArray';\nimport { EMPTY } from './empty';\nimport { createOperatorSubscriber } from '../operators/OperatorSubscriber';\nimport { popResultSelector } from '../util/args';\nexport function zip(...args) {\n  const resultSelector = popResultSelector(args);\n  const sources = argsOrArgArray(args);\n  return sources.length ? new Observable(subscriber => {\n    let buffers = sources.map(() => []);\n    let completed = sources.map(() => false);\n    subscriber.add(() => {\n      buffers = completed = null;\n    });\n    for (let sourceIndex = 0; !subscriber.closed && sourceIndex < sources.length; sourceIndex++) {\n      innerFrom(sources[sourceIndex]).subscribe(createOperatorSubscriber(subscriber, value => {\n        buffers[sourceIndex].push(value);\n        if (buffers.every(buffer => buffer.length)) {\n          const result = buffers.map(buffer => buffer.shift());\n          subscriber.next(resultSelector ? resultSelector(...result) : result);\n          if (buffers.some((buffer, i) => !buffer.length && completed[i])) {\n            subscriber.complete();\n          }\n        }\n      }, () => {\n        completed[sourceIndex] = true;\n        !buffers[sourceIndex].length && subscriber.complete();\n      }));\n    }\n    return () => {\n      buffers = completed = null;\n    };\n  }) : EMPTY;\n}", "map": {"version": 3, "names": ["Observable", "innerFrom", "argsOrArgArray", "EMPTY", "createOperatorSubscriber", "popResultSelector", "zip", "args", "resultSelector", "sources", "length", "subscriber", "buffers", "map", "completed", "add", "sourceIndex", "closed", "subscribe", "value", "push", "every", "buffer", "result", "shift", "next", "some", "i", "complete"], "sources": ["C:/Users/<USER>/Desktop/angular/front/node_modules/rxjs/dist/esm/internal/observable/zip.js"], "sourcesContent": ["import { Observable } from '../Observable';\nimport { innerFrom } from './innerFrom';\nimport { argsOrArgArray } from '../util/argsOrArgArray';\nimport { EMPTY } from './empty';\nimport { createOperatorSubscriber } from '../operators/OperatorSubscriber';\nimport { popResultSelector } from '../util/args';\nexport function zip(...args) {\n    const resultSelector = popResultSelector(args);\n    const sources = argsOrArgArray(args);\n    return sources.length\n        ? new Observable((subscriber) => {\n            let buffers = sources.map(() => []);\n            let completed = sources.map(() => false);\n            subscriber.add(() => {\n                buffers = completed = null;\n            });\n            for (let sourceIndex = 0; !subscriber.closed && sourceIndex < sources.length; sourceIndex++) {\n                innerFrom(sources[sourceIndex]).subscribe(createOperatorSubscriber(subscriber, (value) => {\n                    buffers[sourceIndex].push(value);\n                    if (buffers.every((buffer) => buffer.length)) {\n                        const result = buffers.map((buffer) => buffer.shift());\n                        subscriber.next(resultSelector ? resultSelector(...result) : result);\n                        if (buffers.some((buffer, i) => !buffer.length && completed[i])) {\n                            subscriber.complete();\n                        }\n                    }\n                }, () => {\n                    completed[sourceIndex] = true;\n                    !buffers[sourceIndex].length && subscriber.complete();\n                }));\n            }\n            return () => {\n                buffers = completed = null;\n            };\n        })\n        : EMPTY;\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,eAAe;AAC1C,SAASC,SAAS,QAAQ,aAAa;AACvC,SAASC,cAAc,QAAQ,wBAAwB;AACvD,SAASC,KAAK,QAAQ,SAAS;AAC/B,SAASC,wBAAwB,QAAQ,iCAAiC;AAC1E,SAASC,iBAAiB,QAAQ,cAAc;AAChD,OAAO,SAASC,GAAGA,CAAC,GAAGC,IAAI,EAAE;EACzB,MAAMC,cAAc,GAAGH,iBAAiB,CAACE,IAAI,CAAC;EAC9C,MAAME,OAAO,GAAGP,cAAc,CAACK,IAAI,CAAC;EACpC,OAAOE,OAAO,CAACC,MAAM,GACf,IAAIV,UAAU,CAAEW,UAAU,IAAK;IAC7B,IAAIC,OAAO,GAAGH,OAAO,CAACI,GAAG,CAAC,MAAM,EAAE,CAAC;IACnC,IAAIC,SAAS,GAAGL,OAAO,CAACI,GAAG,CAAC,MAAM,KAAK,CAAC;IACxCF,UAAU,CAACI,GAAG,CAAC,MAAM;MACjBH,OAAO,GAAGE,SAAS,GAAG,IAAI;IAC9B,CAAC,CAAC;IACF,KAAK,IAAIE,WAAW,GAAG,CAAC,EAAE,CAACL,UAAU,CAACM,MAAM,IAAID,WAAW,GAAGP,OAAO,CAACC,MAAM,EAAEM,WAAW,EAAE,EAAE;MACzFf,SAAS,CAACQ,OAAO,CAACO,WAAW,CAAC,CAAC,CAACE,SAAS,CAACd,wBAAwB,CAACO,UAAU,EAAGQ,KAAK,IAAK;QACtFP,OAAO,CAACI,WAAW,CAAC,CAACI,IAAI,CAACD,KAAK,CAAC;QAChC,IAAIP,OAAO,CAACS,KAAK,CAAEC,MAAM,IAAKA,MAAM,CAACZ,MAAM,CAAC,EAAE;UAC1C,MAAMa,MAAM,GAAGX,OAAO,CAACC,GAAG,CAAES,MAAM,IAAKA,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC;UACtDb,UAAU,CAACc,IAAI,CAACjB,cAAc,GAAGA,cAAc,CAAC,GAAGe,MAAM,CAAC,GAAGA,MAAM,CAAC;UACpE,IAAIX,OAAO,CAACc,IAAI,CAAC,CAACJ,MAAM,EAAEK,CAAC,KAAK,CAACL,MAAM,CAACZ,MAAM,IAAII,SAAS,CAACa,CAAC,CAAC,CAAC,EAAE;YAC7DhB,UAAU,CAACiB,QAAQ,CAAC,CAAC;UACzB;QACJ;MACJ,CAAC,EAAE,MAAM;QACLd,SAAS,CAACE,WAAW,CAAC,GAAG,IAAI;QAC7B,CAACJ,OAAO,CAACI,WAAW,CAAC,CAACN,MAAM,IAAIC,UAAU,CAACiB,QAAQ,CAAC,CAAC;MACzD,CAAC,CAAC,CAAC;IACP;IACA,OAAO,MAAM;MACThB,OAAO,GAAGE,SAAS,GAAG,IAAI;IAC9B,CAAC;EACL,CAAC,CAAC,GACAX,KAAK;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}