{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Desktop/angular/front/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n/**\n * @license Angular v16.2.12\n * (c) 2010-2022 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport { Location } from '@angular/common';\nimport { provideLocationMocks } from '@angular/common/testing';\nimport * as i0 from '@angular/core';\nimport { inject, Compiler, Injector, NgModule, Injectable, Component, ViewChild } from '@angular/core';\nimport { UrlSerializer, ChildrenOutletContexts, ROUTES, UrlHandlingStrategy, ROUTER_CONFIGURATION, RouteReuseStrategy, TitleStrategy, Router, RouterModule, ɵROUTER_PROVIDERS, withPreloading, NoPreloading, RouterOutlet, ɵafterNextNavigation } from '@angular/router';\nimport { TestBed } from '@angular/core/testing';\nfunction isUrlHandlingStrategy(opts) {\n  // This property check is needed because UrlHandlingStrategy is an interface and doesn't exist at\n  // runtime.\n  return 'shouldProcessUrl' in opts;\n}\nfunction throwInvalidConfigError(parameter) {\n  throw new Error(`Parameter ${parameter} does not match the one available in the injector. ` + '`setupTestingRouter` is meant to be used as a factory function with dependencies coming from DI.');\n}\n/**\n * Router setup factory function used for testing.\n *\n * @publicApi\n * @deprecated Use `provideRouter` or `RouterModule` instead.\n */\nfunction setupTestingRouter(urlSerializer, contexts, location, compiler, injector, routes, opts, urlHandlingStrategy, routeReuseStrategy, titleStrategy) {\n  // Note: The checks below are to detect misconfigured providers and invalid uses of\n  // `setupTestingRouter`. This function is not used internally (neither in router code or anywhere\n  // in g3). It appears this function was exposed as publicApi by mistake and should not be used\n  // externally either. However, if it is, the documented intent is to be used as a factory function\n  // and parameter values should always match what's available in DI.\n  if (urlSerializer !== inject(UrlSerializer)) {\n    throwInvalidConfigError('urlSerializer');\n  }\n  if (contexts !== inject(ChildrenOutletContexts)) {\n    throwInvalidConfigError('contexts');\n  }\n  if (location !== inject(Location)) {\n    throwInvalidConfigError('location');\n  }\n  if (compiler !== inject(Compiler)) {\n    throwInvalidConfigError('compiler');\n  }\n  if (injector !== inject(Injector)) {\n    throwInvalidConfigError('injector');\n  }\n  if (routes !== inject(ROUTES)) {\n    throwInvalidConfigError('routes');\n  }\n  if (opts) {\n    // Handle deprecated argument ordering.\n    if (isUrlHandlingStrategy(opts)) {\n      if (opts !== inject(UrlHandlingStrategy)) {\n        throwInvalidConfigError('opts (UrlHandlingStrategy)');\n      }\n    } else {\n      if (opts !== inject(ROUTER_CONFIGURATION)) {\n        throwInvalidConfigError('opts (ROUTER_CONFIGURATION)');\n      }\n    }\n  }\n  if (urlHandlingStrategy !== inject(UrlHandlingStrategy)) {\n    throwInvalidConfigError('urlHandlingStrategy');\n  }\n  if (routeReuseStrategy !== inject(RouteReuseStrategy)) {\n    throwInvalidConfigError('routeReuseStrategy');\n  }\n  if (titleStrategy !== inject(TitleStrategy)) {\n    throwInvalidConfigError('titleStrategy');\n  }\n  return new Router();\n}\n/**\n * @description\n *\n * Sets up the router to be used for testing.\n *\n * The modules sets up the router to be used for testing.\n * It provides spy implementations of `Location` and `LocationStrategy`.\n *\n * @usageNotes\n * ### Example\n *\n * ```\n * beforeEach(() => {\n *   TestBed.configureTestingModule({\n *     imports: [\n *       RouterModule.forRoot(\n *         [{path: '', component: BlankCmp}, {path: 'simple', component: SimpleCmp}]\n *       )\n *     ]\n *   });\n * });\n * ```\n *\n * @publicApi\n */\nclass RouterTestingModule {\n  static withRoutes(routes, config) {\n    return {\n      ngModule: RouterTestingModule,\n      providers: [{\n        provide: ROUTES,\n        multi: true,\n        useValue: routes\n      }, {\n        provide: ROUTER_CONFIGURATION,\n        useValue: config ? config : {}\n      }]\n    };\n  }\n  static {\n    this.ɵfac = function RouterTestingModule_Factory(t) {\n      return new (t || RouterTestingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: RouterTestingModule,\n      exports: [RouterModule]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      providers: [ɵROUTER_PROVIDERS, provideLocationMocks(), withPreloading(NoPreloading).ɵproviders, {\n        provide: ROUTES,\n        multi: true,\n        useValue: []\n      }],\n      imports: [RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(RouterTestingModule, [{\n    type: NgModule,\n    args: [{\n      exports: [RouterModule],\n      providers: [ɵROUTER_PROVIDERS, provideLocationMocks(), withPreloading(NoPreloading).ɵproviders, {\n        provide: ROUTES,\n        multi: true,\n        useValue: []\n      }]\n    }]\n  }], null, null);\n})();\nclass RootFixtureService {\n  createHarness() {\n    if (this.harness) {\n      throw new Error('Only one harness should be created per test.');\n    }\n    this.harness = new RouterTestingHarness(this.getRootFixture());\n    return this.harness;\n  }\n  getRootFixture() {\n    if (this.fixture !== undefined) {\n      return this.fixture;\n    }\n    this.fixture = TestBed.createComponent(RootCmp);\n    this.fixture.detectChanges();\n    return this.fixture;\n  }\n  static {\n    this.ɵfac = function RootFixtureService_Factory(t) {\n      return new (t || RootFixtureService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: RootFixtureService,\n      factory: RootFixtureService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(RootFixtureService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nclass RootCmp {\n  static {\n    this.ɵfac = function RootCmp_Factory(t) {\n      return new (t || RootCmp)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: RootCmp,\n      selectors: [[\"ng-component\"]],\n      viewQuery: function RootCmp_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(RouterOutlet, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.outlet = _t.first);\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 1,\n      vars: 0,\n      template: function RootCmp_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelement(0, \"router-outlet\");\n        }\n      },\n      dependencies: [RouterOutlet],\n      encapsulation: 2\n    });\n  }\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(RootCmp, [{\n    type: Component,\n    args: [{\n      standalone: true,\n      template: '<router-outlet></router-outlet>',\n      imports: [RouterOutlet]\n    }]\n  }], null, {\n    outlet: [{\n      type: ViewChild,\n      args: [RouterOutlet]\n    }]\n  });\n})();\n/**\n * A testing harness for the `Router` to reduce the boilerplate needed to test routes and routed\n * components.\n *\n * @publicApi\n */\nclass RouterTestingHarness {\n  /**\n   * Creates a `RouterTestingHarness` instance.\n   *\n   * The `RouterTestingHarness` also creates its own root component with a `RouterOutlet` for the\n   * purposes of rendering route components.\n   *\n   * Throws an error if an instance has already been created.\n   * Use of this harness also requires `destroyAfterEach: true` in the `ModuleTeardownOptions`\n   *\n   * @param initialUrl The target of navigation to trigger before returning the harness.\n   */\n  static create(initialUrl) {\n    return _asyncToGenerator(function* () {\n      const harness = TestBed.inject(RootFixtureService).createHarness();\n      if (initialUrl !== undefined) {\n        yield harness.navigateByUrl(initialUrl);\n      }\n      return harness;\n    })();\n  }\n  /** @internal */\n  constructor(fixture) {\n    this.fixture = fixture;\n  }\n  /** Instructs the root fixture to run change detection. */\n  detectChanges() {\n    this.fixture.detectChanges();\n  }\n  /** The `DebugElement` of the `RouterOutlet` component. `null` if the outlet is not activated. */\n  get routeDebugElement() {\n    const outlet = this.fixture.componentInstance.outlet;\n    if (!outlet || !outlet.isActivated) {\n      return null;\n    }\n    return this.fixture.debugElement.query(v => v.componentInstance === outlet.component);\n  }\n  /** The native element of the `RouterOutlet` component. `null` if the outlet is not activated. */\n  get routeNativeElement() {\n    return this.routeDebugElement?.nativeElement ?? null;\n  }\n  navigateByUrl(url, requiredRoutedComponentType) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      const router = TestBed.inject(Router);\n      let resolveFn;\n      const redirectTrackingPromise = new Promise(resolve => {\n        resolveFn = resolve;\n      });\n      ɵafterNextNavigation(TestBed.inject(Router), resolveFn);\n      yield router.navigateByUrl(url);\n      yield redirectTrackingPromise;\n      _this.fixture.detectChanges();\n      const outlet = _this.fixture.componentInstance.outlet;\n      // The outlet might not be activated if the user is testing a navigation for a guard that\n      // rejects\n      if (outlet && outlet.isActivated && outlet.activatedRoute.component) {\n        const activatedComponent = outlet.component;\n        if (requiredRoutedComponentType !== undefined && !(activatedComponent instanceof requiredRoutedComponentType)) {\n          throw new Error(`Unexpected routed component type. Expected ${requiredRoutedComponentType.name} but got ${activatedComponent.constructor.name}`);\n        }\n        return activatedComponent;\n      } else {\n        if (requiredRoutedComponentType !== undefined) {\n          throw new Error(`Unexpected routed component type. Expected ${requiredRoutedComponentType.name} but the navigation did not activate any component.`);\n        }\n        return null;\n      }\n    })();\n  }\n}\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of the router/testing package.\n */\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of this package.\n */\n// This file only reexports content of the `src` folder. Keep it that way.\n\n// This file is not used to build this module. It is only used during editing\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { RouterTestingHarness, RouterTestingModule, setupTestingRouter };", "map": {"version": 3, "names": ["Location", "provideLocationMocks", "i0", "inject", "Compiler", "Injector", "NgModule", "Injectable", "Component", "ViewChild", "UrlSerializer", "ChildrenOutletContexts", "ROUTES", "UrlHandlingStrategy", "ROUTER_CONFIGURATION", "RouteReuseStrategy", "TitleStrategy", "Router", "RouterModule", "ɵROUTER_PROVIDERS", "withPreloading", "NoPreloading", "RouterOutlet", "ɵafterNextNavigation", "TestBed", "isUrlHandlingStrategy", "opts", "throwInvalidConfigError", "parameter", "Error", "setupTestingRouter", "urlSerializer", "contexts", "location", "compiler", "injector", "routes", "urlHandlingStrategy", "routeReuseStrategy", "titleStrategy", "RouterTestingModule", "with<PERSON>out<PERSON>", "config", "ngModule", "providers", "provide", "multi", "useValue", "ɵfac", "RouterTestingModule_Factory", "t", "ɵmod", "ɵɵdefineNgModule", "type", "exports", "ɵinj", "ɵɵdefineInjector", "ɵproviders", "imports", "ngDevMode", "ɵsetClassMetadata", "args", "RootFixtureService", "createHarness", "harness", "RouterTestingHarness", "getRootFixture", "fixture", "undefined", "createComponent", "RootCmp", "detectChanges", "RootFixtureService_Factory", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "providedIn", "RootCmp_Factory", "ɵcmp", "ɵɵdefineComponent", "selectors", "viewQuery", "RootCmp_Query", "rf", "ctx", "ɵɵviewQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "outlet", "first", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "template", "RootCmp_Template", "ɵɵelement", "dependencies", "encapsulation", "create", "initialUrl", "_asyncToGenerator", "navigateByUrl", "constructor", "routeDebugElement", "componentInstance", "isActivated", "debugElement", "query", "v", "component", "routeNativeElement", "nativeElement", "url", "requiredRoutedComponentType", "_this", "router", "resolveFn", "redirectTrackingPromise", "Promise", "resolve", "activatedRoute", "activatedComponent", "name"], "sources": ["C:/Users/<USER>/Desktop/angular/front/node_modules/@angular/router/fesm2022/testing.mjs"], "sourcesContent": ["/**\n * @license Angular v16.2.12\n * (c) 2010-2022 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport { Location } from '@angular/common';\nimport { provideLocationMocks } from '@angular/common/testing';\nimport * as i0 from '@angular/core';\nimport { inject, Compiler, Injector, NgModule, Injectable, Component, ViewChild } from '@angular/core';\nimport { UrlSerializer, ChildrenOutletContexts, ROUTES, UrlHandlingStrategy, ROUTER_CONFIGURATION, RouteReuseStrategy, TitleStrategy, Router, RouterModule, ɵROUTER_PROVIDERS, withPreloading, NoPreloading, RouterOutlet, ɵafterNextNavigation } from '@angular/router';\nimport { TestBed } from '@angular/core/testing';\n\nfunction isUrlHandlingStrategy(opts) {\n    // This property check is needed because UrlHandlingStrategy is an interface and doesn't exist at\n    // runtime.\n    return 'shouldProcessUrl' in opts;\n}\nfunction throwInvalidConfigError(parameter) {\n    throw new Error(`Parameter ${parameter} does not match the one available in the injector. ` +\n        '`setupTestingRouter` is meant to be used as a factory function with dependencies coming from DI.');\n}\n/**\n * Router setup factory function used for testing.\n *\n * @publicApi\n * @deprecated Use `provideRouter` or `RouterModule` instead.\n */\nfunction setupTestingRouter(urlSerializer, contexts, location, compiler, injector, routes, opts, urlHandlingStrategy, routeReuseStrategy, titleStrategy) {\n    // Note: The checks below are to detect misconfigured providers and invalid uses of\n    // `setupTestingRouter`. This function is not used internally (neither in router code or anywhere\n    // in g3). It appears this function was exposed as publicApi by mistake and should not be used\n    // externally either. However, if it is, the documented intent is to be used as a factory function\n    // and parameter values should always match what's available in DI.\n    if (urlSerializer !== inject(UrlSerializer)) {\n        throwInvalidConfigError('urlSerializer');\n    }\n    if (contexts !== inject(ChildrenOutletContexts)) {\n        throwInvalidConfigError('contexts');\n    }\n    if (location !== inject(Location)) {\n        throwInvalidConfigError('location');\n    }\n    if (compiler !== inject(Compiler)) {\n        throwInvalidConfigError('compiler');\n    }\n    if (injector !== inject(Injector)) {\n        throwInvalidConfigError('injector');\n    }\n    if (routes !== inject(ROUTES)) {\n        throwInvalidConfigError('routes');\n    }\n    if (opts) {\n        // Handle deprecated argument ordering.\n        if (isUrlHandlingStrategy(opts)) {\n            if (opts !== inject(UrlHandlingStrategy)) {\n                throwInvalidConfigError('opts (UrlHandlingStrategy)');\n            }\n        }\n        else {\n            if (opts !== inject(ROUTER_CONFIGURATION)) {\n                throwInvalidConfigError('opts (ROUTER_CONFIGURATION)');\n            }\n        }\n    }\n    if (urlHandlingStrategy !== inject(UrlHandlingStrategy)) {\n        throwInvalidConfigError('urlHandlingStrategy');\n    }\n    if (routeReuseStrategy !== inject(RouteReuseStrategy)) {\n        throwInvalidConfigError('routeReuseStrategy');\n    }\n    if (titleStrategy !== inject(TitleStrategy)) {\n        throwInvalidConfigError('titleStrategy');\n    }\n    return new Router();\n}\n/**\n * @description\n *\n * Sets up the router to be used for testing.\n *\n * The modules sets up the router to be used for testing.\n * It provides spy implementations of `Location` and `LocationStrategy`.\n *\n * @usageNotes\n * ### Example\n *\n * ```\n * beforeEach(() => {\n *   TestBed.configureTestingModule({\n *     imports: [\n *       RouterModule.forRoot(\n *         [{path: '', component: BlankCmp}, {path: 'simple', component: SimpleCmp}]\n *       )\n *     ]\n *   });\n * });\n * ```\n *\n * @publicApi\n */\nclass RouterTestingModule {\n    static withRoutes(routes, config) {\n        return {\n            ngModule: RouterTestingModule,\n            providers: [\n                { provide: ROUTES, multi: true, useValue: routes },\n                { provide: ROUTER_CONFIGURATION, useValue: config ? config : {} },\n            ]\n        };\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: RouterTestingModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"16.2.12\", ngImport: i0, type: RouterTestingModule, exports: [RouterModule] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: RouterTestingModule, providers: [\n            ɵROUTER_PROVIDERS,\n            provideLocationMocks(),\n            withPreloading(NoPreloading).ɵproviders,\n            { provide: ROUTES, multi: true, useValue: [] },\n        ], imports: [RouterModule] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: RouterTestingModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    exports: [RouterModule],\n                    providers: [\n                        ɵROUTER_PROVIDERS,\n                        provideLocationMocks(),\n                        withPreloading(NoPreloading).ɵproviders,\n                        { provide: ROUTES, multi: true, useValue: [] },\n                    ]\n                }]\n        }] });\n\nclass RootFixtureService {\n    createHarness() {\n        if (this.harness) {\n            throw new Error('Only one harness should be created per test.');\n        }\n        this.harness = new RouterTestingHarness(this.getRootFixture());\n        return this.harness;\n    }\n    getRootFixture() {\n        if (this.fixture !== undefined) {\n            return this.fixture;\n        }\n        this.fixture = TestBed.createComponent(RootCmp);\n        this.fixture.detectChanges();\n        return this.fixture;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: RootFixtureService, deps: [], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: RootFixtureService, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: RootFixtureService, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }] });\nclass RootCmp {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: RootCmp, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"16.2.12\", type: RootCmp, isStandalone: true, selector: \"ng-component\", viewQueries: [{ propertyName: \"outlet\", first: true, predicate: RouterOutlet, descendants: true }], ngImport: i0, template: '<router-outlet></router-outlet>', isInline: true, dependencies: [{ kind: \"directive\", type: RouterOutlet, selector: \"router-outlet\", inputs: [\"name\"], outputs: [\"activate\", \"deactivate\", \"attach\", \"detach\"], exportAs: [\"outlet\"] }] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.12\", ngImport: i0, type: RootCmp, decorators: [{\n            type: Component,\n            args: [{\n                    standalone: true,\n                    template: '<router-outlet></router-outlet>',\n                    imports: [RouterOutlet],\n                }]\n        }], propDecorators: { outlet: [{\n                type: ViewChild,\n                args: [RouterOutlet]\n            }] } });\n/**\n * A testing harness for the `Router` to reduce the boilerplate needed to test routes and routed\n * components.\n *\n * @publicApi\n */\nclass RouterTestingHarness {\n    /**\n     * Creates a `RouterTestingHarness` instance.\n     *\n     * The `RouterTestingHarness` also creates its own root component with a `RouterOutlet` for the\n     * purposes of rendering route components.\n     *\n     * Throws an error if an instance has already been created.\n     * Use of this harness also requires `destroyAfterEach: true` in the `ModuleTeardownOptions`\n     *\n     * @param initialUrl The target of navigation to trigger before returning the harness.\n     */\n    static async create(initialUrl) {\n        const harness = TestBed.inject(RootFixtureService).createHarness();\n        if (initialUrl !== undefined) {\n            await harness.navigateByUrl(initialUrl);\n        }\n        return harness;\n    }\n    /** @internal */\n    constructor(fixture) {\n        this.fixture = fixture;\n    }\n    /** Instructs the root fixture to run change detection. */\n    detectChanges() {\n        this.fixture.detectChanges();\n    }\n    /** The `DebugElement` of the `RouterOutlet` component. `null` if the outlet is not activated. */\n    get routeDebugElement() {\n        const outlet = this.fixture.componentInstance.outlet;\n        if (!outlet || !outlet.isActivated) {\n            return null;\n        }\n        return this.fixture.debugElement.query(v => v.componentInstance === outlet.component);\n    }\n    /** The native element of the `RouterOutlet` component. `null` if the outlet is not activated. */\n    get routeNativeElement() {\n        return this.routeDebugElement?.nativeElement ?? null;\n    }\n    async navigateByUrl(url, requiredRoutedComponentType) {\n        const router = TestBed.inject(Router);\n        let resolveFn;\n        const redirectTrackingPromise = new Promise(resolve => {\n            resolveFn = resolve;\n        });\n        ɵafterNextNavigation(TestBed.inject(Router), resolveFn);\n        await router.navigateByUrl(url);\n        await redirectTrackingPromise;\n        this.fixture.detectChanges();\n        const outlet = this.fixture.componentInstance.outlet;\n        // The outlet might not be activated if the user is testing a navigation for a guard that\n        // rejects\n        if (outlet && outlet.isActivated && outlet.activatedRoute.component) {\n            const activatedComponent = outlet.component;\n            if (requiredRoutedComponentType !== undefined &&\n                !(activatedComponent instanceof requiredRoutedComponentType)) {\n                throw new Error(`Unexpected routed component type. Expected ${requiredRoutedComponentType.name} but got ${activatedComponent.constructor.name}`);\n            }\n            return activatedComponent;\n        }\n        else {\n            if (requiredRoutedComponentType !== undefined) {\n                throw new Error(`Unexpected routed component type. Expected ${requiredRoutedComponentType.name} but the navigation did not activate any component.`);\n            }\n            return null;\n        }\n    }\n}\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of the router/testing package.\n */\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of this package.\n */\n// This file only reexports content of the `src` folder. Keep it that way.\n\n// This file is not used to build this module. It is only used during editing\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { RouterTestingHarness, RouterTestingModule, setupTestingRouter };\n"], "mappings": ";AAAA;AACA;AACA;AACA;AACA;;AAEA,SAASA,QAAQ,QAAQ,iBAAiB;AAC1C,SAASC,oBAAoB,QAAQ,yBAAyB;AAC9D,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,SAAS,EAAEC,SAAS,QAAQ,eAAe;AACtG,SAASC,aAAa,EAAEC,sBAAsB,EAAEC,MAAM,EAAEC,mBAAmB,EAAEC,oBAAoB,EAAEC,kBAAkB,EAAEC,aAAa,EAAEC,MAAM,EAAEC,YAAY,EAAEC,iBAAiB,EAAEC,cAAc,EAAEC,YAAY,EAAEC,YAAY,EAAEC,oBAAoB,QAAQ,iBAAiB;AACxQ,SAASC,OAAO,QAAQ,uBAAuB;AAE/C,SAASC,qBAAqBA,CAACC,IAAI,EAAE;EACjC;EACA;EACA,OAAO,kBAAkB,IAAIA,IAAI;AACrC;AACA,SAASC,uBAAuBA,CAACC,SAAS,EAAE;EACxC,MAAM,IAAIC,KAAK,CAAE,aAAYD,SAAU,qDAAoD,GACvF,kGAAkG,CAAC;AAC3G;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,kBAAkBA,CAACC,aAAa,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,MAAM,EAAEV,IAAI,EAAEW,mBAAmB,EAAEC,kBAAkB,EAAEC,aAAa,EAAE;EACrJ;EACA;EACA;EACA;EACA;EACA,IAAIR,aAAa,KAAK5B,MAAM,CAACO,aAAa,CAAC,EAAE;IACzCiB,uBAAuB,CAAC,eAAe,CAAC;EAC5C;EACA,IAAIK,QAAQ,KAAK7B,MAAM,CAACQ,sBAAsB,CAAC,EAAE;IAC7CgB,uBAAuB,CAAC,UAAU,CAAC;EACvC;EACA,IAAIM,QAAQ,KAAK9B,MAAM,CAACH,QAAQ,CAAC,EAAE;IAC/B2B,uBAAuB,CAAC,UAAU,CAAC;EACvC;EACA,IAAIO,QAAQ,KAAK/B,MAAM,CAACC,QAAQ,CAAC,EAAE;IAC/BuB,uBAAuB,CAAC,UAAU,CAAC;EACvC;EACA,IAAIQ,QAAQ,KAAKhC,MAAM,CAACE,QAAQ,CAAC,EAAE;IAC/BsB,uBAAuB,CAAC,UAAU,CAAC;EACvC;EACA,IAAIS,MAAM,KAAKjC,MAAM,CAACS,MAAM,CAAC,EAAE;IAC3Be,uBAAuB,CAAC,QAAQ,CAAC;EACrC;EACA,IAAID,IAAI,EAAE;IACN;IACA,IAAID,qBAAqB,CAACC,IAAI,CAAC,EAAE;MAC7B,IAAIA,IAAI,KAAKvB,MAAM,CAACU,mBAAmB,CAAC,EAAE;QACtCc,uBAAuB,CAAC,4BAA4B,CAAC;MACzD;IACJ,CAAC,MACI;MACD,IAAID,IAAI,KAAKvB,MAAM,CAACW,oBAAoB,CAAC,EAAE;QACvCa,uBAAuB,CAAC,6BAA6B,CAAC;MAC1D;IACJ;EACJ;EACA,IAAIU,mBAAmB,KAAKlC,MAAM,CAACU,mBAAmB,CAAC,EAAE;IACrDc,uBAAuB,CAAC,qBAAqB,CAAC;EAClD;EACA,IAAIW,kBAAkB,KAAKnC,MAAM,CAACY,kBAAkB,CAAC,EAAE;IACnDY,uBAAuB,CAAC,oBAAoB,CAAC;EACjD;EACA,IAAIY,aAAa,KAAKpC,MAAM,CAACa,aAAa,CAAC,EAAE;IACzCW,uBAAuB,CAAC,eAAe,CAAC;EAC5C;EACA,OAAO,IAAIV,MAAM,CAAC,CAAC;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMuB,mBAAmB,CAAC;EACtB,OAAOC,UAAUA,CAACL,MAAM,EAAEM,MAAM,EAAE;IAC9B,OAAO;MACHC,QAAQ,EAAEH,mBAAmB;MAC7BI,SAAS,EAAE,CACP;QAAEC,OAAO,EAAEjC,MAAM;QAAEkC,KAAK,EAAE,IAAI;QAAEC,QAAQ,EAAEX;MAAO,CAAC,EAClD;QAAES,OAAO,EAAE/B,oBAAoB;QAAEiC,QAAQ,EAAEL,MAAM,GAAGA,MAAM,GAAG,CAAC;MAAE,CAAC;IAEzE,CAAC;EACL;EACA;IAAS,IAAI,CAACM,IAAI,YAAAC,4BAAAC,CAAA;MAAA,YAAAA,CAAA,IAAyFV,mBAAmB;IAAA,CAAkD;EAAE;EAClL;IAAS,IAAI,CAACW,IAAI,kBAD+EjD,EAAE,CAAAkD,gBAAA;MAAAC,IAAA,EACSb,mBAAmB;MAAAc,OAAA,GAAYpC,YAAY;IAAA,EAAI;EAAE;EAC7J;IAAS,IAAI,CAACqC,IAAI,kBAF+ErD,EAAE,CAAAsD,gBAAA;MAAAZ,SAAA,EAEyC,CACpIzB,iBAAiB,EACjBlB,oBAAoB,CAAC,CAAC,EACtBmB,cAAc,CAACC,YAAY,CAAC,CAACoC,UAAU,EACvC;QAAEZ,OAAO,EAAEjC,MAAM;QAAEkC,KAAK,EAAE,IAAI;QAAEC,QAAQ,EAAE;MAAG,CAAC,CACjD;MAAAW,OAAA,GAAYxC,YAAY;IAAA,EAAI;EAAE;AACvC;AACA;EAAA,QAAAyC,SAAA,oBAAAA,SAAA,KATqGzD,EAAE,CAAA0D,iBAAA,CASXpB,mBAAmB,EAAc,CAAC;IAClHa,IAAI,EAAE/C,QAAQ;IACduD,IAAI,EAAE,CAAC;MACCP,OAAO,EAAE,CAACpC,YAAY,CAAC;MACvB0B,SAAS,EAAE,CACPzB,iBAAiB,EACjBlB,oBAAoB,CAAC,CAAC,EACtBmB,cAAc,CAACC,YAAY,CAAC,CAACoC,UAAU,EACvC;QAAEZ,OAAO,EAAEjC,MAAM;QAAEkC,KAAK,EAAE,IAAI;QAAEC,QAAQ,EAAE;MAAG,CAAC;IAEtD,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,MAAMe,kBAAkB,CAAC;EACrBC,aAAaA,CAAA,EAAG;IACZ,IAAI,IAAI,CAACC,OAAO,EAAE;MACd,MAAM,IAAInC,KAAK,CAAC,8CAA8C,CAAC;IACnE;IACA,IAAI,CAACmC,OAAO,GAAG,IAAIC,oBAAoB,CAAC,IAAI,CAACC,cAAc,CAAC,CAAC,CAAC;IAC9D,OAAO,IAAI,CAACF,OAAO;EACvB;EACAE,cAAcA,CAAA,EAAG;IACb,IAAI,IAAI,CAACC,OAAO,KAAKC,SAAS,EAAE;MAC5B,OAAO,IAAI,CAACD,OAAO;IACvB;IACA,IAAI,CAACA,OAAO,GAAG3C,OAAO,CAAC6C,eAAe,CAACC,OAAO,CAAC;IAC/C,IAAI,CAACH,OAAO,CAACI,aAAa,CAAC,CAAC;IAC5B,OAAO,IAAI,CAACJ,OAAO;EACvB;EACA;IAAS,IAAI,CAACnB,IAAI,YAAAwB,2BAAAtB,CAAA;MAAA,YAAAA,CAAA,IAAyFY,kBAAkB;IAAA,CAAoD;EAAE;EACnL;IAAS,IAAI,CAACW,KAAK,kBAvC8EvE,EAAE,CAAAwE,kBAAA;MAAAC,KAAA,EAuCYb,kBAAkB;MAAAc,OAAA,EAAlBd,kBAAkB,CAAAd,IAAA;MAAA6B,UAAA,EAAc;IAAM,EAAG;EAAE;AAC9J;AACA;EAAA,QAAAlB,SAAA,oBAAAA,SAAA,KAzCqGzD,EAAE,CAAA0D,iBAAA,CAyCXE,kBAAkB,EAAc,CAAC;IACjHT,IAAI,EAAE9C,UAAU;IAChBsD,IAAI,EAAE,CAAC;MAAEgB,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC;AAAA;AACV,MAAMP,OAAO,CAAC;EACV;IAAS,IAAI,CAACtB,IAAI,YAAA8B,gBAAA5B,CAAA;MAAA,YAAAA,CAAA,IAAyFoB,OAAO;IAAA,CAAmD;EAAE;EACvK;IAAS,IAAI,CAACS,IAAI,kBA/C+E7E,EAAE,CAAA8E,iBAAA;MAAA3B,IAAA,EA+CJiB,OAAO;MAAAW,SAAA;MAAAC,SAAA,WAAAC,cAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA/CLlF,EAAE,CAAAoF,WAAA,CA+CmHhE,YAAY;QAAA;QAAA,IAAA8D,EAAA;UAAA,IAAAG,EAAA;UA/CjIrF,EAAE,CAAAsF,cAAA,CAAAD,EAAA,GAAFrF,EAAE,CAAAuF,WAAA,QAAAJ,GAAA,CAAAK,MAAA,GAAAH,EAAA,CAAAI,KAAA;QAAA;MAAA;MAAAC,UAAA;MAAAC,QAAA,GAAF3F,EAAE,CAAA4F,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,iBAAAd,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFlF,EAAE,CAAAiG,SAAA,mBA+C8M,CAAC;QAAA;MAAA;MAAAC,YAAA,GAA6D9E,YAAY;MAAA+E,aAAA;IAAA,EAAkI;EAAE;AACngB;AACA;EAAA,QAAA1C,SAAA,oBAAAA,SAAA,KAjDqGzD,EAAE,CAAA0D,iBAAA,CAiDXU,OAAO,EAAc,CAAC;IACtGjB,IAAI,EAAE7C,SAAS;IACfqD,IAAI,EAAE,CAAC;MACC+B,UAAU,EAAE,IAAI;MAChBK,QAAQ,EAAE,iCAAiC;MAC3CvC,OAAO,EAAE,CAACpC,YAAY;IAC1B,CAAC;EACT,CAAC,CAAC,QAAkB;IAAEoE,MAAM,EAAE,CAAC;MACvBrC,IAAI,EAAE5C,SAAS;MACfoD,IAAI,EAAE,CAACvC,YAAY;IACvB,CAAC;EAAE,CAAC;AAAA;AAChB;AACA;AACA;AACA;AACA;AACA;AACA,MAAM2C,oBAAoB,CAAC;EACvB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,OAAaqC,MAAMA,CAACC,UAAU,EAAE;IAAA,OAAAC,iBAAA;MAC5B,MAAMxC,OAAO,GAAGxC,OAAO,CAACrB,MAAM,CAAC2D,kBAAkB,CAAC,CAACC,aAAa,CAAC,CAAC;MAClE,IAAIwC,UAAU,KAAKnC,SAAS,EAAE;QAC1B,MAAMJ,OAAO,CAACyC,aAAa,CAACF,UAAU,CAAC;MAC3C;MACA,OAAOvC,OAAO;IAAC;EACnB;EACA;EACA0C,WAAWA,CAACvC,OAAO,EAAE;IACjB,IAAI,CAACA,OAAO,GAAGA,OAAO;EAC1B;EACA;EACAI,aAAaA,CAAA,EAAG;IACZ,IAAI,CAACJ,OAAO,CAACI,aAAa,CAAC,CAAC;EAChC;EACA;EACA,IAAIoC,iBAAiBA,CAAA,EAAG;IACpB,MAAMjB,MAAM,GAAG,IAAI,CAACvB,OAAO,CAACyC,iBAAiB,CAAClB,MAAM;IACpD,IAAI,CAACA,MAAM,IAAI,CAACA,MAAM,CAACmB,WAAW,EAAE;MAChC,OAAO,IAAI;IACf;IACA,OAAO,IAAI,CAAC1C,OAAO,CAAC2C,YAAY,CAACC,KAAK,CAACC,CAAC,IAAIA,CAAC,CAACJ,iBAAiB,KAAKlB,MAAM,CAACuB,SAAS,CAAC;EACzF;EACA;EACA,IAAIC,kBAAkBA,CAAA,EAAG;IACrB,OAAO,IAAI,CAACP,iBAAiB,EAAEQ,aAAa,IAAI,IAAI;EACxD;EACMV,aAAaA,CAACW,GAAG,EAAEC,2BAA2B,EAAE;IAAA,IAAAC,KAAA;IAAA,OAAAd,iBAAA;MAClD,MAAMe,MAAM,GAAG/F,OAAO,CAACrB,MAAM,CAACc,MAAM,CAAC;MACrC,IAAIuG,SAAS;MACb,MAAMC,uBAAuB,GAAG,IAAIC,OAAO,CAACC,OAAO,IAAI;QACnDH,SAAS,GAAGG,OAAO;MACvB,CAAC,CAAC;MACFpG,oBAAoB,CAACC,OAAO,CAACrB,MAAM,CAACc,MAAM,CAAC,EAAEuG,SAAS,CAAC;MACvD,MAAMD,MAAM,CAACd,aAAa,CAACW,GAAG,CAAC;MAC/B,MAAMK,uBAAuB;MAC7BH,KAAI,CAACnD,OAAO,CAACI,aAAa,CAAC,CAAC;MAC5B,MAAMmB,MAAM,GAAG4B,KAAI,CAACnD,OAAO,CAACyC,iBAAiB,CAAClB,MAAM;MACpD;MACA;MACA,IAAIA,MAAM,IAAIA,MAAM,CAACmB,WAAW,IAAInB,MAAM,CAACkC,cAAc,CAACX,SAAS,EAAE;QACjE,MAAMY,kBAAkB,GAAGnC,MAAM,CAACuB,SAAS;QAC3C,IAAII,2BAA2B,KAAKjD,SAAS,IACzC,EAAEyD,kBAAkB,YAAYR,2BAA2B,CAAC,EAAE;UAC9D,MAAM,IAAIxF,KAAK,CAAE,8CAA6CwF,2BAA2B,CAACS,IAAK,YAAWD,kBAAkB,CAACnB,WAAW,CAACoB,IAAK,EAAC,CAAC;QACpJ;QACA,OAAOD,kBAAkB;MAC7B,CAAC,MACI;QACD,IAAIR,2BAA2B,KAAKjD,SAAS,EAAE;UAC3C,MAAM,IAAIvC,KAAK,CAAE,8CAA6CwF,2BAA2B,CAACS,IAAK,qDAAoD,CAAC;QACxJ;QACA,OAAO,IAAI;MACf;IAAC;EACL;AACJ;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA,SAAS7D,oBAAoB,EAAEzB,mBAAmB,EAAEV,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}