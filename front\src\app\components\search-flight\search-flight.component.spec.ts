import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ReactiveFormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { of, throwError } from 'rxjs';

import { SearchFlightComponent } from './search-flight.component';
import { FlightService } from '../../services/flight.service';
import { FlightClass } from '../../models/flight-search.interface';

describe('SearchFlightComponent', () => {
  let component: SearchFlightComponent;
  let fixture: ComponentFixture<SearchFlightComponent>;
  let flightService: jasmine.SpyObj<FlightService>;
  let router: jasmine.SpyObj<Router>;

  const mockFlightResponse = {
    header: {
      requestId: '123',
      success: true,
      messages: []
    },
    body: {
      flights: []
    }
  };

  beforeEach(async () => {
    const flightServiceSpy = jasmine.createSpyObj('FlightService', [
      'searchOneWayFlights',
      'searchRoundTripFlights',
      'searchMulticityFlights'
    ]);
    const routerSpy = jasmine.createSpyObj('Router', ['navigate']);

    await TestBed.configureTestingModule({
      declarations: [SearchFlightComponent],
      imports: [ReactiveFormsModule, HttpClientTestingModule],
      providers: [
        { provide: FlightService, useValue: flightServiceSpy },
        { provide: Router, useValue: routerSpy }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(SearchFlightComponent);
    component = fixture.componentInstance;
    flightService = TestBed.inject(FlightService) as jasmine.SpyObj<FlightService>;
    router = TestBed.inject(Router) as jasmine.SpyObj<Router>;
  });

  beforeEach(() => {
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize form with default values', () => {
    expect(component.searchForm.get('tripType')?.value).toBe('oneWay');
    expect(component.searchForm.get('adults')?.value).toBe(1);
    expect(component.searchForm.get('children')?.value).toBe(0);
    expect(component.searchForm.get('infants')?.value).toBe(0);
    expect(component.searchForm.get('flightClass')?.value).toBe(FlightClass.ECONOMY);
    expect(component.searchForm.get('directFlightsOnly')?.value).toBe(false);
  });

  it('should require return date for round trip', () => {
    component.searchForm.patchValue({ tripType: 'roundTrip' });
    
    const returnDateControl = component.searchForm.get('returnDate');
    expect(returnDateControl?.hasError('required')).toBe(true);
  });

  it('should not require return date for one way', () => {
    component.searchForm.patchValue({ tripType: 'oneWay' });
    
    const returnDateControl = component.searchForm.get('returnDate');
    expect(returnDateControl?.hasError('required')).toBe(false);
  });

  it('should swap airports correctly', () => {
    component.searchForm.patchValue({
      departureLocation: 'IST',
      arrivalLocation: 'TUN'
    });

    component.swapAirports();

    expect(component.searchForm.get('departureLocation')?.value).toBe('TUN');
    expect(component.searchForm.get('arrivalLocation')?.value).toBe('IST');
  });

  it('should calculate total passengers correctly', () => {
    component.searchForm.patchValue({
      adults: 2,
      children: 1,
      infants: 1
    });

    expect(component.getTotalPassengers()).toBe(4);
  });

  it('should generate passenger text correctly', () => {
    component.searchForm.patchValue({
      adults: 2,
      children: 1,
      infants: 0
    });

    expect(component.getPassengerText()).toBe('2 Adults, 1 Child');
  });

  it('should submit one way search successfully', () => {
    flightService.searchOneWayFlights.and.returnValue(of(mockFlightResponse));
    
    component.searchForm.patchValue({
      tripType: 'oneWay',
      departureLocation: 'IST',
      arrivalLocation: 'TUN',
      departureDate: '2024-06-01',
      adults: 1,
      children: 0,
      infants: 0,
      flightClass: FlightClass.ECONOMY
    });

    component.onSubmit();

    expect(flightService.searchOneWayFlights).toHaveBeenCalled();
    expect(component.isLoading).toBe(false);
  });

  it('should submit round trip search successfully', () => {
    flightService.searchRoundTripFlights.and.returnValue(of(mockFlightResponse));
    
    component.searchForm.patchValue({
      tripType: 'roundTrip',
      departureLocation: 'IST',
      arrivalLocation: 'TUN',
      departureDate: '2024-06-01',
      returnDate: '2024-06-08',
      adults: 1,
      children: 0,
      infants: 0,
      flightClass: FlightClass.ECONOMY
    });

    component.onSubmit();

    expect(flightService.searchRoundTripFlights).toHaveBeenCalled();
    expect(component.isLoading).toBe(false);
  });

  it('should handle search error', () => {
    const errorMessage = 'Search failed';
    flightService.searchOneWayFlights.and.returnValue(throwError(() => new Error(errorMessage)));
    
    component.searchForm.patchValue({
      tripType: 'oneWay',
      departureLocation: 'IST',
      arrivalLocation: 'TUN',
      departureDate: '2024-06-01',
      adults: 1,
      children: 0,
      infants: 0,
      flightClass: FlightClass.ECONOMY
    });

    component.onSubmit();

    expect(component.errorMessage).toBe(errorMessage);
    expect(component.isLoading).toBe(false);
  });

  it('should not submit invalid form', () => {
    component.searchForm.patchValue({
      departureLocation: '',
      arrivalLocation: '',
      departureDate: ''
    });

    component.onSubmit();

    expect(flightService.searchOneWayFlights).not.toHaveBeenCalled();
    expect(component.isLoading).toBe(false);
  });

  it('should clear error message', () => {
    component.errorMessage = 'Test error';
    component.clearError();
    expect(component.errorMessage).toBe('');
  });

  it('should validate required fields', () => {
    const departureControl = component.searchForm.get('departureLocation');
    departureControl?.markAsTouched();
    
    expect(component.hasError('departureLocation', 'required')).toBe(true);
    expect(component.getErrorMessage('departureLocation')).toBe('Ce champ est requis');
  });

  it('should validate minimum length', () => {
    const departureControl = component.searchForm.get('departureLocation');
    departureControl?.setValue('AB');
    departureControl?.markAsTouched();
    
    expect(component.hasError('departureLocation', 'minlength')).toBe(true);
    expect(component.getErrorMessage('departureLocation')).toBe('Minimum 3 caractères requis');
  });
});
