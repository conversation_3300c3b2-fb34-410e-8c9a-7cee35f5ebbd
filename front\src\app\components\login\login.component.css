/* Variables CSS pour la cohérence */
:root {
  --primary-color: #2563eb;
  --primary-hover: #1d4ed8;
  --primary-light: #dbeafe;
  --secondary-color: #64748b;
  --success-color: #059669;
  --error-color: #dc2626;
  --error-bg: #fef2f2;
  --error-border: #fecaca;
  --warning-color: #d97706;
  --background-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --card-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --border-radius: 0.75rem;
  --transition: all 0.2s ease;
}

/* Container principal */
.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--background-gradient);
  padding: 1rem;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}

/* Carte de connexion */
.login-card {
  background: white;
  border-radius: var(--border-radius);
  box-shadow: var(--card-shadow);
  padding: 2.5rem;
  width: 100%;
  max-width: 420px;
  animation: slideUp 0.6s ease-out;
  position: relative;
  overflow: hidden;
}

.login-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--background-gradient);
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Header */
.login-header {
  text-align: center;
  margin-bottom: 2.5rem;
}

.logo-container {
  margin-bottom: 1rem;
}

.login-title {
  font-size: 2.25rem;
  font-weight: 800;
  color: var(--primary-color);
  margin: 0 0 0.25rem 0;
  letter-spacing: -0.025em;
}

.logo-subtitle {
  font-size: 0.875rem;
  color: var(--secondary-color);
  font-weight: 500;
}

.login-subtitle {
  color: var(--secondary-color);
  margin: 0;
  font-size: 1rem;
  font-weight: 400;
}

/* Formulaire */
.login-form {
  display: flex;
  flex-direction: column;
  gap: 1.75rem;
}

/* Groupes de champs */
.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-label {
  font-weight: 600;
  color: #374151;
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.label-icon {
  width: 1rem;
  height: 1rem;
  color: var(--secondary-color);
}

.form-input {
  padding: 0.875rem 1rem;
  border: 2px solid #e5e7eb;
  border-radius: 0.5rem;
  font-size: 1rem;
  transition: var(--transition);
  background-color: #f9fafb;
  font-weight: 400;
}

.form-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px var(--primary-light);
  background-color: white;
}

.form-input:hover:not(:focus) {
  border-color: #d1d5db;
}

.form-input.error {
  border-color: var(--error-color);
  background-color: var(--error-bg);
}

.form-input.error:focus {
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

/* Container pour le mot de passe */
.password-input-container {
  position: relative;
  display: flex;
  align-items: center;
}

.password-input {
  padding-right: 3.5rem;
}

.password-toggle {
  position: absolute;
  right: 1rem;
  background: none;
  border: none;
  cursor: pointer;
  color: var(--secondary-color);
  padding: 0.5rem;
  border-radius: 0.375rem;
  transition: var(--transition);
  display: flex;
  align-items: center;
  justify-content: center;
}

.password-toggle:hover {
  color: #374151;
  background-color: #f3f4f6;
}

.password-toggle:focus {
  outline: none;
  color: var(--primary-color);
  background-color: var(--primary-light);
}

.password-icon {
  width: 1.25rem;
  height: 1.25rem;
}

/* Messages d'erreur */
.error-message {
  background-color: var(--error-bg);
  border: 1px solid var(--error-border);
  color: var(--error-color);
  padding: 1rem;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  position: relative;
}

.error-icon {
  width: 1.25rem;
  height: 1.25rem;
  flex-shrink: 0;
  margin-top: 0.125rem;
}

.error-close {
  position: absolute;
  top: 0.75rem;
  right: 0.75rem;
  background: none;
  border: none;
  color: var(--error-color);
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 0.25rem;
  transition: var(--transition);
}

.error-close:hover {
  background-color: rgba(239, 68, 68, 0.1);
}

.error-close svg {
  width: 1rem;
  height: 1rem;
}

.field-error {
  color: var(--error-color);
  font-size: 0.75rem;
  margin-top: 0.25rem;
  font-weight: 500;
}

/* Bouton de connexion */
.login-button {
  background: var(--background-gradient);
  color: white;
  border: none;
  padding: 1rem 1.5rem;
  border-radius: 0.5rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition);
  position: relative;
  overflow: hidden;
  min-height: 3.25rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.login-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.login-button:active:not(:disabled) {
  transform: translateY(0);
}

.login-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.button-content,
.loading-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.button-icon {
  width: 1.25rem;
  height: 1.25rem;
}

.loading-spinner {
  width: 1.25rem;
  height: 1.25rem;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Footer */
.login-footer {
  margin-top: 2.5rem;
  text-align: center;
  border-top: 1px solid #e5e7eb;
  padding-top: 1.5rem;
}

.footer-text {
  color: var(--secondary-color);
  font-size: 0.875rem;
  margin: 0 0 0.75rem 0;
}

.footer-link {
  color: var(--primary-color);
  text-decoration: none;
  font-weight: 600;
  transition: var(--transition);
}

.footer-link:hover {
  color: var(--primary-hover);
  text-decoration: underline;
}

.version-info {
  font-size: 0.75rem;
  color: #9ca3af;
  font-weight: 500;
}

/* Responsive Design */
@media (max-width: 480px) {
  .login-container {
    padding: 0.75rem;
  }
  
  .login-card {
    padding: 2rem 1.5rem;
    border-radius: 0.5rem;
  }
  
  .login-title {
    font-size: 1.875rem;
  }
  
  .form-input {
    padding: 0.75rem;
    font-size: 0.875rem;
  }
  
  .login-button {
    padding: 0.875rem 1.25rem;
    font-size: 0.875rem;
  }
}

@media (max-width: 360px) {
  .login-card {
    padding: 1.5rem 1rem;
  }
  
  .login-title {
    font-size: 1.5rem;
  }
  
  .login-form {
    gap: 1.5rem;
  }
}

/* Accessibilité */
@media (prefers-reduced-motion: reduce) {
  .login-card {
    animation: none;
  }
  
  .loading-spinner {
    animation: none;
  }
  
  .login-button {
    transition: none;
  }
  
  * {
    transition: none !important;
  }
}

/* Mode sombre (optionnel) */
@media (prefers-color-scheme: dark) {
  .login-container {
    background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
  }
  
  .login-card {
    background: #1e293b;
    color: #f1f5f9;
    border: 1px solid #334155;
  }
  
  .login-title {
    color: #60a5fa;
  }
  
  .login-subtitle,
  .logo-subtitle {
    color: #cbd5e1;
  }
  
  .form-label {
    color: #e2e8f0;
  }
  
  .form-input {
    background-color: #334155;
    border-color: #475569;
    color: #f1f5f9;
  }
  
  .form-input:focus {
    background-color: #1e293b;
    border-color: #60a5fa;
  }
  
  .footer-text {
    color: #cbd5e1;
  }
  
  .version-info {
    color: #64748b;
  }
}
