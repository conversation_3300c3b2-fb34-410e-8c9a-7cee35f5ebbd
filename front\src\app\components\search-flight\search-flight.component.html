<div class="search-flight-container">
  <!-- Header Section -->
  <div class="search-header">
    <div class="search-title-section">
      <div class="search-icon">
        <svg viewBox="0 0 24 24" fill="currentColor">
          <circle cx="12" cy="12" r="10"/>
        </svg>
      </div>
      <div class="search-title-content">
        <h1 class="search-title">Search and Book Flights</h1>
        <p class="search-subtitle">We're bringing you a new level of comfort</p>
      </div>
    </div>
    
    <div class="latest-searches">
      <h2 class="latest-title">Latest Searches</h2>
      <p class="latest-subtitle">We're bringing you a new level of comfort</p>
    </div>
  </div>

  <!-- Main Search Form -->
  <div class="search-content">
    <div class="search-form-container">
      <!-- Message d'erreur global -->
      <div *ngIf="errorMessage" class="error-message" role="alert">
        <svg class="error-icon" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
        </svg>
        <span>{{ errorMessage }}</span>
        <button type="button" class="error-close" (click)="clearError()">
          <svg fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
          </svg>
        </button>
      </div>

      <form [formGroup]="searchForm" (ngSubmit)="onSubmit()" class="search-form" novalidate>
        
        <!-- Trip Type Tabs -->
        <div class="trip-type-tabs">
          <button type="button" 
                  class="tab-button" 
                  [class.active]="formControls['tripType'].value === 'oneWay'"
                  (click)="searchForm.patchValue({tripType: 'oneWay'})">
            One way
          </button>
          <button type="button" 
                  class="tab-button" 
                  [class.active]="formControls['tripType'].value === 'roundTrip'"
                  (click)="searchForm.patchValue({tripType: 'roundTrip'})">
            Round Trip
          </button>
          <button type="button" 
                  class="tab-button" 
                  [class.active]="formControls['tripType'].value === 'multiCity'"
                  (click)="searchForm.patchValue({tripType: 'multiCity'})">
            Multi-City/Stop-Overs
          </button>
        </div>

        <!-- Location Fields -->
        <div class="location-row">
          <div class="location-field">
            <label class="location-label">From</label>
            <div class="location-input-container">
              <svg class="location-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"/>
              </svg>
              <input type="text" 
                     formControlName="departureLocation"
                     class="location-input"
                     [class.error]="hasError('departureLocation', 'required') || hasError('departureLocation', 'minlength')"
                     placeholder="IST - Istanbul Airport">
              <button type="button" class="location-button">
                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
                </svg>
              </button>
            </div>
            <div *ngIf="formControls['departureLocation'].touched && formControls['departureLocation'].errors" class="field-error">
              {{ getErrorMessage('departureLocation') }}
            </div>
          </div>

          <!-- Swap Button -->
          <button type="button" class="swap-button" (click)="swapAirports()">
            <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"/>
            </svg>
          </button>

          <div class="location-field">
            <label class="location-label">To</label>
            <div class="location-input-container">
              <svg class="location-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"/>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"/>
              </svg>
              <input type="text" 
                     formControlName="arrivalLocation"
                     class="location-input"
                     [class.error]="hasError('arrivalLocation', 'required') || hasError('arrivalLocation', 'minlength')"
                     placeholder="TUN - Carthage Arpt">
              <button type="button" class="location-button">
                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
                </svg>
              </button>
            </div>
            <div *ngIf="formControls['arrivalLocation'].touched && formControls['arrivalLocation'].errors" class="field-error">
              {{ getErrorMessage('arrivalLocation') }}
            </div>
          </div>
        </div>

        <!-- Date Field -->
        <div class="date-row">
          <div class="date-field">
            <label class="date-label">From</label>
            <input type="date" 
                   formControlName="departureDate"
                   class="date-input"
                   [class.error]="hasError('departureDate', 'required')">
            <div *ngIf="formControls['departureDate'].touched && formControls['departureDate'].errors" class="field-error">
              {{ getErrorMessage('departureDate') }}
            </div>
          </div>

          <div class="date-field" *ngIf="formControls['tripType'].value === 'roundTrip'">
            <label class="date-label">To</label>
            <input type="date" 
                   formControlName="returnDate"
                   class="date-input"
                   [class.error]="hasError('returnDate', 'required')">
            <div *ngIf="formControls['returnDate'].touched && formControls['returnDate'].errors" class="field-error">
              {{ getErrorMessage('returnDate') }}
            </div>
          </div>
        </div>

        <!-- Passenger & Class Row -->
        <div class="passenger-class-row">
          <div class="passenger-field">
            <label class="passenger-label">Passenger & Class of travel</label>
            <div class="passenger-display">
              <div class="passenger-icons">
                <div class="passenger-group">
                  <svg class="passenger-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                  </svg>
                  <select formControlName="adults" class="passenger-select">
                    <option *ngFor="let count of passengerCounts.adults" [value]="count">{{ count }}</option>
                  </select>
                </div>
                <div class="passenger-group">
                  <svg class="passenger-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"/>
                  </svg>
                  <select formControlName="children" class="passenger-select">
                    <option *ngFor="let count of passengerCounts.children" [value]="count">{{ count }}</option>
                  </select>
                </div>
                <div class="passenger-group">
                  <svg class="passenger-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"/>
                  </svg>
                  <select formControlName="infants" class="passenger-select">
                    <option *ngFor="let count of passengerCounts.infants" [value]="count">{{ count }}</option>
                  </select>
                </div>
                <div class="class-group">
                  <svg class="class-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                  </svg>
                  <select formControlName="flightClass" class="class-select">
                    <option *ngFor="let flightClass of flightClasses" [value]="flightClass.value">{{ flightClass.label }}</option>
                  </select>
                </div>
              </div>
            </div>
          </div>

          <div class="airline-field">
            <label class="airline-label">Preferred Airline</label>
            <select formControlName="preferredAirline" class="airline-select">
              <option value="">Preferred Airline</option>
              <option *ngFor="let airline of preferredAirlines" [value]="airline">{{ airline }}</option>
            </select>
          </div>
        </div>

        <!-- Options Row -->
        <div class="options-row">
          <div class="option-group">
            <label class="option-label">Refundable fares</label>
            <select class="option-select">
              <option>--All--</option>
            </select>
          </div>

          <div class="option-group">
            <label class="option-label">Baggage</label>
            <select class="option-select">
              <option>--All--</option>
            </select>
          </div>

          <div class="option-group">
            <label class="option-label">Calendar</label>
            <div class="calendar-display">{{ getDaysBetweenDates() || '+/- 3 Days' }}</div>
          </div>
        </div>

        <!-- Search Button -->
        <button type="submit" 
                class="search-button"
                [disabled]="isLoading || searchForm.invalid"
                [class.loading]="isLoading">
          <span *ngIf="!isLoading">SEARCH NOW</span>
          <span *ngIf="isLoading" class="loading-content">
            <svg class="loading-spinner" viewBox="0 0 24 24">
              <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4" fill="none" opacity="0.25"></circle>
              <path fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" opacity="0.75"></path>
            </svg>
            Searching...
          </span>
        </button>
      </form>
    </div>

    <!-- Latest Searches Sidebar -->
    <div class="latest-searches-sidebar">
      <div class="latest-searches-content">
        <!-- Contenu des dernières recherches -->
        <p class="no-searches">No recent searches</p>
      </div>
    </div>
  </div>
</div>
