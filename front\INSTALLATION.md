# Installation et Configuration - Composant Login Paximum

## Prérequis

- Node.js 16+ 
- Angular CLI 16+
- Backend Spring Boot en cours d'exécution sur le port 8080

## Installation

### 1. Installation des dépendances

```bash
cd front
npm install
```

### 2. Configuration de l'environnement

Modifiez le fichier `src/environments/environment.ts` pour pointer vers votre backend :

```typescript
export const environment = {
  production: false,
  apiUrl: 'http://localhost:8080', // URL de votre backend Spring Boot
  authEndpoint: '/auth/login'
};
```

### 3. Configuration CORS (Backend Spring Boot)

Ajoutez cette configuration dans votre backend Spring Boot :

```java
@Configuration
@EnableWebMvc
public class WebConfig implements WebMvcConfigurer {

    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/**")
                .allowedOrigins("http://localhost:4200")
                .allowedMethods("GET", "POST", "PUT", "DELETE", "OPTIONS")
                .allowedHeaders("*")
                .allowCredentials(true);
    }
}
```

## Démarrage

### 1. Démarrer le backend Spring Boot

```bash
# Dans le répertoire de votre backend
./mvnw spring-boot:run
```

### 2. Démarrer l'application Angular

```bash
# Dans le répertoire front
ng serve
```

L'application sera accessible sur `http://localhost:4200`

## Structure du projet

```
front/src/app/
├── components/
│   ├── login/                 # Composant de connexion
│   │   ├── login.component.ts
│   │   ├── login.component.html
│   │   ├── login.component.css
│   │   ├── login.component.spec.ts
│   │   └── README.md
│   └── dashboard/             # Composant dashboard (exemple)
│       ├── dashboard.component.ts
│       ├── dashboard.component.html
│       └── dashboard.component.css
├── models/                    # Interfaces TypeScript
│   ├── auth-request.interface.ts
│   └── auth-response.interface.ts
├── services/                  # Services Angular
│   └── auth.service.ts
├── guards/                    # Guards de route
│   └── auth.guard.ts
├── interceptors/              # Intercepteurs HTTP
│   └── http.interceptor.ts
└── environments/              # Configuration d'environnement
    ├── environment.ts
    └── environment.prod.ts
```

## Utilisation

### 1. Accès à l'application

1. Ouvrez `http://localhost:4200`
2. Vous serez automatiquement redirigé vers `/login`
3. Entrez vos identifiants :
   - Code Agence (minimum 2 caractères)
   - Code Utilisateur (minimum 2 caractères)  
   - Mot de passe (minimum 6 caractères)
4. Cliquez sur "Se connecter"

### 2. Après connexion réussie

- Redirection automatique vers `/dashboard`
- Affichage des informations utilisateur
- Token stocké dans localStorage
- Possibilité de déconnexion

### 3. Gestion des erreurs

Le composant gère automatiquement :
- Erreurs de validation de formulaire
- Erreurs de connexion réseau
- Erreurs d'authentification du backend
- Messages d'erreur personnalisés

## Tests

### Exécuter les tests unitaires

```bash
ng test
```

### Exécuter les tests e2e

```bash
ng e2e
```

## Build de production

### 1. Build

```bash
ng build --configuration production
```

### 2. Configuration production

Modifiez `src/environments/environment.prod.ts` :

```typescript
export const environment = {
  production: true,
  apiUrl: 'https://your-production-api.com',
  authEndpoint: '/auth/login'
};
```

## Fonctionnalités

### ✅ Implémentées

- [x] Composant login responsable
- [x] Validation de formulaire en temps réel
- [x] Gestion des erreurs d'authentification
- [x] Stockage sécurisé du token
- [x] Guard d'authentification
- [x] Intercepteur HTTP
- [x] Design responsive (mobile/tablet/desktop)
- [x] Accessibilité (WCAG)
- [x] Mode sombre automatique
- [x] Tests unitaires
- [x] Documentation complète

### 🔄 À implémenter (optionnel)

- [ ] Remember me functionality
- [ ] Forgot password
- [ ] Multi-factor authentication
- [ ] Session timeout warning
- [ ] Internationalization (i18n)

## Dépannage

### Erreur CORS

Si vous rencontrez des erreurs CORS :

1. Vérifiez la configuration CORS du backend
2. Assurez-vous que l'URL du backend est correcte
3. Vérifiez que le backend est démarré

### Erreur de connexion

Si l'authentification échoue :

1. Vérifiez les logs du backend
2. Vérifiez les identifiants
3. Vérifiez la structure de la réponse API

### Problèmes de build

Si le build échoue :

1. Supprimez `node_modules` et `package-lock.json`
2. Exécutez `npm install`
3. Relancez `ng build`

## Support

Pour toute question ou problème :

1. Consultez la documentation des composants
2. Vérifiez les logs de la console
3. Consultez les tests unitaires pour des exemples d'utilisation

## Versions

- Angular: 16.2.x
- Node.js: 16+
- TypeScript: 5.1.x
